#ifndef SERVO_CONTROLLER_H
#define SERVO_CONTROLLER_H

#include <Arduino.h>
#include <Servo.h>

// 舵机控制器类
class ServoController {
private:
    static const int NUM_CHANNELS = 6;
    Servo* servos;
    const int* servoPins;
    int servoAngles[NUM_CHANNELS];
    
public:
    // 构造函数
    ServoController(Servo* servoArray, const int pins[NUM_CHANNELS]);
    
    // 初始化舵机
    void begin();
    
    // 设置单个舵机角度
    bool setServo(int channel, int angle);
    
    // 设置所有舵机角度
    bool setAllServos(const int angles[NUM_CHANNELS]);
    
    // 停止所有舵机 (设为中位90度)
    void stopAll();
    
    // 获取舵机角度
    int getServo(int channel);
    
    // 打印状态
    void printStatus();
    
    // 验证角度范围
    bool isValidAngle(int angle);
    
    // 验证通道号
    bool isValidChannel(int channel);
    
    // 分离所有舵机
    void detachAll();
    
    // 重新连接所有舵机
    void attachAll();
};

#endif
