{"files": [{"path": "src/main.cpp", "bookmarks": [{"line": 17, "column": 24, "label": ""}]}, {"path": "src/serial_parser.cpp", "bookmarks": [{"line": 114, "column": 25, "label": ""}, {"line": 182, "column": 54, "label": ""}, {"line": 214, "column": 35, "label": ""}, {"line": 354, "column": 33, "label": ""}, {"line": 400, "column": 33, "label": ""}]}, {"path": "examples/protocol_test.py", "bookmarks": [{"line": 54, "column": 35, "label": ""}, {"line": 211, "column": 27, "label": ""}, {"line": 265, "column": 22, "label": ""}]}, {"path": "src/servo_controller.cpp", "bookmarks": [{"line": 23, "column": 24, "label": ""}, {"line": 29, "column": 35, "label": ""}]}]}