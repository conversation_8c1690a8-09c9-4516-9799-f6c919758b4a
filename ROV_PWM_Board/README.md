# ROV PWM Board

这是一个基于Arduino Nano的6通道舵机控制板，使用Arduino Servo库生成标准的舵机PWM信号。通过串口协议接收命令来控制6个舵机输出，支持标准的二进制通信协议，具有CRC8校验功能。

## 硬件配置

- **微控制器**: <PERSON><PERSON><PERSON><PERSON> (ATmega328P)
- **舵机控制引脚**: 3, 5, 6, 9, 10, 11
- **串口波特率**: 115200
- **PWM信号**: 标准舵机PWM (50Hz, 1-2ms脉宽)
- **角度范围**: 0-180度 (直接控制舵机角度)
- **控制库**: Arduino Servo库

## 引脚映射

| 通道 | Arduino引脚 | 描述 |
|------|-------------|------|
| 0    | 3           | 舵机控制通道0 |
| 1    | 5           | 舵机控制通道1 |
| 2    | 6           | 舵机控制通道2 |
| 3    | 9           | 舵机控制通道3 |
| 4    | 10          | 舵机控制通道4 |
| 5    | 11          | 舵机控制通道5 |

## 通信协议

本项目实现了基于`COMMUNICATION_PROTOCOL.md`的二进制通信协议，具有以下特点：

- **帧格式**: 固定的请求/应答帧结构
- **校验**: CRC8校验确保数据完整性
- **角度控制**: 支持0-180度角度输入，自动转换为PWM值

### 协议帧格式

#### 请求帧 (上位机→下位机)
```
+--------+--------+--------+--------+--------+
| 帧头   | 命令码 | 数据长度| 数据   | 校验码 |
| 0xAA   | 1字节  | 1字节   | N字节  | 1字节  |
+--------+--------+--------+--------+--------+
```

#### 应答帧 (下位机→上位机)
```
+--------+--------+--------+--------+--------+
| 帧头   | 状态码 | 数据长度| 数据   | 校验码 |
| 0x55   | 1字节  | 1字节   | N字节  | 1字节  |
+--------+--------+--------+--------+--------+
```

### 支持的命令

#### 0x10 - 设置单路PWM
**请求**: `AA 10 02 [通道] [角度] CRC`
**应答**: `55 00 00 CRC`

- **通道**: 0-5 (对应6路PWM)
- **角度**: 0-180 (度)

**示例**: 设置通道0为90度
```
请求: AA 10 02 00 5A [CRC]
应答: 55 00 00 [CRC]
```

#### 0x11 - 设置所有PWM
**请求**: `AA 11 06 [角度1] [角度2] [角度3] [角度4] [角度5] [角度6] CRC`
**应答**: `55 00 00 CRC`

**示例**: 设置所有通道
```
请求: AA 11 06 5A 5A 5A 5A 5A 5A [CRC]
应答: 55 00 00 [CRC]
```

### 状态码

- **0x00**: 成功
- **0xFF**: 错误 (参数无效、CRC校验失败等)

## 编译和上传

### 使用PlatformIO
```bash
# 编译
pio run

# 上传到Arduino Nano
pio run --target upload

# 打开串口监视器
pio device monitor
```

### 使用Arduino IDE
1. 打开 `src/main.cpp`
2. 选择板子: Arduino Nano
3. 选择处理器: ATmega328P (Old Bootloader)
4. 编译并上传

## 测试

运行单元测试：
```bash
pio test
```

## 使用示例

### Python协议控制示例
```python
from protocol_test import ROVProtocolController

# 连接到设备
controller = ROVProtocolController('/dev/ttyUSB0')
controller.connect()

# 设置单路PWM
controller.set_single_pwm(0, 90)   # 通道0设为90度
controller.set_single_pwm(1, 45)   # 通道1设为45度

# 设置所有通道
angles = [0, 30, 60, 90, 120, 150]
controller.set_all_pwm(angles)

controller.disconnect()
```

### 协议测试
运行协议测试脚本：
```bash
python examples/protocol_test.py
```

该脚本会自动测试：
- 单路PWM设置
- 所有PWM设置
- 渐变效果演示
- 错误处理验证

## 故障排除

1. **串口连接问题**
   - 检查USB线缆连接
   - 确认波特率设置为115200
   - 检查串口权限（Linux/Mac）

2. **PWM输出无效果**
   - 检查引脚连接
   - 确认PWM值范围（0-255）
   - 使用万用表或示波器测量输出

3. **命令无响应**
   - 检查命令格式是否正确
   - 确认以换行符结尾
   - 发送HELP命令查看可用命令

## 扩展功能

可以在代码中添加以下功能：
- PWM频率调节
- 渐变控制
- 预设模式
- 状态LED指示
- 看门狗保护
- EEPROM设置保存
