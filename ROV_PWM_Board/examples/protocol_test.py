#!/usr/bin/env python3
"""
ROV PWM Board Protocol Test
测试基于COMMUNICATION_PROTOCOL.md的通信协议
"""

import serial
import time
import struct
from serial_port import SerialPort

from colorama import Fore,init,Style,Back
init(autoreset=True)

class ROVProtocolController:
    def __init__(self, port: str, baudrate: int = 115200):
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        
        # 协议常量
        self.FRAME_HEADER_REQUEST = 0xAA
        self.FRAME_HEADER_RESPONSE = 0x55
        self.CMD_SET_SINGLE_PWM = 0x10
        self.CMD_SET_ALL_PWM = 0x11
        self.STATUS_OK = 0x00
        self.STATUS_ERROR = 0xFF
        
        self.serial_port=None
    
    def connect(self):
        """初始化串口连接"""
        if SerialPort:
            try:
                self.serial_port = SerialPort(port=self.port, baudrate=115200)

                if self.serial_port.open(receive_callback=self.handle_serial_data):
                    print(Fore.CYAN+f"串口连接成功,串口接收回调已设置")
                    
                    return True
                
                else:
                    self.serial_port = None
                    print(Fore.RED+"串口连接失败")
                    
            except Exception as e:
                print(f"串口连接失败: {e}")
                self.serial_port = None
        else:
            print("使用模拟数据模式")
            
        return False
            
    def handle_serial_data(self, data: bytes):
        # [ ]TEST------------------------
        print(Fore.GREEN+f"\n接收HEX: {data.hex().upper()}")
        print(Fore.GREEN+f"\tTEXT: {str(data)}\n")
            
    
    def disconnect(self):
        """断开连接"""
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            print("已断开连接")
    
    def calculate_crc8(self, data):
        """计算CRC8校验码"""
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc
    
    def send_frame(self, command, data=None):
        """发送协议帧"""
        if not self.serial_conn or not self.serial_conn.is_open:
            print("设备未连接")
            return None
        
        # 构建帧
        frame = bytearray()
        frame.append(self.FRAME_HEADER_REQUEST)  # 帧头
        frame.append(command)                    # 命令码
        
        if data is None:
            data = bytearray()
        
        frame.append(len(data))                  # 数据长度
        frame.extend(data)                       # 数据
        
        # 计算CRC
        crc = self.calculate_crc8(frame)
        frame.append(crc)
        
        # 发送帧
        self.serial_conn.write(frame)
        
        # 打印发送的帧（调试用）
        print(f"发送: {' '.join([f'{b:02X}' for b in frame])}")
        
        # 等待应答
        return self.receive_response()
    
    def receive_response(self,data:bytes):
        """接收应答帧"""
        try:
            # print(Fore.GREEN+f"\n接收HEX: {data.hex().upper()}")
            # print(Fore.GREEN+f"\tTEXT: {str(data)}\n")
            
            # 读取帧头``
            header = self.serial_conn.read(1)
            print("header:"+header.hex().upper())
        
            if not header or header[0] != self.FRAME_HEADER_RESPONSE:
                print("应答帧头错误")
                # return None
            
            # 读取状态码
            status = self.serial_conn.read(1)
            print("status:"+status.hex().upper())
            
            if not status:
                print("读取状态码超时")
                # return None
            
            # 读取数据长度
            length = self.serial_conn.read(1)
            print("length:"+length.hex().upper())
            
            if not length:
                print("读取数据长度超时")
                # return None
            
            data_len = length[0]
            
            # 读取数据
            data = bytearray()
            if data_len > 0:
                data = self.serial_conn.read(data_len)
                if len(data) != data_len:
                    print("读取数据超时")
                    return None
            
            # 读取CRC
            crc = self.serial_conn.read(1)
            if not crc:
                print("读取CRC超时")
                return None
            
            # 验证CRC
            frame_without_crc = bytearray([self.FRAME_HEADER_RESPONSE, status[0], data_len])
            frame_without_crc.extend(data)
            calculated_crc = self.calculate_crc8(frame_without_crc)
            
            if calculated_crc != crc[0]:
                print(f"CRC校验失败: 计算={calculated_crc:02X}, 接收={crc[0]:02X}")
                return None
            
            # 打印接收的帧（调试用）
            response_frame = frame_without_crc + bytearray([crc[0]])
            print(f"接收: {' '.join([f'{b:02X}' for b in response_frame])}")
            
            return {
                'status': status[0],
                'data': data,
                'success': status[0] == self.STATUS_OK
            }
            
        except Exception as e:
            print(f"接收应答失败: {e}")
            return None
    
    def set_single_pwm(self, channel, angle):
        """设置单路PWM (0x10命令)"""
        if not (0 <= channel <= 5):
            print("通道号必须在0-5之间")
            return False
        
        if not (0 <= angle <= 180):
            print("角度必须在0-180之间")
            return False
        
        data = bytearray([channel, angle])
        # response = self.send_frame(self.CMD_SET_SINGLE_PWM, data)
        response = self.send_command(self.CMD_SET_SINGLE_PWM, data)
        
        if response:
            print(f"成功设置通道{channel}为{angle}度")
            return True
        else:
            print(f"设置通道{channel}失败")
            return False
    
    def set_all_pwm(self, angles):
        """设置所有PWM (0x11命令)"""
        if len(angles) != 6:
            print("必须提供6个角度值")
            return False
        
        for i, angle in enumerate(angles):
            if not (0 <= angle <= 180):
                print(f"通道{i}的角度{angle}超出范围(0-180)")
                return False
        
        data = bytearray(angles)
        # response = self.send_frame(self.CMD_SET_ALL_PWM, data)
        response=self.send_command(0x11,data)
        
        if response:
            print(f"成功设置所有通道: {angles}")
            return True
        else:
            print("设置所有通道失败")
            return False
        
    def send_command(self, cmd, data=b''):
        #[ ] """发送命令到ESP32"""
        
        if not self.serial_port:
            return None

        try:
            # 构建命令帧
            frame = bytearray([0xAA, cmd, len(data)])
            frame.extend(data)
            crc = self.calculate_crc8(frame[1:])
            frame.append(crc)

            # 转换为bytes类型
            frame_bytes = bytes(frame)

            # 记录发送时间
            self.serial_send_time = time.time() * 1000  # 转换为毫秒

            # 发送命令
            success = self.serial_port.send_hex(frame_bytes)
            if success:
                print(f"\n已发送命令: {frame_bytes.hex().upper()}")
                pass
            else:
                print(f"\n发送命令失败: {frame_bytes.hex().upper()}")
                
                time.sleep(0.1)  # 等待10ms，减少延迟
                
            return True
        except Exception as e:
            print(f"发送命令失败: {e}")
            return False
        
def test_protocol():
    """测试协议通信"""
    port = "/dev/tty.wchusbserial140"
    controller = ROVProtocolController(port)
    
    if not controller.connect():
        return
    
    try:
        print("\n=== 协议测试开始 ===")
        
        # 测试1: 设置单路PWM
        print("\n测试1: 设置单路PWM")
        controller.set_single_pwm(0, 0)   # 通道0设为30度
        time.sleep(1)
        controller.set_single_pwm(0, 90)   # 通道0设为30度
        time.sleep(1)
        controller.set_single_pwm(0, 180)   # 通道0设为30度
        time.sleep(1)
        
        # controller.set_single_pwm(1, 45)   # 通道1设为45度
        # time.sleep(0.5)
        
        # controller.set_single_pwm(2, 135)  # 通道2设为135度
        # time.sleep(0.5)
        
        # 测试2: 设置所有PWM
        # print("\n测试2: 设置所有PWM")
        # angles = [0, 30, 60, 90, 120, 150]
        # controller.set_all_pwm(angles)
        # time.sleep(3)
        
        
        # # 测试3: 渐变效果
        # print("\n测试3: 渐变效果")
        # for step in range(0, 181, 10):
        #     angles = [step] * 6
        #     controller.set_all_pwm(angles)
        #     time.sleep(0.1)
        
        # # 测试4: 错误处理
        # print("\n测试4: 错误处理")
        # controller.set_single_pwm(6, 90)   # 无效通道
        # controller.set_single_pwm(0, 200)  # 无效角度
        
        # # 复位到中位
        # print("\n复位到中位")
        # controller.set_all_pwm([90] * 6)
        
        print("\n=== 协议测试完成 ===")
        
    finally:
        controller.disconnect()

if __name__ == "__main__":
    test_protocol()
