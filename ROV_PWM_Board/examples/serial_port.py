import serial
import serial.tools.list_ports
import time
import threading
from typing import Union, List, Optional, Callable
from colorama import Fore,init,Style,Back
init(autoreset=True)

class SerialPort:
    """
    Ubuntu串口通信类，支持十六进制数据发送和独立线程接收
    """
    
    def __init__(self, port: str = None, baudrate: int = 9600, timeout: float = 1):
        """
        初始化串口
        :param port: 串口设备路径，如'/dev/ttyUSB0'，如果为None则自动选择第一个可用串口
        :param baudrate: 波特率
        :param timeout: 超时时间(秒)
        """
        self.serial_port = None
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.is_open = False
        self.receive_thread = None
        self.receive_callback = None
        self._stop_event = threading.Event()

    def open(self, receive_callback: Callable[[bytes], None] = None) -> bool:
        """
        打开串口并启动接收线程
        :param receive_callback: 接收数据回调函数，接收bytes参数
        :return: 成功返回True，失败返回False
        """
        try:
            # 重启ESP32
            print(Fore.YELLOW+"restart esp32...")
            self.serial_port = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            self.serial_port.setDTR(False)  # 拉低 DTR（复位）
            time.sleep(0.01)
            self.serial_port.setDTR(True)
            # time.sleep(0.2)
            # self.serial_port.close()
            # time.sleep(0.3)
            
            print(Fore.YELLOW+f"open esp32 serial:{self.port}")
            self.serial_port = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            if self.serial_port.is_open:
                self.is_open = True
                self.receive_callback = receive_callback
                
                # 启动接收线程
                self._stop_event.clear()
                self.receive_thread = threading.Thread(
                    target=self._receive_data_thread,
                    daemon=True
                )
                self.receive_thread.start()
                
                print(Fore.GREEN+f"串口 {self.port} 打开成功，波特率 {self.baudrate}")
                return True
            else:
                print(Fore.RED+f"串口 {self.port} 打开失败")
                return False
                
        except Exception as e:
            print(Fore.RED+f"打开串口时出错: {e}")
            return False

    def close(self):
        """
        关闭串口并停止接收线程
        """
        if self.is_open:
            # 停止接收线程
            self._stop_event.set()
            if self.receive_thread and self.receive_thread.is_alive():
                self.receive_thread.join(timeout=1)
            
            # 关闭串口
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
            
            self.is_open = False
            print(f"串口 {self.port} 已关闭")

    def write(self, data):
        # print(Fore.YELLOW+f"发送: {data.hex().upper()}")
        self.serial_port.write(data)
        
    def send_hex(self, hex_data: Union[str, List[int], bytes]) -> bool:
        """
        发送十六进制数据
        :param hex_data: 可以是以下格式:
                        - 字符串: "A0 12 34" 或 "A01234"
                        - 列表: [0xA0, 0x12, 0x34]
                        - bytes: b'\xA0\x12\x34'
        :return: 成功返回True，失败返回False
        """
        if not self.is_open:
            print("串口未打开!")
            return False
            
        try:
            # 处理不同类型的输入
            if isinstance(hex_data, str):
                # 去除字符串中的空格
                hex_str = hex_data.replace(" ", "")
                # 确保长度为偶数
                if len(hex_str) % 2 != 0:
                    print("十六进制字符串长度必须为偶数!")
                    return False
                # 转换为bytes
                data = bytes.fromhex(hex_str)
            elif isinstance(hex_data, list):
                data = bytes(hex_data)
            elif isinstance(hex_data, bytes):
                data = hex_data
            else:
                print("不支持的十六进制数据类型!")
                return False
                
            self.write(data)
            return True
            
        except Exception as e:
            print(f"发送数据时出错: @serial_port.py 128 {e}")
            return False

    def _receive_data_thread(self):
        """
        接收数据的线程函数
        """
        while not self._stop_event.is_set():
            try:
                # 检查是否有数据可读
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        hex_str = data.hex(' ').upper()
                        # print(f"接收数据: {hex_str}")
                        if self.receive_callback:
                            self.receive_callback(data)
                else:
                    # 短暂休眠以避免CPU占用过高
                    time.sleep(0.001)
                    
            except Exception as e:
                print(f"接收线程出错: {e}")
                break

    @staticmethod
    def list_available_ports() -> List[str]:
        """
        列出所有可用的串口
        :return: 可用串口列表
        """
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]

    def __del__(self):
        """析构函数，确保串口关闭"""
        self.close()
