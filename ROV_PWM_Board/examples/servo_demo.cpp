#include <Arduino.h>
#include <Servo.h>
#include "servo_controller.h"
#include "serial_parser.h"

// PWM输出引脚定义
const int PWM_PINS[6] = {3, 5, 6, 9, 10, 11};

// 创建6个Servo对象
Servo servos[6];

// 创建舵机控制器和串口解析器对象
ServoController servoController(servos, PWM_PINS);
SerialParser serialParser(&servoController);

// 演示模式变量
bool demoMode = false;
unsigned long lastDemoUpdate = 0;
int demoStep = 0;
int demoDirection = 1;

void setup() {
  // 初始化舵机控制器
  servoController.begin();
  
  // 初始化串口解析器
  serialParser.begin(115200);
  
  Serial.println("Servo Demo Mode Available!");
  Serial.println("Send 'DEMO' to start/stop demo mode");
  Serial.println("Send 'SWEEP' for sweep demo");
  Serial.println("Send 'WAVE' for wave demo");
}

void loop() {
  // 处理串口命令
  serialParser.update();
  
  // 检查是否有演示命令
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toUpperCase();
    
    if (command == "DEMO") {
      demoMode = !demoMode;
      if (demoMode) {
        Serial.println("Demo mode started - synchronized movement");
        demoStep = 0;
        lastDemoUpdate = millis();
      } else {
        Serial.println("Demo mode stopped");
        servoController.stopAll();
      }
    }
    else if (command == "SWEEP") {
      Serial.println("Running sweep demo...");
      sweepDemo();
    }
    else if (command == "WAVE") {
      Serial.println("Running wave demo...");
      waveDemo();
    }
    else if (command == "CENTER") {
      Serial.println("Centering all servos...");
      servoController.stopAll();
    }
    else if (command == "STATUS") {
      servoController.printStatus();
    }
  }
  
  // 运行演示模式
  if (demoMode) {
    runDemo();
  }
}

void runDemo() {
  unsigned long currentTime = millis();
  
  // 每100ms更新一次演示效果
  if (currentTime - lastDemoUpdate >= 100) {
    lastDemoUpdate = currentTime;
    
    // 创建同步摆动效果
    int baseAngle = 90 + (sin(demoStep * 0.1) * 45); // 45-135度范围
    
    // 所有舵机同步运动
    int angles[6];
    for (int i = 0; i < 6; i++) {
      angles[i] = baseAngle;
    }
    
    servoController.setAllServos(angles);
    
    demoStep++;
    
    // 每2秒打印一次状态
    if (demoStep % 20 == 0) {
      servoController.printStatus();
    }
  }
}

void sweepDemo() {
  Serial.println("Sweeping all servos from 0 to 180 degrees...");
  
  // 从0度扫到180度
  for (int angle = 0; angle <= 180; angle += 5) {
    int angles[6];
    for (int i = 0; i < 6; i++) {
      angles[i] = angle;
    }
    servoController.setAllServos(angles);
    delay(50);
  }
  
  // 从180度扫回0度
  for (int angle = 180; angle >= 0; angle -= 5) {
    int angles[6];
    for (int i = 0; i < 6; i++) {
      angles[i] = angle;
    }
    servoController.setAllServos(angles);
    delay(50);
  }
  
  // 回到中位
  servoController.stopAll();
  Serial.println("Sweep demo completed");
}

void waveDemo() {
  Serial.println("Running wave effect...");
  
  for (int cycle = 0; cycle < 3; cycle++) {
    for (int step = 0; step < 360; step += 10) {
      int angles[6];
      
      // 为每个舵机创建不同的相位偏移
      for (int i = 0; i < 6; i++) {
        float phaseOffset = i * 60; // 60度相位差
        float angle = 90 + sin((step + phaseOffset) * PI / 180) * 45;
        angles[i] = constrain((int)angle, 0, 180);
      }
      
      servoController.setAllServos(angles);
      delay(50);
    }
  }
  
  // 回到中位
  servoController.stopAll();
  Serial.println("Wave demo completed");
}
