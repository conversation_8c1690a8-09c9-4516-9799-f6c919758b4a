#!/usr/bin/env python3
"""
ROV Servo Board Test Script
测试基于Servo库的舵机控制功能
"""

import time
from protocol_test import ROVProtocolController

def test_servo_functions():
    """测试舵机功能"""
    port = "/dev/tty.wchusbserial140"  # 根据实际情况修改
    controller = ROVProtocolController(port)
    
    if not controller.connect():
        return
    
    try:
        print("\n=== 舵机功能测试 ===")
        
        # 测试1: 中位测试
        print("\n测试1: 所有舵机设为中位(90度)")
        controller.set_all_pwm([90] * 6)
        time.sleep(2)
        
        # 测试2: 最小角度测试
        print("\n测试2: 所有舵机设为最小角度(0度)")
        controller.set_all_pwm([0] * 6)
        time.sleep(2)
        
        # 测试3: 最大角度测试
        print("\n测试3: 所有舵机设为最大角度(180度)")
        controller.set_all_pwm([180] * 6)
        time.sleep(2)
        
        # 测试4: 单个舵机测试
        print("\n测试4: 单个舵机测试")
        # 先回到中位
        controller.set_all_pwm([90] * 6)
        time.sleep(1)
        
        for i in range(6):
            print(f"  测试通道{i}")
            controller.set_single_pwm(i, 45)   # 45度
            time.sleep(0.5)
            controller.set_single_pwm(i, 135)  # 135度
            time.sleep(0.5)
            controller.set_single_pwm(i, 90)   # 回到中位
            time.sleep(0.5)
        
        # 测试5: 扫描测试
        print("\n测试5: 扫描测试 (0-180度)")
        for angle in range(0, 181, 10):
            controller.set_all_pwm([angle] * 6)
            time.sleep(0.1)
        
        for angle in range(180, -1, -10):
            controller.set_all_pwm([angle] * 6)
            time.sleep(0.1)
        
        # 测试6: 波浪效果
        print("\n测试6: 波浪效果")
        import math
        
        for step in range(0, 360, 10):
            angles = []
            for i in range(6):
                phase_offset = i * 60  # 60度相位差
                angle = 90 + math.sin(math.radians(step + phase_offset)) * 45
                angles.append(int(max(0, min(180, angle))))
            
            controller.set_all_pwm(angles)
            time.sleep(0.1)
        
        # 测试7: 随机位置测试
        print("\n测试7: 随机位置测试")
        import random
        
        for _ in range(10):
            angles = [random.randint(0, 180) for _ in range(6)]
            print(f"  设置角度: {angles}")
            controller.set_all_pwm(angles)
            time.sleep(1)
        
        # 回到中位
        print("\n回到中位位置")
        controller.set_all_pwm([90] * 6)
        
        print("\n=== 舵机功能测试完成 ===")
        
    finally:
        controller.disconnect()

def test_servo_precision():
    """测试舵机精度"""
    port = "/dev/tty.wchusbserial140"
    controller = ROVProtocolController(port)
    
    if not controller.connect():
        return
    
    try:
        print("\n=== 舵机精度测试 ===")
        
        # 测试每度精度
        print("测试每度精度 (通道0)")
        for angle in range(0, 181, 1):
            controller.set_single_pwm(0, angle)
            time.sleep(0.05)  # 50ms延迟
        
        print("精度测试完成")
        
        # 回到中位
        controller.set_single_pwm(0, 90)
        
    finally:
        controller.disconnect()

def interactive_servo_control():
    """交互式舵机控制"""
    port = "/dev/tty.wchusbserial140"
    controller = ROVProtocolController(port)
    
    if not controller.connect():
        return
    
    try:
        print("\n=== 交互式舵机控制 ===")
        print("命令:")
        print("  set <channel> <angle>  - 设置单个舵机角度")
        print("  all <a1> <a2> ... <a6> - 设置所有舵机角度")
        print("  center                 - 所有舵机回中位")
        print("  sweep                  - 扫描演示")
        print("  quit                   - 退出")
        
        while True:
            try:
                cmd = input("\nServo> ").strip().split()
                if not cmd:
                    continue
                
                if cmd[0] == "quit":
                    break
                elif cmd[0] == "set" and len(cmd) == 3:
                    channel = int(cmd[1])
                    angle = int(cmd[2])
                    if 0 <= channel <= 5 and 0 <= angle <= 180:
                        controller.set_single_pwm(channel, angle)
                        print(f"通道{channel}设置为{angle}度")
                    else:
                        print("参数错误: 通道(0-5), 角度(0-180)")
                elif cmd[0] == "all" and len(cmd) == 7:
                    angles = [int(x) for x in cmd[1:]]
                    if all(0 <= a <= 180 for a in angles):
                        controller.set_all_pwm(angles)
                        print(f"所有舵机设置为: {angles}")
                    else:
                        print("角度必须在0-180之间")
                elif cmd[0] == "center":
                    controller.set_all_pwm([90] * 6)
                    print("所有舵机回到中位")
                elif cmd[0] == "sweep":
                    print("执行扫描...")
                    for angle in range(0, 181, 5):
                        controller.set_all_pwm([angle] * 6)
                        time.sleep(0.05)
                    for angle in range(180, -1, -5):
                        controller.set_all_pwm([angle] * 6)
                        time.sleep(0.05)
                    controller.set_all_pwm([90] * 6)
                    print("扫描完成")
                else:
                    print("无效命令")
            
            except KeyboardInterrupt:
                break
            except ValueError:
                print("参数错误，请检查数值格式")
            except Exception as e:
                print(f"错误: {e}")
    
    finally:
        controller.disconnect()

def main():
    print("ROV 舵机测试程序")
    print("1. 基本功能测试")
    print("2. 精度测试")
    print("3. 交互式控制")
    
    choice = input("请选择测试模式 (1-3): ").strip()
    
    if choice == "1":
        test_servo_functions()
    elif choice == "2":
        test_servo_precision()
    elif choice == "3":
        interactive_servo_control()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
