#include "servo_controller.h"

ServoController::ServoController(Servo* servoArray, const int pins[NUM_CHANNELS]) {
    servos = servoArray;
    servoPins = pins;
    
    // 初始化角度数组
    for (int i = 0; i < NUM_CHANNELS; i++) {
        servoAngles[i] = 1500; // 默认中位
    }
}

void ServoController::begin() {
    // 连接所有舵机到对应引脚
    for (int i = 0; i < NUM_CHANNELS; i++) {
        servos[i].attach(servoPins[i]);
        servos[i].write(1500); // 设置为中位
        servoAngles[i] = 1500;
    }
    
    // Serial.println("Servo Controller initialized - all servos at 90 degrees");
}

bool ServoController::setServo(int channel, int angle) {
    servos[channel].write(angle);
    servoAngles[channel] = angle;
    return true;
}

bool ServoController::setAllServos(const int angles[NUM_CHANNELS]) {
    // 如果所有角度都有效，则设置它们
    for (int i = 0; i < NUM_CHANNELS; i++) {
        servos[i].write(angles[i]);
        servoAngles[i] = angles[i];
    }
    return true;
}

void ServoController::stopAll() {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        servos[i].write(1500);
        servoAngles[i] = 1500;
    }
}

int ServoController::getServo(int channel) {
    if (!isValidChannel(channel)) {
        return -1;
    }
    return servoAngles[channel];
}

void ServoController::printStatus() {
    // Serial.print("Servo Status: ");
    // for (int i = 0; i < NUM_CHANNELS; i++) {
    //     Serial.print("CH");
    //     Serial.print(i);
    //     Serial.print("(Pin");
    //     Serial.print(servoPins[i]);
    //     Serial.print(")=");
    //     Serial.print(servoAngles[i]);
    //     Serial.print("°");
    //     if (i < NUM_CHANNELS - 1) Serial.print(", ");
    // }
    // Serial.println();
}

bool ServoController::isValidAngle(int angle) {
    return (angle >= 0 && angle <= 180);
}

bool ServoController::isValidChannel(int channel) {
    return (channel >= 0 && channel < NUM_CHANNELS);
}

void ServoController::detachAll() {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        servos[i].detach();
    }
    // Serial.println("All servos detached");
}

void ServoController::attachAll() {
    for (int i = 0; i < NUM_CHANNELS; i++) {
        servos[i].attach(servoPins[i]);
        servos[i].write(servoAngles[i]); // 恢复之前的角度
    }
    // Serial.println("All servos reattached");
}
