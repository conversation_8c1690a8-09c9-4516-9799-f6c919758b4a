#include "serial_parser.h"

// #define DEBUG_SERIAL_PROTOCOL 1

uint8_t rxBuffer[64];
uint8_t rxIndex = 0;
static unsigned long lastReceiveTime = 0;
static unsigned long lastTimeoutCheck = 0;

SerialParser::SerialParser(ServoController *controller)
{
    servoController = controller;
    resetFrameState();
}

void SerialParser::begin(long baudRate)
{
    Serial.begin(baudRate);
    // Serial.println("ROV PWM Board - Protocol Mode");
    // Serial.println("Waiting for protocol frames...");
}

// // CRC8校验函数
// uint8_t calculateCRC8(uint8_t *data, uint8_t length)
// {
//     uint8_t crc = 0x00;
//     for (uint8_t i = 0; i < length; i++)
//     {
//         crc ^= data[i];
//         for (uint8_t j = 0; j < 8; j++)
//         {
//             if (crc & 0x80)
//             {
//                 crc = (crc << 1) ^ 0x07;
//             }
//             else
//             {
//                 crc <<= 1;
//             }
//         }
//     }
//     return crc;
// }
// 在缓冲区中搜索帧头
int findFrameHeader()
{
    for (int i = 0; i <= rxIndex - 1; i++)
    {
        if (rxBuffer[i] == FRAME_HEADER_REQUEST)
        {
            return i; // 返回帧头位置
        }
    }
    return -1; // 未找到帧头
}
// 移除缓冲区前面的无效数据
void shiftBuffer(int startPos)
{
    if (startPos <= 0 || startPos >= rxIndex)
        return;

    // 将有效数据移到缓冲区开头
    int validDataLength = rxIndex - startPos;
    for (int i = 0; i < validDataLength; i++)
    {
        rxBuffer[i] = rxBuffer[startPos + i];
    }
    rxIndex = validDataLength;
}
//[ ] 发送应答帧
void SerialParser::sendResponse(uint8_t status, uint8_t *data, uint8_t dataLength)
{
    uint8_t frameLength = 4 + dataLength; // 帧头+状态码+长度+数据+校验码
    uint8_t frame[frameLength];

    frame[0] = FRAME_HEADER_RESPONSE; // 帧头
    frame[1] = status;                // 状态码
    frame[2] = dataLength;            // 数据长度

    // 复制数据
    for (uint8_t i = 0; i < dataLength; i++)
    {
        frame[3 + i] = data[i];
    }

    // 计算校验码（不包括帧头和校验码本身）
    uint8_t crc = calculateCRC8(&frame[1], frameLength - 2);
    frame[frameLength - 1] = crc;

    // 发送数据
    Serial.write(frame, frameLength);
}
// [ ]1 处理接收到的命令
void SerialParser::processCommand(uint8_t cmd, uint8_t *data, uint8_t dataLength)
{
    // Serial.println("\n--------------"); // 输出分隔线
    // Serial.print(cmd, HEX);
    // Serial.print(dataLength, HEX);
    // Serial.print(" ");

    // for (int i = 0; i <= dataLength - 1; i++)
    // {
    //     Serial.print(data[i], HEX);
    //     Serial.print(" ");
    // }
    // Serial.println("\n--------------"); // 输出分隔线

    // delay(200);
    switch (cmd)
    {
    case CMD_SET_SINGLE_PWM:
        handleSetSinglePWM(data, dataLength);
        break;

    case CMD_SET_ALL_PWM:
        handleSetAllPWM(data, dataLength);
        break;

        // default:
        //     SerialParser::sendResponse(STATUS_INVALID_CMD, nullptr, 0);
        //     break;
    }
}

// 解析接收到的数据帧
void SerialParser::parseFrame()
{
    // 连续搜索和处理帧
    while (rxIndex >= 4) // 最小帧长度
    {

        // Serial.println("\n--------------"); // 输出分隔线
        // for (int i = 0; i <= rxIndex - 1; i++)
        // {
        // 	Serial.print(rxBuffer[i], HEX);
        // }
        // Serial.println("\n--------------"); // 输出分隔线

        // 搜索帧头
        int headerPos = findFrameHeader();
        if (headerPos == -1)
        {

// 未找到帧头，保留最后3个字节（可能是不完整的帧头）
#if DEBUG_SERIAL_PROTOCOL
            Serial.print("No frame header found in %d bytes\n");
#endif
            if (rxIndex > 3)
            {
                rxIndex = 3;
                rxBuffer[0] = rxBuffer[rxIndex - 3];
                rxBuffer[1] = rxBuffer[rxIndex - 2];
                rxBuffer[2] = rxBuffer[rxIndex - 1];
            }
            return;
        }

        // 如果帧头不在开头，移动数据
        if (headerPos > 0)
        {
            shiftBuffer(headerPos);
        }

        // 现在rxBuffer[0]应该是帧头
        if (rxIndex < 4)
            return; // 数据不够，等待更多数据

        uint8_t cmd = rxBuffer[1];
        uint8_t dataLength = rxBuffer[2];

        // 检查帧长度
        uint8_t expectedLength = 4 + dataLength; // 帧头+命令+长度+数据+校验
        if (rxIndex < expectedLength)
            return; // 数据未接收完整

        // 校验CRC
        uint8_t receivedCRC = rxBuffer[expectedLength - 1];
        uint8_t calculatedCRC = calculateCRC8(&rxBuffer[1], expectedLength - 2);

        if (receivedCRC != calculatedCRC)
        {
            // TODO 通过协议显示CRC失败的数据，并统计
            // Serial.println("CRC mismatch, searching for next frame");
            sendResponse(STATUS_CRC_ERROR_CMD);
            // CRC错误，移除当前帧头，继续搜索下一个
            shiftBuffer(1);
            continue;
        }

        // 帧有效，处理命令
        uint8_t *cmdData = (dataLength > 0) ? &rxBuffer[3] : nullptr;
        processCommand(cmd, cmdData, dataLength);

        // 移除已处理的帧，继续处理剩余数据
        if (rxIndex > expectedLength)
        {
            shiftBuffer(expectedLength);
        }
        else
        {
            rxIndex = 0; // 缓冲区已处理完
            break;
        }
    }
}

void SerialParser::update()
{
    while (Serial.available())
    {
        uint8_t receivedByte = Serial.read();

        lastReceiveTime = millis();

        // Serial.write(receivedByte);

        // 防止缓冲区溢出 - 智能处理
        if (rxIndex >= sizeof(rxBuffer) - 1)
        {
            parseFrame(); // 尝试解析现有数据

            // 如果缓冲区仍然满，保留最后几个字节
            if (rxIndex >= sizeof(rxBuffer) - 1)
            {
                // 保留最后8个字节，可能包含部分帧
                if (rxIndex >= 8)
                {
                    for (int i = 0; i < 8; i++)
                    {
                        rxBuffer[i] = rxBuffer[rxIndex - 8 + i];
                    }
                    rxIndex = 8;
                }
                else
                {
                    rxIndex = 0; // 重置
                }
            }
        }

        rxBuffer[rxIndex++] = receivedByte;

        // 每接收一定数量的字节后尝试解析
        if (rxIndex >= 4) // 有足够数据时才解析
        {
            serialTimeOut = false;
            parseFrame();
        }
    }

    //[ ]超时处理 - 如果长时间没有新数据，清理不完整的帧
    unsigned long currentTime = millis();
    if (currentTime - lastTimeoutCheck > 20) // 每20ms检查一次
    {
        if (currentTime - lastReceiveTime > 100) // 200ms超时
        {
            serialTimeOut = true;
            // 如果有残留数据，清空缓冲区
            if (rxIndex > 0)
            {
                // Serial.printf("Serial timeout, clearing %d bytes\n", rxIndex);
                rxIndex = 0; // 清空不完整的数据
            }
        }
        lastTimeoutCheck = currentTime;
    }
}

void SerialParser::handleByte(uint8_t byte)
{
    switch (frameState)
    {
    case WAIT_HEADER:
        if (byte == FRAME_HEADER_REQUEST)
        {
            frameBuffer[0] = byte;
            frameIndex = 1;
            frameState = WAIT_COMMAND;
        }
        break;

    case WAIT_COMMAND:
        commandCode = byte;
        frameBuffer[frameIndex++] = byte;
        frameState = WAIT_LENGTH;
        break;

    case WAIT_LENGTH:
        dataLength = byte;
        frameBuffer[frameIndex++] = byte;
        if (dataLength == 0)
        {
            frameState = WAIT_CRC;
        }
        else if (dataLength <= MAX_FRAME_SIZE - 4)
        { // 4 = header + cmd + len + crc
            frameState = WAIT_DATA;
        }
        else
        {
            // 数据长度超出限制，重置状态
            resetFrameState();
        }
        break;

    case WAIT_DATA:
        frameBuffer[frameIndex++] = byte;
        if (frameIndex >= 3 + dataLength)
        { // 3 = header + cmd + len
            frameState = WAIT_CRC;
        }
        break;

    case WAIT_CRC:
        expectedCRC = byte;
        frameBuffer[frameIndex++] = byte;
        processFrame();
        resetFrameState();
        break;
    }
}

void SerialParser::processFrame()
{
    // 计算CRC (不包括CRC字节本身)
    uint8_t calculatedCRC = calculateCRC8(frameBuffer, frameIndex - 1);

    if (calculatedCRC != expectedCRC)
    {
        // CRC校验失败，发送错误应答
        sendResponse(STATUS_ERROR);
        return;
    }

    // 根据命令码处理不同的命令
    uint8_t *dataPtr = &frameBuffer[3]; // 数据从第4个字节开始

    switch (commandCode)
    {
    case CMD_SET_SINGLE_PWM:
        handleSetSinglePWM(dataPtr, dataLength);
        break;

    case CMD_SET_ALL_PWM:
        handleSetAllPWM(dataPtr, dataLength);
        break;

    default:
        // 未知命令，发送错误应答
        sendResponse(STATUS_ERROR);
        break;
    }
}

void SerialParser::handleSetSinglePWM(uint8_t *data, uint8_t length)
{

    if (length != 2)
    {
        sendResponse(STATUS_ERROR);
        return;
    }

    uint8_t channel = data[0];
    int angle = data[1];

    // 验证通道号
    if (channel >= PWM_CHANNELS)
    {
        sendResponse(STATUS_ERROR);
        return;
    }

    // 验证角度范围 (0-180)
    if (angle > 180)
    {
        sendResponse(STATUS_ERROR);
        return;
    }

    angle = map(data[1], 0, 180, 1000, 2000);

    // Serial.print("handleSetSinglePWM:");
    // Serial.print(data[0], DEC);
    // Serial.print(",");
    // Serial.print(data[1], DEC);
    // Serial.print(",");
    // Serial.println(angle, DEC);
    // 直接设置舵机角度
    if (servoController->setServo(channel, angle))
    {
        // sendResponse(STATUS_OK);
    }
    else
    {
        sendResponse(STATUS_ERROR);
    }
}

void SerialParser::handleSetAllPWM(uint8_t *data, uint8_t length)
{
    // Serial.print("SerialParser::handleSetAllPWM");
    if (length != PWM_CHANNELS)
    {
        sendResponse(STATUS_ERROR);
        return;
    }

    // 验证所有角度值
    for (int i = 0; i < PWM_CHANNELS; i++)
    {
        if (data[i] > 180)
        {
            sendResponse(STATUS_ERROR);
            return;
        }
    }

    // 直接设置所有舵机角度
    int angles[PWM_CHANNELS];
    for (int i = 0; i < PWM_CHANNELS; i++)
    {
        angles[i] = map(data[i], 0, 180, 1000, 2000);
    }

    if (servoController->setAllServos(angles))
    {
        // sendResponse(STATUS_OK);
    }
    else
    {
        sendResponse(STATUS_ERROR);
    }
}

uint8_t SerialParser::calculateCRC8(uint8_t *data, uint8_t length)
{
    uint8_t crc = 0x00;
    for (uint8_t i = 0; i < length; i++)
    {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++)
        {
            if (crc & 0x80)
            {
                crc = (crc << 1) ^ 0x07;
            }
            else
            {
                crc <<= 1;
            }
        }
    }
    return crc;
}

void SerialParser::resetFrameState()
{
    frameState = WAIT_HEADER;
    frameIndex = 0;
    commandCode = 0;
    dataLength = 0;
    expectedCRC = 0;
}
