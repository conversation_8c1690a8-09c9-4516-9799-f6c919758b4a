#include <Arduino.h>
#include <Servo.h>
#include "servo_controller.h"
#include "serial_parser.h"

// PWM输出引脚定义 (Arduino Nano支持PWM的引脚)
const int PWM_PINS[6] = {3, 5, 6, 9, 10, 11};

unsigned long lastReceiveTime = 0;

// 创建6个Servo对象
Servo servos[6];

// 创建舵机控制器和串口解析器对象
ServoController servoController(servos, PWM_PINS);
SerialParser serialParser(&servoController);

float totalCount = 50.0;

// CRC8校验函数
uint8_t calculateCRC8(uint8_t *data, uint8_t length)
{
  uint8_t crc = 0x00;
  for (uint8_t i = 0; i < length; i++)
  {
    crc ^= data[i];
    for (uint8_t j = 0; j < 8; j++)
    {
      if (crc & 0x80)
      {
        crc = (crc << 1) ^ 0x07;
      }
      else
      {
        crc <<= 1;
      }
    }
  }
  return crc;
}

int getA(float avgRaw)
{
  int a = 0;
  if (avgRaw < 0.24)
  {
    a = 1;
  }
  else if (avgRaw < 0.35)
  {
    a = 2;
  }
  else if (avgRaw < 0.42)
  {
    a = 3;
  }
  else if (avgRaw < 0.5)
  {
    a = 4;
  }
  else if (avgRaw < 0.6)
  {
    a = 5;
  }
  else if (avgRaw < 0.7)
  {
    a = 6;
  }
  else if (avgRaw < 0.8)
  {
    a = 7;
  }
  else if (avgRaw < 0.95)
  {
    a = 8;
  }
  else if (avgRaw < 1.1)
  {
    a = 9;
  }
  else if (avgRaw < 1.25)
  {
    a = 10;
  }
  else if (avgRaw < 1.40)
  {
    a = 11;
  }
  else if (avgRaw < 1.6)
  {
    a = 12;
  }
  else if (avgRaw < 1.8)
  {
    a = 13;
  }
  else if (avgRaw < 2.0)
  {
    a = 14;
  }
  else if (avgRaw < 3)
  {
    a = 15;
  }
  else
  {
    a = 99;
  }
  return a;
}
void getADC()
{
  float sum = 0;
  int a1, a2, a3, a4, a5, a6;
  float avgRaw = 0;

  for (int i = 0; i < totalCount; i++)
  {
    float rawValue = analogRead(A0);
    sum += rawValue;
    // delay(1);
  }
  avgRaw = sum / totalCount;
  a1 = avgRaw * 100;

  // a1 = getA(avgRaw);
  // Serial.print(avgRaw);
  // Serial.print(":");
  // Serial.println(a1);
  // return;

  sum = 0;
  for (int i = 0; i < totalCount; i++)
  {
    float rawValue = analogRead(A1);
    sum += rawValue;
    // delay(1);
  }
  avgRaw = sum / totalCount;
  // a2 = getA(avgRaw);
  a2 = avgRaw * 100;

  sum = 0;
  for (int i = 0; i < totalCount; i++)
  {
    float rawValue = analogRead(A2);
    sum += rawValue;
    // delay(1);
  }
  avgRaw = sum / totalCount;
  // a3 = getA(avgRaw);
  a3 = avgRaw * 100;

  sum = 0;
  for (int i = 0; i < totalCount; i++)
  {
    float rawValue = analogRead(A3);
    sum += rawValue;
    // delay(1);
  }
  avgRaw = sum / totalCount;
  // a4 = getA(avgRaw);
  a4 = avgRaw * 100;

  sum = 0;
  for (int i = 0; i < totalCount; i++)
  {
    float rawValue = analogRead(A4);
    sum += rawValue;
    // delay(1);
  }
  avgRaw = sum / totalCount;
  // a5 = getA(avgRaw);
  a5 = avgRaw * 100;

  sum = 0;
  for (int i = 0; i < totalCount; i++)
  {
    float rawValue = analogRead(A5);
    sum += rawValue;
    // delay(1);
  }
  avgRaw = sum / totalCount;
  // a6 = getA(avgRaw);
  a6 = avgRaw * 100;

  // Serial.print(a1);
  // Serial.print(",");
  // Serial.print(a2);
  // Serial.print(",");
  // Serial.print(a3);
  // Serial.print(",");
  // Serial.print(a4);
  // Serial.print(",");
  // Serial.print(a5);
  // Serial.print(",");
  // Serial.println(a6);

  uint8_t dataLength = 12;
  uint16_t adcValues[6] = {a1, a2, a3, a4, a5, a6};

  // adcValues[0]=300;

  uint8_t frameLength = 4 + dataLength; // 帧头+状态码+长度+数据+校验码
  uint8_t frame[frameLength];

  frame[0] = FRAME_HEADER_REQUEST; // 帧头
  frame[1] = CMD_SET_ADC_VALUES;   // 状态码
  frame[2] = dataLength;           // 数据长度

  // 复制数据
  for (uint8_t i = 0; i < 6; i++)
  {
    // frame[3 + i] = adcValues[i];
    memcpy(&frame[3 + i * 2], &adcValues[i], 2);
  }

  // 计算校验码（不包括帧头和校验码本身）
  uint8_t crc = calculateCRC8(&frame[1], frameLength - 2);
  frame[frameLength - 1] = crc;

  Serial.write(frame, frameLength);
}
void setup()
{
  // 初始化舵机控制器
  servoController.begin();

  // 初始化串口解析器
  serialParser.begin(115200);
}

void loop()
{
  // 处理串口命令
  serialParser.update();

  // TODO TimeOut判断
  if (serialParser.serialTimeOut)
  {
    servoController.stopAll();
  }

  // if (millis() - lastReceiveTime > 40)
  // {
  //   lastReceiveTime = millis();
  //   getADC();
  // }
}
