; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_extra_dirs = lib  # 确保这行存在
lib_deps = 
	; adafruit/Adafruit NeoPixel@^1.12.0
	madhephaestus/ESP32Servo@^3.0.6
	; adafruit/Adafruit MPU6050@^2.2.6
	tockn/MPU6050_tockn@^1.5.2
monitor_speed = 115200
upload_speed = 2000000

; upload_port = socket://orangepi3b.local:54321
upload_port = /dev/tty.wchusbserial539E0045951
