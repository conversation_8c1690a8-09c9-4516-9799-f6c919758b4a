#include <Arduino.h>

// #include <Adafruit_NeoPixel.h>

#include "MPU6050Sensor.h"

#include "HMC5883L.h"

#include "PWMController.h"

#include "TouchSensor.h"

#include "PinController.h"

#include "Serial2Class.h"

#include "WF5803F.h"

#include "ADC.h"

#include <HardwareSerial.h>

// 控制串口选择：定义USE_SERIAL2使用Serial2，不定义则使用Serial
#define USE_SERIAL2 1

// 调试开关
#define DEBUG_SERIAL_PROTOCOL 0 // 设置为1启用串口协议调试

#define DEBUG 1

bool motorProtected = false;

// 创建传感器实例，使用默认引脚和地址
WF5803F pressureSensor;

HMC5883L compassSensor;
unsigned long lastPrintTime = 0;
int sampleCount = 0;
float totalLatency = 0;

// Serial2Class mySerial2(16, 17); // 明确指定引脚，USE_SERIAL2宏控制使用Serial还是Serial2，不定义则使用Serial，定义为1使用Serial2，定义为0使用Serial

#define BUFFER_SIZE 256
// 环形缓冲区结构
typedef struct
{
	uint8_t buffer[BUFFER_SIZE];
	volatile uint16_t head;
	volatile uint16_t tail;
} RingBuffer;

RingBuffer uart2Buffer = {{0}, 0, 0};
HardwareSerial SerialPort2(2);

// 从缓冲区读取一行数据
bool readLineFromBuffer(RingBuffer *rb, char *line, uint16_t maxLen)
{
	uint16_t i = 0;
	while (rb->tail != rb->head && i < maxLen - 1)
	{
		char c = rb->buffer[rb->tail];
		rb->tail = (rb->tail + 1) % BUFFER_SIZE;
		line[i++] = c;
		if (c == '\n')
		{
			line[i] = '\0';
			return true;
		}
	}
	return false;
}

uint8_t currentThreshold = 20;

// 通信协议定义
#define FRAME_HEADER_REQUEST 0xAA  // 请求帧头
#define FRAME_HEADER_RESPONSE 0x55 // 应答帧头

// 命令码定义
#define CMD_GET_SENSOR_DATA 0x01 // 获取传感器数据
#define CMD_GET_PRESSURE 0x02	 // 获取气压计数据
#define CMD_GET_ATTITUDE 0x03	 // 获取姿态数据
#define CMD_GET_COMPASS 0x04	 // 获取罗盘数据
#define CMD_SET_PWM 0x10		 // 设置PWM输出
#define CMD_SET_LED 0x11		 // 设置LED
#define CMD_SET_RELAY 0x12		 // 设置继电器

#define CMD_SET_HOLD_DEPTH 0x13

#define CMD_SET_DEPTH_CONTROL 0x14	  // 设置深度控制模式
#define CMD_SET_HEADING_CONTROL 0x15  // 设置航向控制模式
#define CMD_GET_CONTROL_STATUS 0x16	  // 获取控制模式状态
#define CMD_SET_ATTITUDE_BALANCE 0x17 // 设置姿态平衡模式

#define CMD_SET_FORWARD_BALANCE 0x18	  // 设置前进平衡模式
#define CMD_SET_SERVO_CURRENT_PID 0x19	  // 设置舵机电流PID参数
#define CMD_GET_SERVO_STATUS 0x1A		  // 获取舵机状态
#define CMD_SET_CONSTANT_CURRENT 0x1B	  // 设置恒流控制模式
#define CMD_SET_CONSTANT_CURRENT_PID 0x1C // 设置恒流控制PID参数

#define CMD_GET_VOLTAGE 0x20   // 获取电压
#define CMD_CALIBRATE_ADC 0x21 // ADC校准命令

#define CMD_JOYSTICK_CONTROL 0x30  // Xbox摇杆控制命令
#define CMD_JOYSTICK_BUTTON_B 0x34 // 按键B

#define CMD_PING 0xFF // 心跳包

// 状态码定义
#define STATUS_OK 0x00			 // 成功
#define STATUS_ERROR 0x01		 // 错误
#define STATUS_INVALID_CMD 0x02	 // 无效命令
#define STATUS_INVALID_DATA 0x03 // 无效数据

int baudrate = 115200;

// 通信缓冲区
uint8_t rxBuffer[128];
uint8_t txBuffer[128];
uint8_t rxIndex = 0;
bool frameReceived = false;

bool serialTimeOut = true;

// D21 SDA D22 SCL
MPU6050Sensor mpu; // 创建 MPU6050 对象

// D13 LED
const uint8_t LED_PIN = 13;
// const int LED_SIZE = 12; // 12
// Adafruit_NeoPixel pixels(LED_SIZE, LED_PIN, NEO_GRB + NEO_KHZ800);

// D13 LED
PinController ledSwicher(LED_PIN); // 默认初始状态为LOW

// D 14，25, 26, 27, 32, 33,
// PWMController pwmController(32, 33, 25, 26, 27, 14);
PWMController pwmController(32, 14, 25, 26, 27);

// 创建触摸传感器对象
// 水密封性能检测
TouchSensor waterSealSensorTop(15, 0, 3);

// TouchSensor waterSealSensorBottom(32, 0, 3);
// 备用
// TouchSensor waterSealSensor(33, 0, 3);

// 入水状态检测
TouchSensor waterDetectionSensor(4, 0, 3);

bool cameraSwicherStatus = false;
PinController cameraSwicher(12); // D12 继电器 默认初始状态为LOW

// D34，35 ADC
// ADC adc1(34, 0.3333); //10/(10+20)
ADC adc1(34, 0.335193); // 10.033/(10.033+19.899)

ADC adcServo(33, 1);

unsigned long lastGetAllSensorDataTime = 0;

// [ ]DEBUG
unsigned long lastDebugTime = 0;

// NANO PWM board
u_int8_t motorCurrents[6] = {0, 0, 0, 0, 0, 0};

// 深度控制相关变量
float targetDepth = 0.0;				// 目标深度 (米)
float currentDepth = 0.0;				// 当前深度 (米)
bool depthControlEnabled = false;		// 深度控制开关
float depthKp = 150.0;					// 深度控制比例系数
float depthKi = 0.1;					// 深度控制积分系数
float depthKd = 0.1;					// 深度控制微分系数
float depthError = 0.0;					// 深度误差
float depthErrorSum = 0.0;				// 深度误差积分
float lastDepthError = 0.0;				// 上次深度误差
unsigned long lastDepthControlTime = 0; // 上次深度控制时间

// 航向控制相关变量
float targetHeading = 0.0;				  // 目标航向 (度)
float currentHeading = 0.0;				  // 当前航向 (度)
bool headingControlEnabled = false;		  // 航向控制开关
float headingKp = 6.0;					  // 航向控制比例系数 (进一步降低)
float headingKi = 0.0;					  // 航向控制积分系数 (暂时禁用积分项)
float headingKd = 0.1;					  // 航向控制微分系数 (大幅降低)
float headingError = 0.0;				  // 航向误差
float headingErrorSum = 0.0;			  // 航向误差积分
float lastHeadingError = 0.0;			  // 上次航向误差
unsigned long lastHeadingControlTime = 0; // 上次航向控制时间
float headingErrorDeadZone = 2.0;		  // 航向误差死区 (度) - 增大死区
float lastValidHeading = 0.0;			  // 上次有效航向读数
int headingStableCount = 0;				  // 航向稳定计数器
float smoothedYawOutput = 0.0;			  // 平滑的偏航输出

// 姿态平衡控制相关变量（用于定深模式）
float rollBalanceKp = 3.0;		  // Roll轴平衡比例系数（大幅降低）
float pitchBalanceKp = 3.0;		  // Pitch轴平衡比例系数（大幅降低）
float rollBalanceDeadZone = 20.0; // Roll轴死区（度）（增大死区）
// float pitchBalanceDeadZone = 8.0; // Pitch轴死区（度）（增大死区）
bool attitudeBalanceEnabled = true;		   // 姿态平衡开关
float smoothedRollCorrection = 0.0;		   // 平滑的Roll修正输出
unsigned long lastAttitudeControlTime = 0; // 上次姿态控制时间
float lastRollAngle = 0.0;				   // 上次Roll角度（用于滤波）
float lastPitchAngle = 0.0;				   // 上次Pitch角度（用于滤波）

// 前进平衡控制相关变量
float forwardBalanceKp = 2.0;		 // 前进俯仰平衡比例系数
float forwardBalanceDeadZone = 6.0;	 // 前进俯仰平衡死区（度）
float smoothedPitchCorrection = 0.0; // 平滑的俯仰修正输出
bool forwardBalanceEnabled = false;	 // 前进平衡开关

// 舵机
float servoCurrent = 0;
uint8_t servoAngle = 0;

// 舵机电流控制PID相关变量
float servoCurrentKp = 1.0;				// 电流控制比例系数（降低以减少抖动）
float servoCurrentKi = 1.0;				// 电流控制积分系数（降低以减少积分饱和）
float servoCurrentKd = 0.5;				// 电流控制微分系数（降低以减少噪声敏感性）
float targetCurrent = 0.5;				// 目标电流（A），设置为0.8A留有余量
float currentError = 0.0;				// 电流误差
float currentErrorSum = 0.0;			// 电流误差积分
float lastCurrentError = 0.0;			// 上次电流误差
unsigned long lastServoControlTime = 0; // 上次舵机控制时间
float servoAngleAdjustment = 0.0;		// 舵机角度调整量
float finalAngle = 0.0;

// 舵机平滑控制相关变量
float smoothedCurrent = 0.0;	 // 平滑后的电流值
float smoothedTargetAngle = 0.0; // 平滑后的目标角度
float lastValidAngle = 0.0;		 // 上次有效角度
float currentDeadZone = 0.05;	 // 电流死区（A）
int angleChangeLimit = 2;		 // 每次角度变化限制（度）

// 恒流控制专用变量
float constantCurrentKp = 20.0;			   // 恒流控制比例系数
float constantCurrentKi = 2.0;			   // 恒流控制积分系数
float constantCurrentKd = 1.0;			   // 恒流控制微分系数
float constantCurrentError = 0.0;		   // 恒流控制误差
float constantCurrentErrorSum = 0.0;	   // 恒流控制误差积分
float lastConstantCurrentError = 0.0;	   // 上次恒流控制误差
unsigned long lastConstantCurrentTime = 0; // 上次恒流控制时间
float constantCurrentAngle = 10.0;		   // 恒流控制角度
bool constantCurrentMode = false;		   // 恒流控制模式开关

bool servoStatus = true;

uint8_t systemStatus = 0;

// 函数声明
void handleJoystickControl(uint8_t *data, uint8_t dataLength);
void handleMotorControl(int8_t left_x, int8_t left_y, int8_t right_x, int8_t right_y);
void handleSetDepthControl(uint8_t *data, uint8_t dataLength);
void handleSetHeadingControl(uint8_t *data, uint8_t dataLength);
void handleGetControlStatus();
void handleSetAttitudeBalance(uint8_t *data, uint8_t dataLength);
void handleSetForwardBalance(uint8_t *data, uint8_t dataLength);
void handleSetServoCurrentPID(uint8_t *data, uint8_t dataLength);
void handleGetServoStatus();
void doServoConstantCurrent(float targetCurrentValue);
void handleSetConstantCurrent(uint8_t *data, uint8_t dataLength);
void handleSetConstantCurrentPID(uint8_t *data, uint8_t dataLength);

void servoClose();
void servoOpen();

int getCurrentByAvgRaw(int avgRaw)
{
	int a = 0;
	if (avgRaw < 5)
	{
		a = 0;
	}
	else if (avgRaw < 9)
	{
		a = 1;
	}
	else if (avgRaw < 15)
	{
		a = 2;
	}
	else if (avgRaw < 20)
	{
		a = 3;
	}
	else if (avgRaw < 30)
	{
		a = 4;
	}
	else if (avgRaw < 40)
	{
		a = 5;
	}
	else if (avgRaw < 70)
	{
		a = 6;
	}
	else if (avgRaw < 90)
	{
		a = 7;
	}
	else if (avgRaw < 120)
	{
		a = 8;
	}
	else if (avgRaw < 160)
	{
		a = 9;
	}
	else if (avgRaw < 200)
	{
		a = 10;
	}
	else if (avgRaw < 240)
	{
		a = 11;
	}
	else if (avgRaw < 270)
	{
		a = 12;
	}
	else if (avgRaw < 300)
	{
		a = 13;
	}
	else if (avgRaw < 330)
	{
		a = 14;
	}
	else if (avgRaw < 370)
	{
		a = 15;
	}
	else if (avgRaw < 410)
	{
		a = 16;
	}
	else if (avgRaw < 450)
	{
		a = 17;
	}
	else if (avgRaw < 480)
	{
		a = 18;
	}
	else if (avgRaw < 500)
	{
		a = 19;
	}
	else if (avgRaw < 540)
	{
		a = 20;
	}
	else if (avgRaw < 600)
	{
		a = 21;
	}
	else if (avgRaw < 640)
	{
		a = 22;
	}
	else if (avgRaw < 680)
	{
		a = 23;
	}
	else if (avgRaw < 710)
	{
		a = 24;
	}
	else
	{
		a = 25;
	}

	return a;
}

// 计算航向角度差（简化版本，减少抖动）
float calculateHeadingError(float target, float current)
{
	float error = target - current;

	// 处理360度环绕
	if (error > 180.0)
	{
		error -= 360.0;
	}
	else if (error < -180.0)
	{
		error += 360.0;
	}

	// 应用更大的死区
	if (abs(error) < headingErrorDeadZone)
	{
		error = 0.0;
	}

	return error;
}

// 简化的航向滤波函数
float filterHeading(float newHeading)
{
	// 简单的移动平均滤波
	return 0.7 * lastValidHeading + 0.3 * newHeading;
}

// CRC8校验函数
uint8_t calculateCRC8(uint8_t *data, uint8_t length)
{
	uint8_t crc = 0x00;
	for (uint8_t i = 0; i < length; i++)
	{
		crc ^= data[i];
		for (uint8_t j = 0; j < 8; j++)
		{
			if (crc & 0x80)
			{
				crc = (crc << 1) ^ 0x07;
			}
			else
			{
				crc <<= 1;
			}
		}
	}
	return crc;
}

//[ ] 发送应答帧
void sendResponse(uint8_t status, uint8_t *data, uint8_t dataLength)
{
	uint8_t frameLength = 4 + dataLength; // 帧头+状态码+长度+数据+校验码
	uint8_t frame[frameLength];

	frame[0] = FRAME_HEADER_RESPONSE; // 帧头
	frame[1] = status;				  // 状态码
	frame[2] = dataLength;			  // 数据长度

	// 复制数据
	for (uint8_t i = 0; i < dataLength; i++)
	{
		frame[3 + i] = data[i];
	}

	// 计算校验码（不包括帧头和校验码本身）
	uint8_t crc = calculateCRC8(&frame[1], frameLength - 2);
	frame[frameLength - 1] = crc;

	// 发送数据
	Serial.write(frame, frameLength);

	// 调试信息
	// Serial.printf("Sent response: status=0x%02X, dataLen=%d, frame=", status, dataLength);
	// for (uint8_t i = 0; i < frameLength; i++) {
	// 	Serial.printf("%02X", frame[i]);
	// }
	// Serial.println();
}

void sendCommandToNano(uint8_t command, uint8_t *data, uint8_t dataLength)
{
	uint8_t frameLength = 4 + dataLength; // 帧头+状态码+长度+数据+校验码
	uint8_t frame[frameLength];

	frame[0] = FRAME_HEADER_REQUEST; // 帧头
	frame[1] = command;				 // 状态码
	frame[2] = dataLength;			 // 数据长度

	// 复制数据
	for (uint8_t i = 0; i < dataLength; i++)
	{
		frame[3 + i] = data[i];
	}

	// 计算校验码（不包括帧头和校验码本身）
	uint8_t crc = calculateCRC8(&frame[1], frameLength - 2);
	frame[frameLength - 1] = crc;

	// 发送数据
	// mySerial2.writeBytes(frame, frameLength);

#ifndef DEBUG
	// 开发测试时临时关闭
	SerialPort2.write(frame, frameLength);
#endif
}
void stopMotor(u_int8_t motorIndex)
{
	uint8_t motorValue[2] = {motorIndex, 90};
	sendCommandToNano(0x10, motorValue, 2);
}

//[ ]!!! 处理获取传感器数据命令，包含水密封性能
void handleGetAllSensorData()
{
	uint8_t dataLenth = 35 + 6 + 1 + 4; // 气压计12字节 + MPU6050姿态12字节 + 罗盘航向4字节 + ADC电压4字节 + 水密封性能1字节 + 入水状态1字节+6字节ADC+舵机电流4字节
	uint8_t data[dataLenth];

	// 气压计数据
	float temperature = 0;
	float pressure = 0;
	float depth = 0;

	if (pressureSensor.init)
	{
		temperature = pressureSensor.readTemperature();
		pressure = pressureSensor.readPressureKalman();

		// depth = (pressure-4.68 - 1013.250) * 0.010;
		depth = pressureSensor.readAltitude();
	}

	memcpy(&data[0], &temperature, 4);
	memcpy(&data[4], &pressure, 4);
	memcpy(&data[8], &depth, 4);

	// MPU6050姿态数据和磁力计航向数据
	float angleX = 0.0, angleY = 0.0, angleZ = 0.0;
	float compassHeading = 0.0;

	// 读取MPU6050姿态数据
	mpu.update();
	angleX = mpu.getAngleX(); // Roll角度（横滚）
	angleY = mpu.getAngleY(); // Pitch角度（俯仰）
	// angleZ = mpu.getAngleZ(); // MPU6050的Yaw角度（偏航）

	// 读取磁力计数据作为独立的航向数据
	MagnetometerData compassData = compassSensor.read();
	compassHeading = compassData.heading; // HMC5883罗盘航向

	// 确保航向在0-360度范围内
	if (compassHeading < 0)
	{
		compassHeading += 360.0;
	}

	// 读取ADC校准后的电压值
	float adcVoltage = adc1.readCalibratedVoltage(); // 获取校准后的实际电压值 (V)

	// 读取水密封性能数据 (使用TouchSensor D4引脚)
	uint16_t waterSealTopValue = waterSealSensorTop.readRaw();

	// uint16_t waterSealBottomValue = waterSealSensorBottom.readRaw();
	uint16_t waterSealBottomValue = 0;

	// 读取入水状态数据 (使用TouchSensor D15引脚)
	uint16_t waterDetectionValue = waterDetectionSensor.readRaw();

	// 将16位触摸值转换为8位数据（取高8位或进行缩放）
	uint8_t waterSealStatus = (waterSealTopValue > 255) ? 255 : (uint8_t)waterSealTopValue;

	uint8_t waterSealBottomStatus = (waterSealBottomValue > 255) ? 255 : (uint8_t)waterSealBottomValue;

	uint8_t waterDetectionStatus = (waterDetectionValue > 255) ? 255 : (uint8_t)waterDetectionValue;

	// 数据打包：温度(4) + 压力(4) + 深度(4) + MPU6050姿态(12) + 罗盘航向(4) + ADC电压(4) + 水密封性能(1) + 入水状态(1) = 34字节
	memcpy(&data[12], &angleX, 4);		   // MPU6050 Roll
	memcpy(&data[16], &angleY, 4);		   // MPU6050 Pitch
	memcpy(&data[20], &angleZ, 4);		   // MPU6050 Yaw
	memcpy(&data[24], &compassHeading, 4); // HMC5883 罗盘航向
	memcpy(&data[28], &adcVoltage, 4);	   // ADC电压值 (4字节float)
	data[32] = waterSealStatus;			   // 水密封性能状态 (1字节uint8_t)
	data[33] = waterDetectionStatus;	   // 入水状态 (1字节uint8_t)

	// 增加操作模式
	if (depthControlEnabled)
	{
		data[34] = 1;
	}
	else
	{
		data[34] = 0;
	}

	memcpy(&data[35], &motorCurrents[0], 6); // 6字节 NANO pwm board 6路电机电流

	// memcpy(&data[35], &motorCurrents[0], 6);

	data[41] = waterSealBottomStatus; // 水密封性能状态 (1字节uint8_t)

	memcpy(&data[42], &servoCurrent, 4);

	sendResponse(STATUS_OK, data, dataLenth);
}

// 处理获取气压计数据命令
void handleGetPressureData()
{
	uint8_t data[12]; // 4字节温度 + 4字节压力 + 4字节深度

	float temperature = pressureSensor.readTemperature();
	float pressure = pressureSensor.readPressureKalman();
	float depth = (pressure - 1013.25) * 0.01; // 简化深度计算，1hPa ≈ 1cm水深

	// 将浮点数转换为字节数组（小端序）
	memcpy(&data[0], &temperature, 4);
	memcpy(&data[4], &pressure, 4);
	memcpy(&data[8], &depth, 4);

	sendResponse(STATUS_OK, data, 12);
}

// 处理获取罗盘数据命令
void handleGetCompassData()
{
	uint8_t data[16]; // 4个浮点数：x, y, z, heading

	// 读取罗盘数据
	MagnetometerData compassData = compassSensor.read();
	float x = compassData.x;
	float y = compassData.y;
	float z = compassData.z;
	float heading = compassData.heading;

	// 确保航向在0-360度范围内
	if (heading < 0)
	{
		heading += 360.0;
	}

	memcpy(&data[0], &x, 4);
	memcpy(&data[4], &y, 4);
	memcpy(&data[8], &z, 4);
	memcpy(&data[12], &heading, 4);

	sendResponse(STATUS_OK, data, 16);
}

// 处理设置PWM命令
void handleSetPWM(uint8_t *cmdData, uint8_t dataLength)
{
	if (dataLength < 2)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	uint8_t channel = cmdData[0];
	uint8_t value = cmdData[1];

	if (channel < 6)
	{
		// pwmController.updateValue(channel, value);
		sendResponse(STATUS_OK, nullptr, 0);
	}
	else
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
	}
}

// 处理设置LED命令
void handleSetLED(uint8_t *cmdData, uint8_t dataLength)
{
	if (dataLength < 4)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	uint8_t r = cmdData[0];
	// uint8_t g = cmdData[1];
	// uint8_t b = cmdData[2];
	// uint8_t brightness = cmdData[3];

	// // 设置LED颜色和亮度
	// for (int i = 0; i < LED_SIZE; i++)
	// {
	// 	pixels.setPixelColor(i, pixels.Color(r * brightness / 255,
	// 										 g * brightness / 255,
	// 										 b * brightness / 255));
	// }
	// pixels.show();

	if (r == 255)
	{
	}
	if (r == 0)
	{
	}

	bool ledStatus = ledSwicher.getState();
	if (ledStatus)
	{
		ledSwicher.Off();
	}
	else
	{
		ledSwicher.On();
	}
	sendResponse(STATUS_OK, nullptr, 0);
}

void handleSetServo(uint8_t *cmdData, uint8_t dataLength)
{

	if (servoStatus)
	{
		servoClose();
	}
	else
	{
		servoOpen();
	}
	sendResponse(STATUS_OK, nullptr, 0);
}

// 处理获取电压命令
void handleGetVoltage()
{
	uint8_t data[4];

	float voltage = adc1.readVoltage();
	memcpy(&data[0], &voltage, 4);

	sendResponse(STATUS_OK, data, 4);
}

// 处理心跳包
void handlePing()
{
	uint8_t data[4];
	uint32_t timestamp = millis();
	memcpy(&data[0], &timestamp, 4);

	sendResponse(STATUS_OK, data, 4);
}

// 处理ADC校准命令
void handleCalibrateADC(uint8_t *data, uint8_t dataLength)
{
	if (dataLength != 4)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 解析参考电压值 (float, 4字节)
	float referenceVoltage;
	memcpy(&referenceVoltage, data, 4);

	// 读取当前ADC测量值
	float measuredVoltage = adc1.readVoltage();

	// 执行校准
	adc1.calibrateWithReference(measuredVoltage, referenceVoltage);

	// 返回校准结果
	uint8_t responseData[12];
	memcpy(&responseData[0], &measuredVoltage, 4);	// 测量值
	memcpy(&responseData[4], &referenceVoltage, 4); // 参考值
	float offset = adc1.getCalibrationOffset();
	memcpy(&responseData[8], &offset, 4); // 校准偏移

	sendResponse(STATUS_OK, responseData, 12);

	// Serial.printf("ADC校准完成 - 测量:%.3fV, 参考:%.3fV, 偏移:%.3fV\n",
	// 			  measuredVoltage, referenceVoltage, offset);
}

// 处理设置深度控制模式命令
void handleSetDepthControl(uint8_t *data, uint8_t dataLength)
{
	if (dataLength != 1)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	uint8_t mode = data[0];

	if (depthControlEnabled)
	{
		mode = 0;
	}
	else
	{
		mode = 1;
	}

	if (mode == 0)
	{
		// 关闭深度控制
		depthControlEnabled = false;
		Serial.println("Depth control disabled");
	}
	else if (mode == 1)
	{
		// 开启深度控制
		if (pressureSensor.init)
		{
			depthControlEnabled = true;
			// 重新设置目标深度为当前深度
			targetDepth = pressureSensor.readAltitude();
			currentDepth = targetDepth;
			depthErrorSum = 0.0;
			lastDepthError = 0.0;
			lastDepthControlTime = millis();
			Serial.print("Depth control enabled - Target depth: ");
			Serial.println(targetDepth);
		}
		else
		{
			sendResponse(STATUS_ERROR, nullptr, 0);
			delay(3);
			Serial.println("Cannot enable depth control - Pressure sensor not available");
			return;
		}
	}
	else
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 返回当前状态
	uint8_t responseData[1];
	responseData[0] = depthControlEnabled ? 1 : 0;
	sendResponse(STATUS_OK, responseData, 1);
}

// 处理设置航向控制模式命令
void handleSetHeadingControl(uint8_t *data, uint8_t dataLength)
{
	// Serial.println("LOG:Heading control");
	// Serial.printf("LOG:handleSetHeadingControl:%X,%d\r\n", data[0], dataLength);

	// if (dataLength != 1)
	// {
	// 	sendResponse(STATUS_INVALID_DATA, nullptr, 0);
	// 	return;
	// }

	uint8_t mode = data[0];

	if (headingControlEnabled)
	{
		mode = 0;
	}
	else
	{
		mode = 1;
	}

	if (mode == 0)
	{
		// 关闭航向控制
		headingControlEnabled = false;
		smoothedYawOutput = 0.0;
		Serial.println("LOG:Heading control Disabled");
	}
	else if (mode == 1)
	{
		// 开启航向控制
		headingControlEnabled = true;
		// 重新设置目标航向为当前航向
		MagnetometerData compassData = compassSensor.read();
		targetHeading = compassData.heading;
		currentHeading = targetHeading;
		lastValidHeading = targetHeading;
		smoothedYawOutput = 0.0;
		lastHeadingControlTime = millis();
		Serial.println("LOG:Heading control Enabled");
	}
	else
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// Serial.printf("LOG:Heading control:%d",headingControlEnabled);

	// sendResponse(STATUS_OK, nullptr, 0);
}

// 处理获取控制模式状态命令
void handleGetControlStatus()
{
	uint8_t data[8]; // 深度控制状态(1) + 航向控制状态(1) + 姿态平衡状态(1) + 前进平衡状态(1) + 目标深度(4)

	data[0] = depthControlEnabled ? 1 : 0;
	data[1] = headingControlEnabled ? 1 : 0;
	data[2] = attitudeBalanceEnabled ? 1 : 0;
	// data[3] = forwardBalanceEnabled ? 1 : 0;

	// 添加目标深度信息
	memcpy(&data[4], &targetDepth, 4);

	sendResponse(STATUS_OK, data, 8);
}

// 处理设置姿态平衡模式命令
void handleSetAttitudeBalance(uint8_t *data, uint8_t dataLength)
{
	if (dataLength != 1)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	uint8_t mode = data[0];

	if (mode == 0)
	{
		// 关闭姿态平衡
		attitudeBalanceEnabled = false;
		Serial.println("LOG:Attitude balance disabled");
	}
	else if (mode == 1)
	{
		// 开启姿态平衡
		attitudeBalanceEnabled = true;
		Serial.println("LOG:Attitude balance enabled");
	}
	else
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 返回当前状态
	// uint8_t responseData[1];
	// responseData[0] = attitudeBalanceEnabled ? 1 : 0;
	// sendResponse(STATUS_OK, responseData, 1);
}

// 处理设置前进平衡模式命令
void handleSetForwardBalance(uint8_t *data, uint8_t dataLength)
{
	if (dataLength != 1)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	uint8_t mode = data[0];
	if (forwardBalanceEnabled)
	{
		mode = 0;
	}
	else
	{
		mode = 1;
	}

	if (mode == 0)
	{
		// 关闭前进平衡
		forwardBalanceEnabled = false;
		smoothedPitchCorrection = 0.0;
		Serial.println("LOG:Forward balance Disabled");
	}
	else if (mode == 1)
	{
		// 开启前进平衡
		forwardBalanceEnabled = true;
		smoothedPitchCorrection = 0.0;
		Serial.println("LOG:Forward balance Enabled");
	}
	else
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 返回当前状态
	// uint8_t responseData[1];
	// responseData[0] = forwardBalanceEnabled ? 1 : 0;
	// sendResponse(STATUS_OK, responseData, 1);
}

// 处理设置舵机电流PID参数命令
void handleSetServoCurrentPID(uint8_t *data, uint8_t dataLength)
{
	// Serial.print("ServoPID ");

	if (dataLength != 16) // 3个float参数，每个4字节
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 解析PID参数
	float kp, ki, kd, tC;
	memcpy(&kp, &data[0], 4);
	memcpy(&ki, &data[4], 4);
	memcpy(&kd, &data[8], 4);
	memcpy(&tC, &data[12], 4);

	// 验证参数范围
	if (kp < 0 || kp > 200 || ki < 0 || ki > 50 || kd < 0 || kd > 20 || tC < 0 || tC > 5)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// if(kp<=0.1){
	// 	kp=0;
	// }
	// if(ki<=0.1){
	// 	ki=0;
	// }
	// if(kd<=0.1){
	// 	kd=0;
	// }
	// 更新PID参数
	servoCurrentKp = kp;
	servoCurrentKi = ki;
	servoCurrentKd = kd;

	targetCurrent = tC;

	// 重置PID状态
	currentError = 0.0;
	currentErrorSum = 0.0;
	lastCurrentError = 0.0;

	// Serial.print("Kp:");
	// Serial.print(kp);
	// Serial.print(" Ki:");
	// Serial.print(ki);
	// Serial.print(" Kd:");
	// Serial.println(kd);

	// 返回成功状态
	// sendResponse(STATUS_OK, nullptr, 0);
	handleGetServoStatus();
}

// 处理获取舵机状态命令
void handleGetServoStatus()
{
	uint8_t data[17]; // 舵机角度(1) +目标电流(4) + PID参数(12)

	data[0] = servoAngle;
	memcpy(&data[1], &targetCurrent, 4);
	memcpy(&data[5], &servoCurrentKp, 4);
	memcpy(&data[9], &servoCurrentKi, 4);
	memcpy(&data[13], &servoCurrentKd, 4);

	sendResponse(STATUS_OK, data, 17);
}

// 处理设置恒流控制模式命令
void handleSetConstantCurrent(uint8_t *data, uint8_t dataLength)
{
	if (dataLength != 5) // 模式(1) + 目标电流(4)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	uint8_t mode = data[0];
	float targetCurrentValue;
	memcpy(&targetCurrentValue, &data[1], 4);

	// 验证目标电流范围
	if (targetCurrentValue < 0 || targetCurrentValue > 0.9)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	if (mode == 0)
	{
		// 关闭恒流控制模式
		constantCurrentMode = false;
		constantCurrentAngle = 90.0; // 回到中位
		Serial.println("Constant current mode disabled");
	}
	else if (mode == 1)
	{
		// 开启恒流控制模式
		constantCurrentMode = true;
		constantCurrentAngle = servoAngle; // 从当前角度开始
		constantCurrentErrorSum = 0.0;	   // 重置积分项
		lastConstantCurrentError = 0.0;
		lastConstantCurrentTime = millis();
		Serial.print("Constant current mode enabled - Target: ");
		Serial.print(targetCurrentValue);
		Serial.println("A");
	}
	else
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 返回当前状态
	uint8_t responseData[5];
	responseData[0] = constantCurrentMode ? 1 : 0;
	memcpy(&responseData[1], &targetCurrentValue, 4);
	sendResponse(STATUS_OK, responseData, 5);
}

// 处理设置恒流控制PID参数命令
void handleSetConstantCurrentPID(uint8_t *data, uint8_t dataLength)
{
	if (dataLength != 12) // 3个float参数，每个4字节
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 解析PID参数
	float kp, ki, kd;
	memcpy(&kp, &data[0], 4);
	memcpy(&ki, &data[4], 4);
	memcpy(&kd, &data[8], 4);

	// 验证参数范围
	if (kp < 0 || kp > 100 || ki < 0 || ki > 20 || kd < 0 || kd > 10)
	{
		sendResponse(STATUS_INVALID_DATA, nullptr, 0);
		return;
	}

	// 更新PID参数
	constantCurrentKp = kp;
	constantCurrentKi = ki;
	constantCurrentKd = kd;

	// 重置PID状态
	constantCurrentError = 0.0;
	constantCurrentErrorSum = 0.0;
	lastConstantCurrentError = 0.0;

	Serial.print("Constant current PID updated - Kp:");
	Serial.print(kp);
	Serial.print(" Ki:");
	Serial.print(ki);
	Serial.print(" Kd:");
	Serial.println(kd);

	// 返回成功状态
	sendResponse(STATUS_OK, nullptr, 0);
}

float currentOffset = 0;
float getServoCurrent(uint8_t checkCount = 1)
{
	float vcc = 4.485;
	// vcc = 4.977;

	float realV = 2.237;
	float checkV = 2.14;

	float currentDividerRatio;
	// currentDividerRatio = 0.066;//30A
	currentDividerRatio=0.185;//5A

	float voltageAfterDivider;
	float voltageAfterDividerCalibrattion;

	uint8_t i = 0;
	float smoothedVTotal = 0.0;
	float smoothedVoltage;
	float rawValue;

	while (i < checkCount)
	{
		rawValue = adcServo.readRaw();
		

		voltageAfterDivider = (rawValue * 3.300) / 4095.00;
		voltageAfterDividerCalibrattion = voltageAfterDivider + (realV - checkV);

		smoothedVTotal += voltageAfterDividerCalibrattion;
		i++;
	}
	smoothedVoltage = smoothedVTotal / checkCount;


	float rawCurrent = (smoothedVoltage - vcc / 2) / currentDividerRatio;
	rawCurrent = rawCurrent - currentOffset;
	// 确保电流值为正数
	if (rawCurrent < 0)
	{
		rawCurrent = 0;
	}

	// SerialPort2.printf("Base:2 raw:%.3f V:%.3f VCal:%.3f A:%.3f smoothedCurrent:%.3f servoAngle:%d\n",
	// 				   rawValue, voltageAfterDivider,voltageAfterDividerCalibrattion, rawCurrent, smoothedCurrent, servoAngle);
	return rawCurrent;
}
float getServoSmoothedCurrent()
{
	float current = getServoCurrent();
	smoothedCurrent = 0.5 * smoothedCurrent + 0.5 * current;
	return smoothedCurrent;
}

void calibrateServoCurrent()
{
	currentOffset= getServoCurrent(100);
}

unsigned long lastServoTestTime = 0;
bool startDoServoConstantCurrent = false;
bool servoOverCurrent = false;

// 目标角度 90-180
float _rawTargetAngle = 0;
void servoClose()
{
	startDoServoConstantCurrent = true;
	servoStatus = false;
}
void servoOpen()
{
	servoStatus = true;
}

void runServoJoystickStopOnPID(int8_t joystickValue)
{
	// 舵机电流控制（带PID和平滑处理）
	if (joystickValue < 0)
	{
		joystickValue = 0;
	}

	// 读取电流传感器
	float rawCurrent = getServoCurrent();

	// 确保电流值为正数
	if (rawCurrent < 0)
	{
		rawCurrent = 0;
	}

	// 电流平滑滤波（移动平均）
	smoothedCurrent = 0.8 * smoothedCurrent + 0.2 * rawCurrent;
	servoCurrent = smoothedCurrent;
	// servoCurrent = rawCurrent;

	// 计算目标角度（基于摇杆输入）
	float rawTargetAngle = map(joystickValue, 0, 100, 0, 180);

	// 目标角度平滑处理
	smoothedTargetAngle = 0.8 * smoothedTargetAngle + 0.2 * rawTargetAngle;
	// smoothedTargetAngle = rawTargetAngle;

	// PID电流控制（降低频率以减少抖动）
	unsigned long currentTime = millis();
	float deltaTime = (currentTime - lastServoControlTime) / 1000.0; // 转换为秒

	if (deltaTime > 0.02)
	{ // 增加控制周期到50ms，减少高频抖动
		// 计算电流误差（添加死区）
		currentError = targetCurrent - servoCurrent;

		// 应用电流死区，减少小幅波动
		if (abs(currentError) < currentDeadZone)
		{
			currentError = 0.0;
		}

		// 积分项（更严格的限幅）
		if (abs(currentError) > currentDeadZone)
		{
			currentErrorSum += currentError * deltaTime;
		}
		else
		{
			// 在死区内逐渐减少积分项
			currentErrorSum *= 0.9;
		}
		// currentErrorSum = constrain(currentErrorSum, -0.2, 0.2); // 更严格的积分限幅

		// 微分项（添加滤波）
		float currentErrorDiff = (currentError - lastCurrentError) / deltaTime;
		// currentErrorDiff = constrain(currentErrorDiff, -10.0, 10.0); // 限制微分项

		// PID输出（角度调整量）
		servoAngleAdjustment = servoCurrentKp * currentError +
							   servoCurrentKi * currentErrorSum +
							   servoCurrentKd * currentErrorDiff;

		// 更严格的角度调整范围限制
		// servoAngleAdjustment = constrain(servoAngleAdjustment, -15, 15);

		// 平滑角度调整输出
		static float lastAdjustment = 0.0;

		// servoAngleAdjustment = 0.7 * lastAdjustment + 0.3 * servoAngleAdjustment;
		servoAngleAdjustment = servoAngleAdjustment;

		lastAdjustment = servoAngleAdjustment;

		lastCurrentError = currentError;
		lastServoControlTime = currentTime;
	}

	// 应用电流保护逻辑
	finalAngle = smoothedTargetAngle;

	if (servoCurrent > 10.0)
	{
		// 电流超过1A，强制减小角度
		finalAngle = lastValidAngle - 10; // 更温和的减小角度
		// 重置积分项，避免积分饱和
		currentErrorSum = 0;
	}
	else if (servoCurrent > targetCurrent + currentDeadZone)
	{
		// 电流超过目标值，应用PID调整
		finalAngle = smoothedTargetAngle + servoAngleAdjustment;
	}
	else
	{
		// 电流在安全范围内，正常控制
		finalAngle = smoothedTargetAngle;
		// 在安全范围内逐渐减少积分项
		currentErrorSum *= 0.95;
	}

	// 角度变化速率限制（防止突变）
	// float angleDiff = finalAngle - lastValidAngle;
	// if (abs(angleDiff) > angleChangeLimit) {
	// 	if (angleDiff > 0) {
	// 		finalAngle = lastValidAngle + angleChangeLimit;
	// 	} else {
	// 		finalAngle = lastValidAngle - angleChangeLimit;
	// 	}
	// }

	// 限制最终角度范围
	// finalAngle = constrain(finalAngle, 0, 180);

	// 更新角度记录
	lastValidAngle = finalAngle;
	servoAngle = (uint8_t)finalAngle;

	// 输出到舵机
	pwmController.updateValue(0, servoAngle);
}
// 不适用电流判断的方式执行舵机运动
void runServoByJoystick(int8_t joystickValue)
{
	// 读取电流传感器
	servoCurrent = getServoCurrent();

	if (joystickValue < 0)
	{
		joystickValue = 0;
	}
	servoAngle = map(joystickValue, 0, 100, 0, 180);
	pwmController.updateValue(0, servoAngle);
}

unsigned long servoRunTime = 0;

float _t_servoAngle = 0;

// 逐步增加角度方式运行舵机,达到电流后退10度
void runServoByStepStopOnCurrentThreshold()
{
	servoCurrent = getServoSmoothedCurrent();

	if (servoStatus)
	{
		// Serial.printf("servoCurrent now:%d,last:%d\r\n", millis(), lastServoTestTime);

		if (servoCurrent > targetCurrent)
		{
			if ((millis() - lastServoTestTime) > 10)
			{
				startDoServoConstantCurrent = true;

				// 直接设置角度退回10度
				if (!servoOverCurrent && servoAngle > 5)
				{
					servoAngle -= 10;

					// 直接设置角度退回10度
					pwmController.updateValue(0, servoAngle);
				}
				servoOverCurrent = true;
			}
		}
		else
		{
			lastServoTestTime = millis();

			if (!startDoServoConstantCurrent)
			{
				if ((millis() - servoRunTime) > 15)
				{
					if (servoAngle <= 180)
					{
						servoAngle += 1;
					}
					pwmController.updateValue(0, servoAngle);

					servoRunTime = millis();
				}
			}
		}

		if (startDoServoConstantCurrent)
		{
			// pid控制锁紧电流
			// doServoConstantCurrent(targetCurrent);
		}
		else
		{
		}
	}
	else
	{
		lastServoTestTime = millis();
		servoOverCurrent = false;
		startDoServoConstantCurrent = false;
		servoAngle = 0;
		pwmController.updateValue(0, servoAngle);
	}
}

float pValue, iValue, dValue;
// 恒流控制舵机函数 - 以目标电流为目标，自动调整角度
void doServoConstantCurrent(float targetCurrentValue)
{
	// // 读取电流传感器
	// float rawCurrent = getServoCurrent(1);

	// // 电流平滑滤波
	// smoothedCurrent = 0.9 * smoothedCurrent + 0.1 * rawCurrent;
	// servoCurrent = smoothedCurrent;
	// servoCurrent=rawCurrent;

	// PID恒流控制
	unsigned long currentTime = millis();
	float deltaTime = (currentTime - lastConstantCurrentTime) / 1000.0;

	if (deltaTime > 0.02)
	{
		// 计算电流误差
		constantCurrentError = targetCurrentValue - servoCurrent;

		// 应用死区，减少小幅波动
		if (abs(constantCurrentError) < currentDeadZone)
		{
			constantCurrentError = 0.0;
		}

		// 积分项控制
		if (abs(constantCurrentError) > currentDeadZone)
		{
			constantCurrentErrorSum += constantCurrentError * deltaTime;
		}
		else
		{
			// 在死区内逐渐减少积分项
			constantCurrentErrorSum *= 0.95;
		}
		// 积分限幅
		constantCurrentErrorSum = constrain(constantCurrentErrorSum, -1.0, 1.0);

		// 微分项
		float constantCurrentErrorDiff = (constantCurrentError - lastConstantCurrentError) / deltaTime;
		constantCurrentErrorDiff = constrain(constantCurrentErrorDiff, -20.0, 20.0);

		// PID输出计算角度调整
		// servoAngleAdjustment = constantCurrentKp * constantCurrentError +
		// 					   constantCurrentKi * constantCurrentErrorSum +
		// 					   constantCurrentKd * constantCurrentErrorDiff;

		pValue = servoCurrentKp * constantCurrentError;
		iValue = servoCurrentKi * constantCurrentErrorSum;
		dValue = servoCurrentKd * constantCurrentErrorDiff;
		servoAngleAdjustment = pValue + iValue + dValue;

		// 限制角度调整范围
		// servoAngleAdjustment = constrain(servoAngleAdjustment, -15.0, 15.0);

		// 更新目标角度
		constantCurrentAngle += servoAngleAdjustment;

		lastConstantCurrentError = constantCurrentError;
		lastConstantCurrentTime = currentTime;
	}

	// 应用角度变化速率限制
	// float angleDiff = constantCurrentAngle - lastValidAngle;
	// if (abs(angleDiff) > angleChangeLimit)
	// {
	// 	if (angleDiff > 0)
	// 	{
	// 		constantCurrentAngle = lastValidAngle + angleChangeLimit;
	// 	}
	// 	else
	// 	{
	// 		constantCurrentAngle = lastValidAngle - angleChangeLimit;
	// 	}
	// }

	constantCurrentAngle = constrain(constantCurrentAngle, 0, 180);

	// 更新舵机角度
	lastValidAngle = constantCurrentAngle;
	servoAngle = (uint8_t)constantCurrentAngle;

	// 输出到舵机
	pwmController.updateValue(0, servoAngle);
}
// PID控制电流
void runServoByTimeStepOnPID()
{
	if (!servoStatus)
	{
		doServoConstantCurrent(targetCurrent);
	}
	else
	{
		// 重置PID状态
		constantCurrentError = 0.0;
		constantCurrentErrorSum = 0.0;

		constantCurrentAngle = 0.0;
		lastValidAngle = 0;

		lastServoTestTime = millis();
		servoAngle = 0;
		pwmController.updateValue(0, servoAngle);
	}

	// 调试输出（可选）
	// SerialPort2.print("Line0:0");
	// SerialPort2.print(",Line30:30");
	// SerialPort2.print(",Line60:180");
	// SerialPort2.print(",Line180:180");
	SerialPort2.print("PV:");
	SerialPort2.print(pValue);
	SerialPort2.print(",IV:");
	SerialPort2.print(iValue);
	SerialPort2.print(",DV:");
	SerialPort2.print(dValue);
	SerialPort2.print(",TC:");
	SerialPort2.print(targetCurrent);
	SerialPort2.print(",C:");
	SerialPort2.print(servoCurrent);
	SerialPort2.print(",Angle:");
	SerialPort2.print(servoAngle);
	SerialPort2.print(",Err:");
	SerialPort2.print(constantCurrentError);
	SerialPort2.print(",Adjust:");
	SerialPort2.println(servoAngleAdjustment);
}

uint8_t servoOverCurrentCount=0;
uint8_t servoOverCurrentCountEnd=10;
void runServoStopOnPID()
{
	// 读取电流传感器
	float rawCurrent = getServoCurrent(1);
	// 电流平滑滤波
	smoothedCurrent = 0.8 * smoothedCurrent + 0.2 * rawCurrent;
	servoCurrent = smoothedCurrent;

	if (startDoServoConstantCurrent && !servoOverCurrent)
	{
		doServoConstantCurrent(targetCurrent);
	}

	if (!servoStatus)
	{
		if (abs(servoAngleAdjustment) > 3)
		{
			startDoServoConstantCurrent = true;

			lastServoTestTime = millis();
		}
		else
		{
			// 延时后停止
			if (millis() - lastServoTestTime > 200 && !servoOverCurrent)
			{
				servoCurrent=getServoCurrent(10);
				if (servoCurrent < targetCurrent*0.8)
				{
					startDoServoConstantCurrent = false;
					servoOverCurrent=true;
				}
			}
			else
			{
				startDoServoConstantCurrent = true;
			}
		}
	}
	else
	{
		// 重置PID状态
		constantCurrentError = 0.0;
		constantCurrentErrorSum = 0.0;

		constantCurrentAngle = 0.0;
		lastValidAngle = 170;

		lastServoTestTime = millis();
		servoOverCurrent = false;
		startDoServoConstantCurrent = false;
		servoAngle = 0;
		pwmController.updateValue(0, servoAngle);
	}
	// 调试输出（可选）
	// SerialPort2.print("Line0:0");
	// SerialPort2.print(",Line30:30");
	// SerialPort2.print(",Line60:180");
	// SerialPort2.print(",Line180:180");
	SerialPort2.print("PV:");
	SerialPort2.print(pValue);
	SerialPort2.print(",IV:");
	SerialPort2.print(iValue);
	SerialPort2.print(",DV:");
	SerialPort2.print(dValue);
	SerialPort2.print(",C:");
	SerialPort2.print(servoCurrent);
	SerialPort2.print(",Angle:");
	SerialPort2.print(servoAngle);
	SerialPort2.print(",Err:");
	SerialPort2.print(constantCurrentError);
	SerialPort2.print(",Adjust:");
	SerialPort2.println(servoAngleAdjustment);
}
void runServoStopOnPID2()
{
		// 读取电流传感器
	float rawCurrent = getServoCurrent(10);

	// 电流平滑滤波
	smoothedCurrent = 0.8 * smoothedCurrent + 0.2 * rawCurrent;
	servoCurrent = smoothedCurrent;

	if (startDoServoConstantCurrent && !servoOverCurrent)
	{
		doServoConstantCurrent(targetCurrent);
	}

	if (!servoStatus)
	{
		if (abs(servoAngleAdjustment) > 3)
		{
			startDoServoConstantCurrent = true;

			lastServoTestTime = millis();
		}
		else
		{
			// 延时后停止
			if (millis() - lastServoTestTime > 100 && !servoOverCurrent)
			{
				if (servoCurrent < targetCurrent)
				{
					servoOverCurrentCount++;
					
				}

				if(servoOverCurrentCount>servoOverCurrentCountEnd)
				{
					servoOverCurrentCount=0;
					startDoServoConstantCurrent = false;
					servoOverCurrent=true;
				}

				lastServoTestTime = millis();
			}
			else
			{
				startDoServoConstantCurrent = true;
			}
		}
	}
	else
	{
		// 重置PID状态
		constantCurrentError = 0.0;
		constantCurrentErrorSum = 0.0;

		constantCurrentAngle = 180;
		lastValidAngle = 180;

		servoOverCurrentCount=0;

		lastServoTestTime = millis();
		servoOverCurrent = false;
		startDoServoConstantCurrent = false;
		servoAngle = 0;
		pwmController.updateValue(0, servoAngle);
	}

	// 调试输出（可选）
	// SerialPort2.print("Line0:0");
	// SerialPort2.print(",Line30:30");
	// SerialPort2.print(",Line60:180");
	// SerialPort2.print(",Line180:180");
	SerialPort2.print("PV:");
	SerialPort2.print(pValue);
	SerialPort2.print(",IV:");
	SerialPort2.print(iValue);
	SerialPort2.print(",DV:");
	SerialPort2.print(dValue);
	SerialPort2.print(",C:");
	SerialPort2.print(servoCurrent);
	SerialPort2.print(",Angle:");
	SerialPort2.print(servoAngle);
	SerialPort2.print(",Err:");
	SerialPort2.print(constantCurrentError);
	SerialPort2.print(",Adjust:");
	SerialPort2.println(servoAngleAdjustment);
}

// TODO 测试时声明全局，发布改为局部变量
int8_t left_y;
// [ ] 2 处理Xbox摇杆控制命令
void handleJoystickControl(uint8_t *data, uint8_t dataLength)
{
	// if (dataLength != 4)
	// {
	// 	sendResponse(STATUS_INVALID_DATA, nullptr, 0);
	// 	return;
	// }

	// 解析摇杆数据
	// 数据格式: left_x, left_y, right_x, right_y (每个1字节，有符号，范围-100到+100)
	int8_t left_x = (int8_t)data[0];
	left_y = (int8_t)data[1];
	int8_t right_x = (int8_t)data[2];
	int8_t right_y = (int8_t)data[3];

	// 打印接收到的摇杆数据
	// Serial.printf("L(%+4d,%+4d) R(%+4d,%+4d)\r\n", left_x, left_y, right_x, right_y);

	// 电机控制逻辑
	handleMotorControl(left_x, left_y, right_x, right_y);

	// 接收joystick数据后 发送确认响应
	if (millis() - lastGetAllSensorDataTime > 200)
	{
		//  TODO 使用获取全部传感器信息作为响应
		lastGetAllSensorDataTime = millis();

#ifndef DEBUG
		handleGetAllSensorData();
#else
		sendResponse(STATUS_OK, nullptr, 0);
#endif
	}
	else
	{
		// 发送响应
		sendResponse(STATUS_OK, nullptr, 0);
	}
}
// 自定义函数用于浮点数映射
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max)
{
	return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}
float reverseAngle(int angle)
{
	angle = mapFloat(angle, 180, 0, 0, 180);
	return angle;
}

void testMotor(int8_t left_x, int8_t left_y, int8_t right_x, int8_t right_y)
{
	// 无刷版本
	int m1 = mapFloat(left_x, -100, 100, 0, 180);
	int m2 = mapFloat(left_y, -100, 100, 0, 180);
	int m3 = mapFloat(right_x, -100, 100, 0, 180);
	int m4 = mapFloat(right_y, -100, 100, 0, 180);
	int m5 = mapFloat(left_x, -100, 100, 0, 180);
	int m6 = mapFloat(left_y, -100, 100, 0, 180);

	// pwmController.updateValue(0, m1);				// 3
	// pwmController.updateValue(1, m2);				// 4
	// pwmController.updateValue(2, reverseAngle(m3)); // 2 R
	// pwmController.updateValue(3, m4);				// 5 R
	// pwmController.updateValue(4, m5);				// 1
	// pwmController.updateValue(5, m6);				// 6

	// 调整后
	// pwmController.updateValue(4, m5); // 1
	// pwmController.updateValue(2, reverseAngle(m3)); // 2 R
	// pwmController.updateValue(0, m1);// 3
	// pwmController.updateValue(1, m2);// 4
	// pwmController.updateValue(3, m4); // 5 R
	// pwmController.updateValue(5, m6); // 6

	// 有刷版本
	// pwmController.updateValue(0, m1);				// 1
	// pwmController.updateValue(2, m2);				// 2
	// pwmController.updateValue(5, reverseAngle(m3)); // 3
	// pwmController.updateValue(3, reverseAngle(m4)); // 4
	// pwmController.updateValue(1, reverseAngle(m5)); // 5
	// pwmController.updateValue(4, reverseAngle(m6)); // 6
}
// [ ] 处理电机控制逻辑 - 带深度控制
void handleMotorControl(int8_t left_x, int8_t left_y, int8_t right_x, int8_t right_y)
{
	// 测试电机方向
	//  testMotor(left_x, left_y, right_x, right_y);
	//  return;

	// 电机序号
	// 2 1
	// 6 5
	// 4 3
	// 将摇杆值转换为电机控制值
	// 摇杆范围: -100 到 +100

	int u_angle = mapFloat(left_y, -100, 100, 0, 180);
	int p_angle = mapFloat(right_y, -100, 100, 0, 180);
	int r_angle = mapFloat(right_x, -100, 100, 0, 180);
	int y_angle = mapFloat(left_x, -100, 100, 0, 180);

	// 将输入映射到-90~+90范围（方便计算）
	int normX = r_angle - 90;	// -90（左） ~ +90（右）
	int normY = p_angle - 90;	// -90（后） ~ +90（前）
	int normZ = u_angle - 90;	// -90（下降） ~ +90（上升）
	int normYaw = y_angle - 90; // -90（逆） ~ +90（顺）

	// 航向控制逻辑（简化版本，减少抖动）
	int yawOutput = 0; // 航向控制输出

	if (headingControlEnabled)
	{
		// 读取当前航向（简化滤波）
		MagnetometerData compassData = compassSensor.read();
		float rawHeading = compassData.heading;

		// 简单的移动平均滤波
		currentHeading = 0.7 * currentHeading + 0.3 * rawHeading;

		// 根据left_x调整目标航向（简化逻辑）
		if (abs(normYaw) > 15)
		{ // 大幅增加死区，只响应明确的操作
			if (normYaw > 0)
			{
				targetHeading += 0.3; // 非常小的调整步长
			}
			else
			{
				targetHeading -= 0.3;
			}

			// 限制目标航向范围到0-360度
			if (targetHeading >= 360.0)
				targetHeading -= 360.0;
			if (targetHeading < 0.0)
				targetHeading += 360.0;
		}

		// 简化的控制逻辑
		unsigned long currentTime = millis();
		float deltaTime = (currentTime - lastHeadingControlTime) / 1000.0;

		if (deltaTime > 0.01)
		{ // 进一步增加控制周期到50ms
			headingError = calculateHeadingError(targetHeading, currentHeading);

			// 只使用比例控制，禁用积分和微分
			float headingPIDOutput = 0.0;

			// 只有在误差超过死区时才输出
			if (abs(headingError) > headingErrorDeadZone)
			{
				headingPIDOutput = headingKp * headingError;
			}

			// 严格限制输出范围
			headingPIDOutput = constrain(headingPIDOutput, -15, 15);

			// 输出平滑处理
			float targetYawOutput = headingPIDOutput;
			smoothedYawOutput = 0.8 * smoothedYawOutput + 0.2 * targetYawOutput;

			// 输出死区
			if (abs(smoothedYawOutput) < 2.0)
			{
				smoothedYawOutput = 0.0;
			}

			yawOutput = (int)smoothedYawOutput;

			lastHeadingControlTime = currentTime;
		}
		else
		{
			// 使用上次的平滑输出
			yawOutput = (int)smoothedYawOutput;
		}
	}
	else
	{
		// 航向控制关闭时，使用原始摇杆控制
		yawOutput = normYaw;
		smoothedYawOutput = 0.0;
	}

	// 前进平衡控制（基于俯仰角度）
	int pitchCorrection = 0; // 俯仰修正输出
	if (forwardBalanceEnabled && (abs(normY) > 10))
	{ // 只在前进/后退时启用
		// 前进平衡控制周期控制
		unsigned long currentTime = millis();
		if (currentTime - lastAttitudeControlTime > 50)
		{ // 50ms控制周期
			// 读取MPU6050俯仰数据
			mpu.update();
			float rawPitchAngle = mpu.getAngleY(); // Pitch角度（俯仰）

			// 简单的移动平均滤波
			float pitchAngle = 0.7 * lastPitchAngle + 0.3 * rawPitchAngle;

			// 俯仰平衡控制
			float targetPitchCorrection = 0.0;
			if (abs(pitchAngle) > forwardBalanceDeadZone)
			{
				targetPitchCorrection = forwardBalanceKp * pitchAngle;
				targetPitchCorrection = constrain(targetPitchCorrection, -20, 20); // 限制最大输出
			}

			// 输出平滑处理
			smoothedPitchCorrection = 0.8 * smoothedPitchCorrection + 0.2 * targetPitchCorrection;

			// 输出死区处理
			if (abs(smoothedPitchCorrection) < 2.0)
			{
				smoothedPitchCorrection = 0.0;
			}

			// 更新滤波变量
			lastPitchAngle = pitchAngle;

			// 调试输出（可选）
			// Serial.print("Pitch: ");
			// Serial.print(pitchAngle);
			// Serial.print(" PitchCorr: ");
			// Serial.println(smoothedPitchCorrection);
		}

		pitchCorrection = (int)smoothedPitchCorrection;
	}
	else
	{
		// 不在前进状态时，重置俯仰修正
		smoothedPitchCorrection *= 0.9; // 逐渐衰减
		pitchCorrection = (int)smoothedPitchCorrection;
	}

	// 计算水平移动电机输出（使用航向控制输出 + 俯仰平衡）
	// 俯仰修正：抬头时增加后电机功率，减少前电机功率（修正方向）
	int m1 = 90 + normX + normY + yawOutput - pitchCorrection; // 前右：右平移 + 前进 + 航向控制 - 俯仰修正
	int m2 = 90 - normX + normY - yawOutput - pitchCorrection; // 前左：左平移 + 前进 + 航向控制 - 俯仰修正
	int m3 = 90 + normX - normY - yawOutput + pitchCorrection; // 后右：右平移 + 后退 + 航向控制 + 俯仰修正
	int m4 = 90 - normX - normY + yawOutput + pitchCorrection; // 后左：左平移 + 后退 + 航向控制 + 俯仰修正

	// 深度控制逻辑
	int m5 = 90; // 中右电机：升降
	int m6 = 90; // 中左电机：升降

	if (depthControlEnabled && pressureSensor.init)
	{
		// 读取当前深度
		currentDepth = pressureSensor.readAltitude();
		// currentDepth=pressureSensor.readPressureKalman()*10.0f*0.01;
		// Serial.print("currentDepth:");
		// Serial.println(currentDepth);

		//  -90（下降） ~ +90（上升）
		// 跟随摇杆比例，最大0.18，最小0.01
		float step = abs(normZ) * 0.00001;

		// 根据u_angle调整目标深度
		if (abs(normZ) > 5)
		{
			// 死区，避免小幅抖动
			if (normZ < 0)
			{
				// u_angle减小，上升（减小深度）
				// targetDepth -= 0.01; // 每次调整1cm
				targetDepth -= step; // 跟随摇杆比例 新增
			}
			else
			{
				// u_angle增大，下降（增加深度）
				// targetDepth += 0.01; // 每次调整1cm
				targetDepth += step; // 跟随摇杆比例 新增
			}
			// 限制目标深度范围
			// targetDepth = constrain(targetDepth, -20.0, 30.0); // -2米到30米
		}

		// PID深度控制
		unsigned long currentTime = millis();
		float deltaTime = (currentTime - lastDepthControlTime) / 1000.0; // 转换为秒

		if (deltaTime > 0.01)
		{
			// 最小控制周期10ms
			depthError = targetDepth - currentDepth;
			depthErrorSum += depthError * deltaTime;

			// 积分限幅
			depthErrorSum = constrain(depthErrorSum, -1.0, 1.0);

			float depthErrorDiff = (depthError - lastDepthError) / deltaTime;

			// PID输出
			float depthOutput = depthKp * depthError + depthKi * depthErrorSum + depthKd * depthErrorDiff;

			// 限制输出范围
			depthOutput = constrain(depthOutput, -90, 90);

			// 基础深度控制输出
			float baseM5 = 90 + depthOutput;
			float baseM6 = 90 - depthOutput;

			// 姿态平衡控制（基于陀螺仪，优化版本）
			if (attitudeBalanceEnabled)
			{
				// 姿态控制周期控制（降低频率）
				if (currentTime - lastAttitudeControlTime > 10)
				{ // 50ms控制周期
					// 读取MPU6050姿态数据
					mpu.update();
					float rawRollAngle = mpu.getAngleX(); // Roll角度（横滚）
					// float rawPitchAngle = mpu.getAngleY();  // Pitch角度（俯仰）

					// 简单的移动平均滤波
					float rollAngle = 0.7 * lastRollAngle + 0.3 * rawRollAngle;
					// float pitchAngle = 0.7 * lastPitchAngle + 0.3 * rawPitchAngle;

					// Roll轴平衡控制（左右倾斜）
					float targetRollCorrection = 0.0;
					if (abs(rollAngle) > rollBalanceDeadZone)
					{
						targetRollCorrection = rollBalanceKp * rollAngle;
						targetRollCorrection = constrain(targetRollCorrection, -15, 15); // 减小最大输出
					}

					// 输出平滑处理（参考航向控制方式）
					smoothedRollCorrection = 0.8 * smoothedRollCorrection + 0.2 * targetRollCorrection;

					// 输出死区处理
					if (abs(smoothedRollCorrection) < 1.5)
					{
						smoothedRollCorrection = 0.0;
					}

					// 更新滤波变量
					lastRollAngle = rollAngle;
					// lastPitchAngle = pitchAngle;
					lastAttitudeControlTime = currentTime;
				}

				// 应用姿态修正到升降电机
				// Roll修正：左倾时增加右电机(m5)功率，减少左电机(m6)功率
				m5 = (int)constrain(baseM5 + smoothedRollCorrection, 0, 180);
				m6 = (int)constrain(baseM6 - smoothedRollCorrection, 0, 180);

				// 调试输出（可选）
				// Serial.print("Roll: ");
				// Serial.print(lastRollAngle);
				// Serial.print(" SmoothCorr: ");
				// Serial.println(smoothedRollCorrection);
			}
			else
			{
				// 姿态平衡关闭时，只使用深度控制
				smoothedRollCorrection = 0.0;
				m5 = (int)constrain(baseM5, 0, 180);
				m6 = (int)constrain(baseM6, 0, 180);
			}

			lastDepthError = depthError;
			lastDepthControlTime = currentTime;
		}
	}
	else
	{
		// 深度控制关闭时，使用原始摇杆控制
		m5 = 90 + normZ;
		m6 = 90 - normZ;
	}

	// 限制所有电机输出范围
	m1 = constrain(m1, 0, 180);
	m2 = constrain(m2, 0, 180);
	m3 = constrain(m3, 0, 180);
	m4 = constrain(m4, 0, 180);
	m5 = constrain(m5, 0, 180);
	m6 = constrain(m6, 0, 180);

// 无刷版本
#ifndef BRUSHED_MOTOR
	// V1
	// pwmController.updateValue(4, m1);				// 1
	// pwmController.updateValue(2, m2);				// 2
	// pwmController.updateValue(0, reverseAngle(m3)); // 3
	// pwmController.updateValue(1, reverseAngle(m4)); // 4
	// pwmController.updateValue(3, reverseAngle(m5)); // 5
	// pwmController.updateValue(5, reverseAngle(m6)); // 6

	// V2
	uint8_t motorValue[6] = {90, 90, 90, 90, 90, 90};
	motorValue[0] = m1;
	motorValue[1] = m2;
	motorValue[2] = reverseAngle(m3);
	motorValue[3] = reverseAngle(m4);
	motorValue[4] = reverseAngle(m5);
	motorValue[5] = reverseAngle(m6);

	// TODO 电流检测有问题，禁用电流保护
	// if (motorCurrents[0] > currentThreshold)
	// {
	// 	motorValue[0] = 90;
	// 	motorProtected = true;
	// }
	// if (motorCurrents[1] > currentThreshold)
	// {
	// 	motorValue[1] = 90;
	// 	motorProtected = true;
	// }
	// if (motorCurrents[2] > currentThreshold)
	// {
	// 	motorValue[2] = 90;
	// 	motorProtected = true;
	// }
	// if (motorCurrents[3] > currentThreshold)
	// {
	// 	motorValue[3] = 90;
	// 	motorProtected = true;
	// }
	// if (motorCurrents[4] > currentThreshold)
	// {
	// 	motorValue[4] = 90;
	// 	motorProtected = true;
	// }
	// if (motorCurrents[5] > currentThreshold)
	// {
	// 	motorValue[5] = 90;
	// 	motorProtected = true;
	// }

	// if (motorProtected)
	// {
	// 	depthControlEnabled = false;
	// 	motorValue[0] = 90;
	// 	motorValue[1] = 90;
	// 	motorValue[2] = 90;
	// 	motorValue[3] = 90;
	// 	motorValue[4] = 90;
	// 	motorValue[5] = 90;
	// }

	sendCommandToNano(0x11, motorValue, 6);

#endif
}

// [ ]1 处理接收到的命令
void processCommand(uint8_t cmd, uint8_t *data, uint8_t dataLength)
{
	// if (cmd != CMD_JOYSTICK_CONTROL && cmd != 0x90)
	// {
	// 	Serial.printf("LOG:%X,%d\r\n", cmd, dataLength);
	// }

	// delay(200);
	switch (cmd)
	{
	case CMD_JOYSTICK_CONTROL:
		handleJoystickControl(data, dataLength);
		break;

	case CMD_JOYSTICK_BUTTON_B:
		handleSetServo(data, dataLength);
		break;

	case CMD_SET_SERVO_CURRENT_PID:
		handleSetServoCurrentPID(data, dataLength);
		break;

	case CMD_GET_SERVO_STATUS:
		handleGetServoStatus();
		break;

	case CMD_SET_CONSTANT_CURRENT:
		handleSetConstantCurrent(data, dataLength);
		break;

	case CMD_SET_CONSTANT_CURRENT_PID:
		handleSetConstantCurrentPID(data, dataLength);
		break;

	case CMD_SET_DEPTH_CONTROL:
		handleSetDepthControl(data, dataLength);
		break;
	case CMD_SET_HEADING_CONTROL:
		// Serial.printf("LOG:cmd:%X\r\n", cmd);
		handleSetHeadingControl(data, dataLength);
		break;

	case 0x90: // nano adc all raw values
		for (int i = 0; i < 6; i++)
		{

			motorCurrents[i] = getCurrentByAvgRaw(data[i * 2 + 1] << 8 | data[i * 2]);
		}

		// Serial.print(getCurrentByAvgRaw(data[1]<<8 | data[0]), DEC);
		// Serial.print(data[1]<<8 | data[0], DEC);
		// Serial.println();
		break;

	case 0x31: // 解除电机保护
		motorProtected = false;
		Serial.printf("LOG:motorProtect:False\r\n");
		break;

	case 0x32: // 电流减小
		// 临时航向P
		//  motorProtected = false;
		//  currentThreshold--;
		//  if (currentThreshold < 5)
		//  {
		//  	currentThreshold = 5;
		//  }
		//  Serial.printf("LOG:currentThreshold:%d\r\n", currentThreshold);

		headingKp -= 0.1;
		Serial.printf("LOG:headingKp:%f\r\n", headingKp);
		break;

	case 0x33: // 电流加大
			   // 临时航向P
		//  motorProtected = false;
		//  currentThreshold++;
		//  if (currentThreshold > 40)
		//  {
		//  	currentThreshold = 40;
		//  }
		//  Serial.printf("LOG:currentThreshold:%d\r\n", currentThreshold);

		headingKp += 0.1;
		Serial.printf("LOG:headingKp:%f\r\n", headingKp);
		break;

	case CMD_GET_SENSOR_DATA:
		handleGetAllSensorData();
		break;

	case CMD_GET_PRESSURE:
		handleGetPressureData();
		break;

	case CMD_GET_COMPASS:
		handleGetCompassData();
		break;

	case CMD_SET_PWM:
		handleSetPWM(data, dataLength);
		break;

	case CMD_SET_LED:
		handleSetLED(data, dataLength);
		break;

	case CMD_GET_VOLTAGE:
		handleGetVoltage();
		break;

	case CMD_CALIBRATE_ADC:
		handleCalibrateADC(data, dataLength);
		break;

	case CMD_PING:
		handlePing();
		break;

	case CMD_SET_RELAY:
		cameraSwicherStatus = !cameraSwicherStatus;
		// Serial.println(cameraSwicherStatus);

		if (cameraSwicherStatus)
		{
			cameraSwicher.On();
		}
		else
		{
			cameraSwicher.Off();
		}
		sendResponse(STATUS_OK, nullptr, 0);
		break;

	case CMD_SET_HOLD_DEPTH:
		targetDepth = pressureSensor.readAltitude();
		depthControlEnabled = !depthControlEnabled;
		break;

	case CMD_GET_CONTROL_STATUS:
		handleGetControlStatus();
		break;

	case CMD_SET_ATTITUDE_BALANCE:
		handleSetAttitudeBalance(data, dataLength);
		break;

	case CMD_SET_FORWARD_BALANCE:
		handleSetForwardBalance(data, dataLength);
		break;

	default:
		sendResponse(STATUS_INVALID_CMD, nullptr, 0);
		break;
	}
}

// 在缓冲区中搜索帧头
int findFrameHeader()
{
	for (int i = 0; i <= rxIndex - 1; i++)
	{
		if (rxBuffer[i] == FRAME_HEADER_REQUEST)
		{
			return i; // 返回帧头位置
		}
	}
	return -1; // 未找到帧头
}

// 移除缓冲区前面的无效数据
void shiftBuffer(int startPos)
{
	if (startPos <= 0 || startPos >= rxIndex)
		return;

	// 将有效数据移到缓冲区开头
	int validDataLength = rxIndex - startPos;
	for (int i = 0; i < validDataLength; i++)
	{
		rxBuffer[i] = rxBuffer[startPos + i];
	}
	rxIndex = validDataLength;
}

// 解析接收到的数据帧
void parseFrame()
{
	// 连续搜索和处理帧
	while (rxIndex >= 4) // 最小帧长度
	{
#if DEBUG_SERIAL_PROTOCOL
		Serial.println("--------------"); // 输出分隔线

		for (int i = 0; i <= rxIndex - 1; i++)
		{
			Serial.print(rxBuffer[i], HEX);
		}

		Serial.println("--------------"); // 输出分隔线
#endif

		// 搜索帧头
		int headerPos = findFrameHeader();
		if (headerPos == -1)
		{

// 未找到帧头，保留最后3个字节（可能是不完整的帧头）
#if DEBUG_SERIAL_PROTOCOL
			// Serial.printf("No frame header found in %d bytes\n", rxIndex);
#endif
			if (rxIndex > 3)
			{
				rxIndex = 3;
				rxBuffer[0] = rxBuffer[rxIndex - 3];
				rxBuffer[1] = rxBuffer[rxIndex - 2];
				rxBuffer[2] = rxBuffer[rxIndex - 1];
			}
			return;
		}

		// 如果帧头不在开头，移动数据
		if (headerPos > 0)
		{
#if DEBUG_SERIAL_PROTOCOL
			Serial.printf("Frame header found at position %d, shifting buffer\n", headerPos);
#endif
			shiftBuffer(headerPos);
		}

		// 现在rxBuffer[0]应该是帧头
		if (rxIndex < 4)
			return; // 数据不够，等待更多数据

		uint8_t cmd = rxBuffer[1];
		uint8_t dataLength = rxBuffer[2];

		// 检查帧长度
		uint8_t expectedLength = 4 + dataLength; // 帧头+命令+长度+数据+校验
		if (rxIndex < expectedLength)
			return; // 数据未接收完整

		// 校验CRC
		uint8_t receivedCRC = rxBuffer[expectedLength - 1];
		uint8_t calculatedCRC = calculateCRC8(&rxBuffer[1], expectedLength - 2);

		if (receivedCRC != calculatedCRC)
		{
			// TODO 通过协议显示CRC失败的数据，并统计
			Serial.println("CRC mismatch, searching for next frame");
			// CRC错误，移除当前帧头，继续搜索下一个
			shiftBuffer(1);
			continue;
		}

		// 帧有效，处理命令
		uint8_t *cmdData = (dataLength > 0) ? &rxBuffer[3] : nullptr;
		processCommand(cmd, cmdData, dataLength);

		// 移除已处理的帧，继续处理剩余数据
		if (rxIndex > expectedLength)
		{
			shiftBuffer(expectedLength);
		}
		else
		{
			rxIndex = 0; // 缓冲区已处理完
			break;
		}
	}
}

void parseFrame2()
{
	// 连续搜索和处理帧
	while (rxIndex >= 4) // 最小帧长度
	{
		// 搜索帧头
		int headerPos = findFrameHeader();
		if (headerPos == -1)
		{
			if (rxIndex > 3)
			{
				rxIndex = 3;
				rxBuffer[0] = rxBuffer[rxIndex - 3];
				rxBuffer[1] = rxBuffer[rxIndex - 2];
				rxBuffer[2] = rxBuffer[rxIndex - 1];
			}
			return;
		}

		// 如果帧头不在开头，移动数据
		if (headerPos > 0)
		{
			shiftBuffer(headerPos);
		}

		// 现在rxBuffer[0]应该是帧头
		if (rxIndex < 4)
			return; // 数据不够，等待更多数据

		uint8_t cmd = rxBuffer[1];
		uint8_t dataLength = rxBuffer[2];

		// 检查帧长度
		uint8_t expectedLength = 4 + dataLength; // 帧头+命令+长度+数据+校验
		if (rxIndex < expectedLength)
			return; // 数据未接收完整

		// 校验CRC
		uint8_t receivedCRC = rxBuffer[expectedLength - 1];
		uint8_t calculatedCRC = calculateCRC8(&rxBuffer[1], expectedLength - 2);

		if (receivedCRC != calculatedCRC)
		{
			// TODO 通过协议显示CRC失败的数据，并统计
			// Serial.println("Serial 2CRC mismatch");
			// CRC错误，移除当前帧头，继续搜索下一个
			shiftBuffer(1);
			continue;
		}

		// 帧有效，处理命令
		uint8_t *cmdData = (dataLength > 0) ? &rxBuffer[3] : nullptr;
		processCommand(cmd, cmdData, dataLength);

		// 移除已处理的帧，继续处理剩余数据
		if (rxIndex > expectedLength)
		{
			shiftBuffer(expectedLength);
		}
		else
		{
			rxIndex = 0; // 缓冲区已处理完
			break;
		}
	}
}
// 串口2中断处理
// void IRAM_ATTR handleUART2()
// {
// 	while (SerialPort2.available())
// 	{
// 		// Serial.write(Serial2.read());
// 		// Serial.print(Serial2.read(), HEX);

// 		uint8_t receivedByte = SerialPort2.read();
// 		// 防止缓冲区溢出 - 智能处理
// 		if (rxIndex >= sizeof(rxBuffer) - 1)
// 		{
// 			parseFrame2(); // 尝试解析现有数据

// 			// 如果缓冲区仍然满，保留最后几个字节
// 			if (rxIndex >= sizeof(rxBuffer) - 1)
// 			{
// 				// Serial.println("Buffer overflow, keeping last 8 bytes...");
// 				// 保留最后8个字节，可能包含部分帧
// 				if (rxIndex >= 8)
// 				{
// 					for (int i = 0; i < 8; i++)
// 					{
// 						rxBuffer[i] = rxBuffer[rxIndex - 8 + i];
// 					}
// 					rxIndex = 8;
// 				}
// 				else
// 				{
// 					rxIndex = 0; // 重置
// 				}
// 			}
// 		}

// 		rxBuffer[rxIndex++] = receivedByte;

// 		// 每接收一定数量的字节后尝试解析
// 		if (rxIndex >= 4) // 有足够数据时才解析
// 		{
// 			serialTimeOut = false;
// 			parseFrame2();
// 		}
// 	}
// }
// 处理串口接收数据
void handleSerialData()
{
	static unsigned long lastReceiveTime = 0;
	static unsigned long lastTimeoutCheck = 0;

	// TODO 处理串口2数据 来自NANO的ADC信息
	// bool serial2HasData = false;
	// while (Serial2.available())
	// {
	// 	serial2HasData = true;
	// 	// Serial.write(Serial2.read());
	// 	// Serial.print(Serial2.read(), HEX);
	// 	uint8_t receivedByte = Serial2.read();

	// 	// 防止缓冲区溢出 - 智能处理
	// 	if (rxIndex >= sizeof(rxBuffer) - 1)
	// 	{
	// 		parseFrame2(); // 尝试解析现有数据

	// 		// 如果缓冲区仍然满，保留最后几个字节
	// 		if (rxIndex >= sizeof(rxBuffer) - 1)
	// 		{
	// 			// Serial.println("Buffer overflow, keeping last 8 bytes...");
	// 			// 保留最后8个字节，可能包含部分帧
	// 			if (rxIndex >= 8)
	// 			{
	// 				for (int i = 0; i < 8; i++)
	// 				{
	// 					rxBuffer[i] = rxBuffer[rxIndex - 8 + i];
	// 				}
	// 				rxIndex = 8;
	// 			}
	// 			else
	// 			{
	// 				rxIndex = 0; // 重置
	// 			}
	// 		}
	// 	}

	// 	rxBuffer[rxIndex++] = receivedByte;

	// 	// 每接收一定数量的字节后尝试解析
	// 	if (rxIndex >= 4) // 有足够数据时才解析
	// 	{
	// 		serialTimeOut = false;
	// 		parseFrame2();
	// 	}

	// }

	while (Serial.available())
	{
		uint8_t receivedByte = Serial.read();

		lastReceiveTime = millis();

		// Serial.write(receivedByte);

		// 防止缓冲区溢出 - 智能处理
		if (rxIndex >= sizeof(rxBuffer) - 1)
		{
			// Serial.println("Buffer near full, trying to parse existing data...");
			parseFrame(); // 尝试解析现有数据

			// 如果缓冲区仍然满，保留最后几个字节
			if (rxIndex >= sizeof(rxBuffer) - 1)
			{
				// Serial.println("Buffer overflow, keeping last 8 bytes...");
				// 保留最后8个字节，可能包含部分帧
				if (rxIndex >= 8)
				{
					for (int i = 0; i < 8; i++)
					{
						rxBuffer[i] = rxBuffer[rxIndex - 8 + i];
					}
					rxIndex = 8;
				}
				else
				{
					rxIndex = 0; // 重置
				}
			}
		}

		rxBuffer[rxIndex++] = receivedByte;

		// 每接收一定数量的字节后尝试解析
		if (rxIndex >= 4) // 有足够数据时才解析
		{
			serialTimeOut = false;
			parseFrame();
		}
	}

	//[ ]超时处理 - 如果长时间没有新数据，清理不完整的帧
	unsigned long currentTime = millis();
	if (currentTime - lastTimeoutCheck > 30) // 每20ms检查一次
	{
		if (currentTime - lastReceiveTime > 100) // 200ms超时
		{
			serialTimeOut = true;
			// 如果有残留数据，清空缓冲区
			if (rxIndex > 0)
			{
				// Serial.printf("Serial timeout, clearing %d bytes\n", rxIndex);
				rxIndex = 0; // 清空不完整的数据
			}
		}
		lastTimeoutCheck = currentTime;
	}
}

void touchSensorInit()
{
	// 初始化水密封触摸传感器 (D4)
	waterSealSensorTop.begin();

	// waterSealSensorBottom.begin();

	// 初始化入水检测触摸传感器 (D15)
	waterDetectionSensor.begin();

	// 打印校准信息
	// Serial.println("Touch Sensors Initialized");
	// Serial.print("Water Seal No Touch Value: ");
	// Serial.println(waterSealSensorTop.getNoTouchValue());
	// Serial.print("Water Detection No Touch Value: ");
	// Serial.println(waterDetectionSensor.getNoTouchValue());
}

void pwmInit()
{
	Serial.println("PWM controller init");

	pwmController.begin();

	// 设置6路PWM参数
	// 参数: 通道(0-5), 频率(Hz), 最小脉冲宽度(us), 最大脉冲宽度(us), 初始值(0-180)
	pwmController.setPWM(0, 50, 1000, 2000, 0);
	pwmController.setPWM(1, 50, 1000, 2000, 90);
	pwmController.setPWM(2, 50, 1000, 2000, 90);
	pwmController.setPWM(3, 50, 1000, 2000, 90);
	pwmController.setPWM(4, 50, 1000, 2000, 90);
	pwmController.setPWM(5, 50, 1000, 2000, 90);
}

void initLED()
{
	// pixels.begin();
	// pixels.clear();
	// pixels.show();
}
void ledOn()
{
	// u_int8_t val = 5; // 255
	// for (int i = 0; i < LED_SIZE; i++)
	// {
	// 	pixels.setPixelColor(i, pixels.Color(val, val, val));
	// }
	// pixels.show();

	ledSwicher.On();
}
void ledOff()
{
	// for (int i = 0; i < LED_SIZE; i++)
	// {
	// 	pixels.setPixelColor(i, pixels.Color(0, 0, 0));
	// }
	// pixels.show();

	ledSwicher.Off();
}

void serial2Init()
{
	Serial.println("Initializing Serial2...");

	// mySerial2.setRxBufferSize(256);
	// Serial2.setRxBufferSize(256);

	// 初始化串口2，波特率115200
	// mySerial2.begin(baudrate);
	// Serial2.begin(baudrate);

	SerialPort2.begin(115200, SERIAL_8N1, 16, 17);
	// SerialPort2.onReceive(handleUART2, true);
	// SerialPort2.onReceive(handleUART2);

	Serial.println("Serial2 initialized successfully");
}

void initMPU()
{
	mpu.begin(); // 初始化 MPU6050

	// mpu.calcGyroOffsets(true); //[ ] 自动校准陀螺仪
}
void displayMpu()
{
	mpu.update(); // 更新数据
	// 获取角度数据并打印
	Serial.print(" Angle X: ");
	Serial.print(mpu.getAngleX());
	Serial.print(", Y: ");
	Serial.print(mpu.getAngleY());
	Serial.print(", Z: ");
	Serial.print(mpu.getAngleZ());
}

void initWf5803f()
{
	// Serial.println("WF5803F Sensor Init");

	// 初始化传感器
	if (!pressureSensor.begin())
	{
		Serial.println("WF5803F not found!");
		// while (1)
		// 	;
		return;
	}

	pressureSensor.setAverageCount(1);
	// 可选：调整卡尔曼滤波参数
	pressureSensor.setKalmanParams(0.01, 0.1); // Q=0.01, R=0.1
}
void displayWf5803f()
{
	// 读取并打印数据
	// float temperature = pressureSensor.readTemperature();
	// float pressure = pressureSensor.readPressure();

	// float temperature = pressureSensor.readTemperatureKalman();
	// float pressure = pressureSensor.readPressureKalman();

	// float altitude = pressureSensor.readAltitude(); // 使用默认海平面气压1013.25hPa
	// Serial.printf(" T: %.2f°C, P: %.3f kPa\r\n", temperature, pressure);
	// Serial.println(altitude);
}

// 配置传感器
void compassInit()
{
	// 初始化HMC5883L，设置I2C频率为800kHz
	if (!compassSensor.begin(21, 22, 800000))
	{
		Serial.println("Could not find HMC5883L pressureSensor!");
		// while (1)
		// 	;
	}

	// 配置为最高速率75Hz
	compassSensor.setDataRate(DR_75HZ);
	// compassSensor.setDataRate(DR_15HZ);
	compassSensor.setRange(GA_1_3);
	compassSensor.setMeasurementMode(CONTINUOUS);
	compassSensor.setDeclination(2.16f); // 设置磁偏角

	// Serial.println("");
	// Serial.println("Calibrating compassSensor... rotate pressureSensor slowly in all directions");
	// compassSensor.calibrate(); // 默认500个采样点
	// Serial.println("Calibration complete!");
}
void displayCompass(bool testAll = 0)
{
	unsigned long startTime = micros();

	MagnetometerData data = compassSensor.read();

	if (testAll)
	{
		// 测试速度
		displayMpu();
		// displayWf5803f();
	}

	unsigned long endTime = micros();
	float latency = (endTime - startTime) / 1000.0f; // 转换为ms

	sampleCount++;
	totalLatency += latency;

	// 每秒打印一次统计信息
	if (millis() - lastPrintTime >= 1000)
	{
		float avgLatency = totalLatency / sampleCount;
		float actualRate = sampleCount / ((millis() - lastPrintTime) / 1000.0f);

		Serial.print("Samples: ");
		Serial.print(sampleCount);
		Serial.print(" | Avg. Latency: ");
		Serial.print(avgLatency);
		Serial.print("ms");
		Serial.print(" | Actual Rate: ");
		Serial.print(actualRate);
		Serial.print("Hz");

		// 打印最新数据
		// Serial.print("X: "); Serial.print(data.x);
		// Serial.print(" Y: "); Serial.print(data.y);
		// Serial.print(" Z: "); Serial.print(data.z);
		Serial.print(" Heading: ");
		Serial.println(data.heading);

		// 重置统计
		sampleCount = 0;
		totalLatency = 0;
		lastPrintTime = millis();
	}
}

void setup()
{
	delay(100);

	Serial.begin(115200);
	delay(50);

	serial2Init();

	delay(50);

	// cameraSwicher.Off();

	// delay(500);

	// 初始化ADC
	adc1.begin();
	Serial.println("ADC initialized");

	// ADC校准设置
	// 根据实际测量结果进行校准：万用表4.64V，ADC测量4.45V，差值0.19V
	adc1.setCalibration(0.19, 1.0); // 偏移校准+0.19V，比例保持1.0
	// Serial.println("ADC calibration applied: +0.19V offset");

	// 显示校准前后的ADC读数
	float rawVoltage = adc1.readVoltage();
	float calibratedVoltage = adc1.readCalibratedVoltage();
	// Serial.printf("ADC测试读数 - 原始: %.3fV, 校准后: %.3fV\n", rawVoltage, calibratedVoltage);

	// 初始化传感器
	initMPU();	   // 启用MPU6050姿态传感器初始化
	compassInit(); // 启用磁力计初始化
	initWf5803f();

	// 初始化触摸传感器
	touchSensorInit();
	Serial.println("Touch pressureSensor initialized");

	// 初始化LED
	// initLED();
	// Serial.println("LED initialized");

	delay(50);

	// 初始化深度控制
	if (pressureSensor.init)
	{
		targetDepth = pressureSensor.readAltitude();
		currentDepth = targetDepth;
		lastDepthControlTime = millis();
		Serial.print("Depth control initialized - Target depth: %.2f m\n");
		Serial.println(targetDepth);
	}
	else
	{
		depthControlEnabled = false;
		Serial.println("Depth control disabled - Pressure sensor not available");
	}

	// 初始化航向控制
	if (compassSensor.begin())
	{
		MagnetometerData compassData = compassSensor.read();
		targetHeading = compassData.heading;
		currentHeading = targetHeading;
		lastValidHeading = targetHeading;
		lastHeadingControlTime = millis();
		smoothedYawOutput = 0.0;
		Serial.println("Heading control initialized");
	}
	else
	{
		headingControlEnabled = false;
		Serial.println("Heading control disabled - Compass sensor not available");
	}

	// 初始化姿态平衡控制
	smoothedRollCorrection = 0.0;
	smoothedPitchCorrection = 0.0;
	lastAttitudeControlTime = millis();
	lastRollAngle = 0.0;
	lastPitchAngle = 0.0;
	Serial.println("Attitude balance control initialized");

	// 初始化前进平衡控制
	forwardBalanceEnabled = true;
	// Serial.println("Forward balance control initialized");

	// 初始化舵机电流控制PID
	currentError = 0.0;
	currentErrorSum = 0.0;
	lastCurrentError = 0.0;
	servoAngleAdjustment = 0.0;
	lastServoControlTime = millis();

	// 初始化舵机平滑控制变量
	smoothedCurrent = 0.0;
	smoothedTargetAngle = 90.0;
	lastValidAngle = 90.0;

	// 初始化恒流控制变量
	constantCurrentError = 0.0;
	constantCurrentErrorSum = 0.0;
	lastConstantCurrentError = 0.0;
	constantCurrentAngle = 90.0;
	constantCurrentMode = false;
	lastConstantCurrentTime = millis();

	Serial.println("Servo current PID control initialized");
	Serial.println("Constant current control initialized");

	delay(50);

	// 初始化PWM控制器
	pwmInit();
	Serial.println("PWM controller initialized");

	delay(50);
	calibrateServoCurrent();
	Serial.println("calibrateServoCurrent");

	Serial.println("LOG:ROV ESP32 System Ready!");
	delay(50);

	systemStatus = 1;

	uint8_t data[2] = {0x01, 0xFF};
	sendResponse(STATUS_OK, data, 2);
}

void loop()
{
	// 处理串口通信
	handleSerialData();

	// runServoByJoystick(left_y);

	// runServoJoystickStopOnPID(left_y);

	// runServoByTimeStepOnPID();

	// runServoByStepStopOnCurrentThreshold();
	
	// runServoStopOnPID();
	runServoStopOnPID2();

	if (serialTimeOut)
	{
		uint8_t motorValue[6] = {90, 90, 90, 90, 90, 90};
		// 电机测试
		// motorValue[0] = 0;
		// motorValue[1] = 30;
		// motorValue[2] = 90;
		// motorValue[3] = 120;
		// motorValue[4] = 150;
		// motorValue[5] = 180;

		sendCommandToNano(0x11, motorValue, 6);

		// uint8_t motorValue1[2]={0,10};
		// sendCommandToNano(0x10, motorValue1, 2);

		// uint8_t motorValue2[2]={5,180};
		// sendCommandToNano(0x10, motorValue2, 2);
	}

	// 可选：定期输出传感器数据到串口监视器（调试用）
	if (millis() - lastDebugTime > 200 && systemStatus == 1)
	{
		lastDebugTime = millis();

		if (serialTimeOut)
		{
			// Serial.println("serialTimeOut\r\n");
			Serial.println("LOG:serialTimeOut");
		}

		if (motorProtected)
		{
			Serial.println("LOG:MotorProtected");
		}

		// 调试输出（可选）
		// SerialPort2.print("Current:");
		// SerialPort2.print(servoCurrent);
		// SerialPort2.print(",SmoothedC:");
		// SerialPort2.print(smoothedCurrent);
		// SerialPort2.print(",TargetAngle:");
		// SerialPort2.print(smoothedTargetAngle);
		// SerialPort2.print(",FinalAngle:");
		// SerialPort2.print(serveAngle);
		// SerialPort2.print(",AdjAngle:");
		// SerialPort2.print(servoAngleAdjustment);
		// SerialPort2.print(",Error:");
		// SerialPort2.println(currentError);

		// pwmController.updateValue(0, serveAngle);

		// if(serveAngle<180){
		// 	serveAngle+=1;
		// }else{
		// 	serveAngle=30;
		// }
		// pwmController.updateValue(0, serveAngle);

		// float rawValue = adcServo.readRaw();
		// float stepV2=3.300;
		// float voltageAfterDivider = (rawValue * stepV2) / 4095.00;
		// float a=(2.3-voltageAfterDivider)/0.066+0.7;
		// Serial.printf("LOG:raw:%.3f V:%.3f A:%.3f\n", rawValue, voltageAfterDivider,a);

		// // 输出气压计数据
		// float temperature = pressureSensor.readTemperature();
		// float pressure = pressureSensor.readPressureKalman();
		// Serial.printf("Debug - Temp: %.2f°C, Pressure: %.3f kPa\n",
		// 			  temperature, pressure);

		// float depth = (pressure - 1013.25) * 0.01;
		// Serial.printf("Debug - Temp: %.2f°C, Pressure: %.2f hPa, Depth: %.2f m\n",
		// 			  temperature, pressure, depth);

		// // 输出MPU6050姿态数据
		// mpu.update();
		// Serial.printf("Debug - MPU6050: Roll=%.1f°, Pitch=%.1f°, Yaw=%.1f°\n",
		// 			  mpu.getAngleX(), mpu.getAngleY(), mpu.getAngleZ());

		// // 输出磁力计数据
		// MagnetometerData compassData = compassSensor.read();
		// Serial.printf("Debug - Compass: X=%.2f, Y=%.2f, Z=%.2f, Heading=%.1f°\n",
		// 			  compassData.x, compassData.y, compassData.z, compassData.heading);

		// 输出电压
		// float voltage = adc1.readCalibratedVoltage();
		// Serial.printf("Debug - Voltage: %.1fV\n\n", voltage);

		// Serial.println("Serial.println");

		// uint16_t waterSealTopValue = waterSealSensorTop.readRaw();
		// uint16_t waterDetectionValue = waterDetectionSensor.readRaw();
		// Serial.printf("Debug - waterSealTopValue: %d, waterDetectionValue: %d\n\n", waterSealTopValue, waterDetectionValue);
		// Serial.printf("Debug - pressureInit: %d\n\n", pressureSensor.init);

		// 深度控制调试信息
		// if (depthControlEnabled && pressureSensor.init)
		// {
		// Serial.print("\tTarget=%.2fm");
		// Serial.print(targetDepth);
		// Serial.print(" Current=%.2fm");
		// Serial.print(currentDepth);
		// Serial.print(" Error=%.3fm");
		// Serial.println(depthError);

		// Serial.print("depthControlEnabled Target=");
		// Serial.print(targetDepth);
		// Serial.print(" Current=");
		// Serial.print(currentDepth);
		// Serial.print(" Error=");
		// Serial.println(depthError);
		// }

		// 航向控制调试信息
		// if (headingControlEnabled)
		// {
		// Serial.print(" Heading Target=");
		// Serial.print(targetHeading);
		// Serial.print(" Current=");
		// Serial.print(currentHeading);
		// Serial.print(" Error=");
		// Serial.println(headingError);
		// }

		// 测试串口2状态（不发送测试数据，避免干扰协议）
		// Serial.println("Sending test byte on Serial2...");
		// Serial.write("T esp32 serial2");  // 发送测试字符

		// 检查Serial2状态
		// Serial.printf("Debug - Serial2 available bytes: %d\n\n", Serial.available());
	}

	// ledSwicher.On();
	// delay(2000);
	// ledSwicher.Off();
	// delay(1000);

	// 短暂延迟，避免CPU占用过高
	// delay(1);
}
