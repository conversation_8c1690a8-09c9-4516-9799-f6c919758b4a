#ifndef TOUCH_SENSOR_H
#define TOUCH_SENSOR_H

#include <Arduino.h>

class TouchSensor {
  private:
    uint8_t touchPin;          // 触摸引脚
    uint16_t threshold;        // 触摸阈值
    uint16_t noTouchValue;     // 未触摸时的基准值
    uint16_t samples;          // 采样次数
    bool calibrated;           // 是否已校准
    
  public:
    // 构造函数
    TouchSensor(uint8_t pin, uint16_t thresh = 0, uint16_t sampleCount = 1);
    
    // 初始化
    void begin();
    
    // 校准传感器（应在未触摸状态下调用）
    void calibrate();
    
    // 读取原始触摸值
    uint16_t readRaw();
    
    // 检查是否被触摸
    bool isTouched();
    
    // 设置触摸阈值
    void setThreshold(uint16_t thresh);
    
    // 获取当前阈值
    uint16_t getThreshold() const;
    
    // 获取基准值（未触摸时的值）
    uint16_t getNoTouchValue() const;
};

#endif
