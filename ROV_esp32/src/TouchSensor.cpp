#include "TouchSensor.h"

TouchSensor::TouchSensor(uint8_t pin, uint16_t thresh, uint16_t sampleCount) 
  : touchPin(pin), threshold(thresh), samples(sampleCount), calibrated(false) {
}

void TouchSensor::begin() {
  // 不需要特别初始化，但保留接口以便未来扩展
  if (!calibrated) {
    calibrate();
  }
}

void TouchSensor::calibrate() {
  noTouchValue = readRaw();
  calibrated = true;
  
  // 如果没有设置阈值，则自动设置为比基准值低10%
  if (threshold == 0) {
    threshold = noTouchValue * 0.9;
  }
}

uint16_t TouchSensor::readRaw() {
  uint32_t total = 0;
  
  for (uint16_t i = 0; i < samples; i++) {
    total += touchRead(touchPin);
    delay(1); // 短暂延迟
  }
  
  return total / samples;
}

bool TouchSensor::isTouched() {
  uint16_t currentValue = readRaw();
  return currentValue < threshold; // 值低于阈值表示被触摸
}

void TouchSensor::setThreshold(uint16_t thresh) {
  threshold = thresh;
}

uint16_t TouchSensor::getThreshold() const {
  return threshold;
}

uint16_t TouchSensor::getNoTouchValue() const {
  return noTouchValue;
}
