#include "WF5803F.h"

// ================= SimpleKalman实现 =================
SimpleKalman::SimpleKalman(float q, float r) : Q(q), R(r), P(1.0), K(0.0), X(0.0) {}

float SimpleKalman::update(float measurement) {
    P = P + Q;
    K = P / (P + R);
    X = X + K * (measurement - X);
    P = (1 - K) * P;
    return X;
}

// ================= WF5803F实现 =================
WF5803F::WF5803F(uint8_t sdaPin, uint8_t sclPin, uint8_t address) 
    : _sdaPin(sdaPin), _sclPin(sclPin), _address(address), _averageCount(3) {
    tempKalman = new SimpleKalman(0.01, 0.1);
    pressKalman = new SimpleKalman(0.01, 0.1);
}

WF5803F::~WF5803F() {
    delete tempKalman;
    delete pressKalman;
}

bool WF5803F::begin()
{
    Wire.begin(_sdaPin, _sclPin);
    Wire.setClock(400000);
    delay(100);

    Wire.beginTransmission(_address);
    byte error = Wire.endTransmission();

    if (error == 0)
    {
        writeRegister(0x30, 0x0B);

        init=true;
    }
    
    return (error == 0);
}

void WF5803F::setKalmanParams(float q, float r)
{
    delete tempKalman;
    delete pressKalman;
    tempKalman = new SimpleKalman(q, r);
    pressKalman = new SimpleKalman(q, r);
}

float WF5803F::readTemperatureKalman()
{
    return tempKalman->update(_readRawTemperature());
}

float WF5803F::readPressureKalman()
{
    float sum = 0;
    for (uint8_t i = 0; i < _averageCount; i++)
    {
        sum += _readRawPressure();
        // delay(5); // 短延迟
    }
    return pressKalman->update(sum / _averageCount);
}

void WF5803F::scanI2CDevices()
{
    byte error, address;
    int nDevices = 0;

    Serial.println("Scanning I2C devices...");

    for (address = 1; address < 127; address++)
    {
        Wire.beginTransmission(address);
        error = Wire.endTransmission();

        if (error == 0)
        {
            Serial.print("I2C device found at address 0x");
            if (address < 16)
                Serial.print("0");
            Serial.print(address, HEX);
            Serial.println();
            nDevices++;
        }
    }

    if (nDevices == 0)
    {
        Serial.println("No I2C devices found\n");
    }
}

float WF5803F::_readRawTemperature()
{
    Wire.beginTransmission(_address);
    Wire.write(0x09);            // 温度寄存器
    Wire.endTransmission(false); // Repeated start

    Wire.requestFrom(_address, 2);
    if (Wire.available() >= 2)
    {
        uint8_t msb = Wire.read();
        uint8_t lsb = Wire.read();

        long ad = (msb << 8) | lsb;

        if (ad > 32768)
        {
            return (ad - 65844) / 256.0f; // 零下温度 (3BAR)
        }
        else
        {
            return (ad - 308) / 256.0f; // 零上温度 (3BAR)
        }
    }
    return NAN;
}

float WF5803F::_readRawPressure()
{
    Wire.beginTransmission(_address);
    Wire.write(0x06);            // 压力寄存器
    Wire.endTransmission(false); // Repeated start

    Wire.requestFrom(_address, 3);
    if (Wire.available() >= 3)
    {
        uint8_t msb = Wire.read();
        uint8_t csb = Wire.read();
        uint8_t lsb = Wire.read();

        long dat = ((long)msb << 16) | ((long)csb << 8) | lsb;
        float fDat;

        if (dat > 8388608)
        {
            fDat = (int32_t)(dat - 16777216) / 8388608.0f;
        }
        else
        {
            fDat = dat / 8388608.0f;
        }

        return ((fDat * 1000 + 1200) / 3)+offset; // 3BAR转换公式
    }
    return NAN;
}

float WF5803F::readTemperature()
{
    return _readRawTemperature();
}

float WF5803F::readPressure()
{
    float total = 0;
    for (uint8_t i = 0; i < _averageCount; i++)
    {
        total += _readRawPressure();
        // delay(10);
    }
    return total / _averageCount;
}

void WF5803F::writeRegister(uint8_t reg, uint8_t value)
{
    Wire.beginTransmission(_address);
    Wire.write(reg);
    Wire.write(value);
    byte error = Wire.endTransmission();

    if (error != 0)
    {
        Serial.print("Write error: ");
        Serial.println(error);
    }
}

void WF5803F::setAverageCount(uint8_t count)
{
    _averageCount = max(1, (int)count); // 使用Arduino的max宏并显式转换类型
}

float WF5803F::readAltitude(float seaLevelhPa) {
    float pressure = readPressureKalman(); // 使用滤波后的压力值
    return _pressureToAltitude(pressure, seaLevelhPa);
}

// float WF5803F::_pressureToAltitude(float pressure, float seaLevel) {
//     // 使用国际民航组织(ICAO)标准大气公式       
//     // 压力单位应为hPa(百帕)，1kPa = 10hPa
    
//     // 确保压力值有效
//     if(pressure <= 0 || seaLevel <= 0) {
//         return NAN;
//     }
    
//     // 转换为hPa
//     pressure = pressure * 10.0f; // 假设原始压力单位为kPa
//     seaLevel = seaLevel;         // 假设输入已经是hPa
    
//     // 标准大气公式
//     return 44330.0f * (1.0f - pow(pressure / seaLevel, 0.1903f));
// }

float WF5803F::_pressureToAltitude(float pressure, float seaLevel) {
    // 确保压力值有效
    if(pressure <= 0 || seaLevel <= 0) {
        return NAN;
    }
    pressure=pressure*1000.0f;
    // 标准大气
    float atmospheric_pressure = 101325.0;
    float rho = 1000.0;
    float g = 9.80665; // 重力加速度(m/s²)
    return (pressure - atmospheric_pressure) / (rho * g);
}
