#include "MPU6050Sensor.h"

MPU6050Sensor::MPU6050Sensor() : mpu6050(Wire)
{
    // 构造函数初始化
}

void MPU6050Sensor::begin()
{
    Wire.begin(); // 初始化 I2C

    Wire.setClock(400000);

    mpu6050.begin();

    // mpu6050.calcGyroOffsets(true);  // 自动校准陀螺仪
}

void MPU6050Sensor::update()
{
    mpu6050.update(); // 更新传感器数据
}

float MPU6050Sensor::getAngleX()
{
    // return mpu6050.getAngleX();

    // 基于加速度计计算俯仰角（Pitch）和横滚角（Roll）（单位：度）。仅适用于静态或低速场景（受加速度噪声影响大）
    return mpu6050.getAccAngleX();
    
    // 基于陀螺仪积分计算角度（单位：度）存在累积误差（随时间漂移
    // return mpu6050.getGyroAngleX();

    // return mpu6050.getGyroX();
}

float MPU6050Sensor::getAngleY()
{
    // return mpu6050.getAngleY();
    return mpu6050.getAccAngleY();
    // return mpu6050.getGyroAngleY();

    // return mpu6050.getGyroY();
}

float MPU6050Sensor::getAngleZ()
{
    return mpu6050.getAngleZ();
    
    // return mpu6050.getGyroAngleY();

}

// 新增：校准陀螺仪偏移
void MPU6050Sensor::calcGyroOffsets(bool printOutput)
{
    mpu6050.calcGyroOffsets(printOutput,3000, 2000); // 调用底层库的校准方法
}
