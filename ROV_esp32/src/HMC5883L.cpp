#include "HMC5883L.h"

HMC5883L::HMC5883L() 
    : _declination(0.0f), 
      _x_offset(0.0f), _y_offset(0.0f),
      _x_scale(1.0f), _y_scale(1.0f),
      _calibrated(false),
      _high_speed(false),
      _i2c_freq(400000) {
}

bool HMC5883L::begin(uint8_t sda, uint8_t scl, uint32_t i2c_freq) {
    _i2c_freq = i2c_freq;
    Wire.begin(sda, scl);
    Wire.setClock(_i2c_freq);
    
    Wire.beginTransmission(HMC5883L_ADDR);
    if(Wire.endTransmission() != 0) {
        return false;
    }
    
    // 默认配置
    setDataRate(DR_75HZ);  // 设置为最高速率
    setRange(GA_1_3);
    setMeasurementMode(CONTINUOUS);
    
    return true;
}

void HMC5883L::setRange(HMC5883L_Range range) {
    writeRegister(HMC5883L_RA_CONFIG_B, range << 5);
}

void HMC5883L::setDataRate(HMC5883L_DataRate rate) {
    uint8_t config_a = readRegister(HMC5883L_RA_CONFIG_A);
    config_a &= 0xE3; // 清除速率位
    config_a |= (rate << 2);
    writeRegister(HMC5883L_RA_CONFIG_A, config_a);
    
    if(rate == DR_75HZ) {
        enableHighSpeedMode(true);
    } else {
        enableHighSpeedMode(false);
    }
}

void HMC5883L::enableHighSpeedMode(bool enable) {
    _high_speed = enable;
    if(enable) {
        Wire.setClock(800000); // 提高I2C时钟频率
        configureForHighSpeed();
    } else {
        Wire.setClock(_i2c_freq);
    }
}

void HMC5883L::configureForHighSpeed() {
    // 为高速模式优化配置
    writeRegister(HMC5883L_RA_CONFIG_A, 
                 (0x03 << 5) | // 8样本平均
                 (0x06 << 2) | // 75Hz数据速率
                 (0x00));      // 正常测量偏置
    
    writeRegister(HMC5883L_RA_MODE, 0x00); // 连续测量模式
}

void HMC5883L::setMeasurementMode(HMC5883L_Mode mode) {
    writeRegister(HMC5883L_RA_MODE, mode);
}

void HMC5883L::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(HMC5883L_ADDR);
    Wire.write(reg);
    Wire.write(value);
    Wire.endTransmission();
    
    // 75Hz模式需要更短的延迟
    if(!_high_speed) delay(5);
    else delayMicroseconds(200);
}

uint8_t HMC5883L::readRegister(uint8_t reg) {
    Wire.beginTransmission(HMC5883L_ADDR);
    Wire.write(reg);
    Wire.endTransmission();
    
    Wire.requestFrom(HMC5883L_ADDR, 1);
    return Wire.read();
}

MagnetometerData HMC5883L::read() {
    MagnetometerData data;
    data.timestamp = micros();
    
    Wire.beginTransmission(HMC5883L_ADDR);
    Wire.write(HMC5883L_RA_DATA);
    Wire.endTransmission();
    
    Wire.requestFrom(HMC5883L_ADDR, 6);
    if(Wire.available() == 6) {
        int16_t x = Wire.read() << 8 | Wire.read();
        int16_t z = Wire.read() << 8 | Wire.read();
        int16_t y = Wire.read() << 8 | Wire.read();
        
        // 应用校准
        if(_calibrated) {
            data.x = (x - _x_offset) * _x_scale;
            data.y = (y - _y_offset) * _y_scale;
        } else {
            data.x = x;
            data.y = y;
        }
        data.z = z;
        
        // 计算航向角
        data.heading = atan2(data.y, data.x) * 180.0f / M_PI + 180.0f + _declination;
        
        // 规范化到0-360度
        if(data.heading < 0) data.heading += 360.0f;
        if(data.heading > 360.0f) data.heading -= 360.0f;

        // 反向
        data.heading =360-data.heading;
    }
    
    return data;
}

void HMC5883L::calibrate(uint16_t samples) {
    int16_t x_min = 32767, x_max = -32768;
    int16_t y_min = 32767, y_max = -32768;
    
    // 临时降低速率以提高校准精度
    HMC5883L_DataRate old_rate = static_cast<HMC5883L_DataRate>((readRegister(HMC5883L_RA_CONFIG_A) >> 2) & 0x07);
    setDataRate(DR_15HZ);
    
    for(uint16_t i = 0; i < samples; i++) {
        MagnetometerData data = read();
        
        if(data.x < x_min) x_min = data.x;
        if(data.x > x_max) x_max = data.x;
        if(data.y < y_min) y_min = data.y;
        if(data.y > y_max) y_max = data.y;
        
        delay(10); // 校准期间适当延时
    }
    
    _x_offset = (x_max + x_min) / 2.0f;
    _y_offset = (y_max + y_min) / 2.0f;
    
    float x_range = x_max - x_min;
    float y_range = y_max - y_min;
    
    _x_scale = 1.0f / (x_range / 2.0f);
    _y_scale = 1.0f / (y_range / 2.0f);
    
    _calibrated = true;
    
    // 恢复原速率
    setDataRate(old_rate);
}

void HMC5883L::setDeclination(float declination) {
    _declination = declination;
}
