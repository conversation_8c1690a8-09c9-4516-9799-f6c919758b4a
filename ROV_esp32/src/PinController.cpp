#include "PinController.h"

PinController::PinController(uint8_t pin, bool initialState) 
    : pinNumber(pin), currentState(initialState) {
    pinMode(pinNumber, OUTPUT);
    digitalWrite(pinNumber, initialState ? HIGH : LOW);
}

void PinController::On() {
    digitalWrite(pinNumber, HIGH);
    currentState = true;
}

void PinController::Off() {
    digitalWrite(pinNumber, LOW);
    currentState = false;
}

bool PinController::getState() const {
    return currentState;
}
