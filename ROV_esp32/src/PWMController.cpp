#include "PWMController.h"

PWMController::PWMController(uint8_t pin1,uint8_t pin2,uint8_t pin3,uint8_t pin4,uint8_t pin5,uint8_t pin6) {
    // 初始化通道配置 - 脉冲宽度使用uint16_t
    channels[0] = {pin1, Servo(), false, 50, 1000, 2000};
    channels[1] = {pin2, Servo(), false, 50, 1000, 2000};
    channels[2] = {pin3, Servo(), false, 50, 1000, 2000};
    channels[3] = {pin4, Servo(), false, 50, 1000, 2000};
    channels[4] = {pin5, Servo(), false, 50, 1000, 2000};
    channels[5] = {pin6, Servo(), false, 50, 1000, 2000};
}
PWMController::PWMController(uint8_t pin1,uint8_t pin2,uint8_t pin3,uint8_t pin4,uint8_t pin5) {
    // 初始化通道配置 - 脉冲宽度使用uint16_t
    channels[0] = {pin1, Servo(), false, 50, 1000, 2000};
    channels[1] = {pin2, Servo(), false, 50, 1000, 2000};
    channels[2] = {pin3, Servo(), false, 50, 1000, 2000};
    channels[3] = {pin4, Servo(), false, 50, 1000, 2000};
    channels[4] = {pin5, Servo(), false, 50, 1000, 2000};
}

void PWMController::begin() {
    // 允许ESP32Servo库分配定时器
    ESP32PWM::allocateTimer(0);
    ESP32PWM::allocateTimer(1);
    ESP32PWM::allocateTimer(2);
    ESP32PWM::allocateTimer(3);
    
    // 初始化所有通道
    for (int i = 0; i < 6; i++) {
        channels[i].servo.setPeriodHertz(channels[i].frequency);
        channels[i].servo.attach(channels[i].pin, 
                               channels[i].minPulseWidth, 
                               channels[i].maxPulseWidth);
        channels[i].servo.write(90); // 初始中间位置
        channels[i].isActive = true;
    }
}

void PWMController::setPWM(uint8_t channel, uint32_t frequency, uint16_t minPulseWidth, uint16_t maxPulseWidth, uint8_t value) {
    if (channel >= 6) return;
    
    // 停止当前通道
    if (channels[channel].isActive) {
        channels[channel].servo.detach();
    }
    
    // 更新配置
    channels[channel].frequency = frequency;
    channels[channel].minPulseWidth = minPulseWidth;
    channels[channel].maxPulseWidth = maxPulseWidth;
    
    // 重新配置
    channels[channel].servo.setPeriodHertz(frequency);
    channels[channel].servo.attach(channels[channel].pin, 
                                 minPulseWidth, 
                                 maxPulseWidth);
    channels[channel].servo.write(value);
    channels[channel].isActive = true;
}

void PWMController::updateValue(uint8_t channel, uint8_t value) {
    if (channel >= 6 || !channels[channel].isActive) return;
    
    channels[channel].servo.write(value);
}

void PWMController::stop(uint8_t channel) {
    if (channel >= 6 || !channels[channel].isActive) return;
    
    channels[channel].servo.detach();
    channels[channel].isActive = false;
}

void PWMController::resume(uint8_t channel) {
    if (channel >= 6 || channels[channel].isActive) return;
    
    channels[channel].servo.setPeriodHertz(channels[channel].frequency);
    channels[channel].servo.attach(channels[channel].pin, 
                                 channels[channel].minPulseWidth, 
                                 channels[channel].maxPulseWidth);
    channels[channel].isActive = true;
}
