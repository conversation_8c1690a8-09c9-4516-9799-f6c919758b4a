#include "Serial2Class.h"

Serial2Class::Serial2Class(int rxPin, int txPin) : _rxPin(rxPin), _txPin(txPin), serialCommon(nullptr)
{
// #ifdef USE_SERIAL2
    serialCommon = &Serial2;
// #else
    // serialCommon = &Serial;
// #endif
}

void Serial2Class::begin(unsigned long baudrate)
{
    if (_rxPin != -1 && _txPin != -1)
    {
        serialCommon->begin(baudrate, SERIAL_8N1, _rxPin, _txPin);
    }
    else
    {
        serialCommon->begin(baudrate);
    }
}

bool Serial2Class::available()
{
    return serialCommon->available();
}

char Serial2Class::read()
{
    return serialCommon->read();
}

String Serial2Class::readStringUntil(char terminator, unsigned int timeout)
{
    String result;
    unsigned long startTime = millis();

    while (millis() - startTime < timeout)
    {
        if (serialCommon->available())
        {
            char c = serialCommon->read();
            if (c == terminator)
            {
                return result;
            }
            result += c;
        }
    }
    return result;
}

void Serial2Class::write(const char *data)
{
    serialCommon->write(data);
}

void Serial2Class::write(String data)
{
    serialCommon->write(data.c_str());
}

void Serial2Class::write(uint8_t data)
{
    serialCommon->write(data);
}

void Serial2Class::setRxBufferSize(size_t size)
{
    serialCommon->setRxBufferSize(size);
}

void Serial2Class::writeBytes(const uint8_t *data, size_t length)
{
    serialCommon->write(data, length);
}
String Serial2Class::readHex()
{
    String hexString;
    if (serialCommon->available())
    {
        uint8_t byte = serialCommon->read();
        hexString = "0x";
        if (byte < 0x10)
            hexString += "0";
        hexString += String(byte, HEX);
    }
    return hexString;
}

String Serial2Class::readHex(size_t length)
{
    String hexString;
    for (size_t i = 0; i < length && serialCommon->available(); i++)
    {
        uint8_t byte = serialCommon->read();
        if (i > 0)
            hexString += " ";
        hexString += "0x";
        if (byte < 0x10)
            hexString += "0";
        hexString += String(byte, HEX);
    }
    return hexString;
}

String Serial2Class::readHexLine(unsigned int timeout)
{
    String hexString;
    unsigned long startTime = millis();

    while (millis() - startTime < timeout)
    {
        if (serialCommon->available())
        {
            uint8_t byte = serialCommon->read();
            if (!hexString.isEmpty())
                hexString += " ";
            hexString += "0x";
            if (byte < 0x10)
                hexString += "0";
            hexString += String(byte, HEX);

            if (byte == '\n')
            { // 换行符结束
                break;
            }
        }
    }
    return hexString;
}
