#include "ADC.h"

ADC::ADC(uint8_t pin, float voltageDivider, adc_atten_t attenuation)
    : _pin(pin), _voltageDivider(voltageDivider), _attenuation(attenuation),
      _calibrationOffset(0.0), _calibrationScale(1.0) {
    _channel = pinToChannel(pin);
}

void ADC::begin() {
    adc1_config_width(ADC_WIDTH_BIT_12);
    adc1_config_channel_atten(_channel, _attenuation);
}

int ADC::readRaw() {
    return adc1_get_raw(_channel);
}

float ADC::calculateVoltage(int rawValue) {
    // 将原始值转换为分压后的电压(V)
    // float stepV1=3.3; //8.4V
    float stepV2=12.086; //25.2V

    float voltageAfterDivider = (rawValue * stepV2) / 4095.0;
    
    // 计算分压前的实际电压(V)
    return voltageAfterDivider / _voltageDivider;
}

float ADC::readVoltage() {
    int raw = readRaw();
    return calculateVoltage(raw);
}

void ADC::setVoltageDivider(float ratio) {
    _voltageDivider = ratio;
}

void ADC::setAttenuation(adc_atten_t attenuation) {
    _attenuation = attenuation;
    adc1_config_channel_atten(_channel, _attenuation);
}

adc1_channel_t ADC::pinToChannel(uint8_t pin) {
    switch(pin) {
        case 36: return ADC1_CHANNEL_0;
        case 37: return ADC1_CHANNEL_1;
        case 38: return ADC1_CHANNEL_2;
        case 39: return ADC1_CHANNEL_3;
        case 32: return ADC1_CHANNEL_4;
        case 33: return ADC1_CHANNEL_5;
        case 34: return ADC1_CHANNEL_6;
        case 35: return ADC1_CHANNEL_7;
        default:
            Serial.printf("Error: Pin %d is not ADC1 compatible\n", pin);
            return ADC1_CHANNEL_MAX;
    }
}

// ADC校准功能实现
void ADC::setCalibration(float offset, float scale) {
    _calibrationOffset = offset;
    _calibrationScale = scale;
    // Serial.printf("ADC校准设置: 偏移=%.3fV, 比例=%.6f\n", offset, scale);
}

void ADC::calibrateWithReference(float measuredVoltage, float referenceVoltage) {
    // 计算校准参数
    // 校准公式: 校准电压 = (测量电压 * 比例) + 偏移
    // 为了简化，我们主要使用偏移校准
    _calibrationOffset = referenceVoltage - measuredVoltage;
    _calibrationScale = 1.0; // 保持比例为1，主要使用偏移校准

    Serial.printf("ADC自动校准完成:\n");
    Serial.printf("  测量值: %.3fV\n", measuredVoltage);
    Serial.printf("  参考值: %.3fV\n", referenceVoltage);
    Serial.printf("  偏移校准: %.3fV\n", _calibrationOffset);
    Serial.printf("  比例校准: %.6f\n", _calibrationScale);
}

float ADC::getCalibrationOffset() const {
    return _calibrationOffset;
}

float ADC::getCalibrationScale() const {
    return _calibrationScale;
}

float ADC::readCalibratedVoltage() {
    float rawVoltage = readVoltage();
    float calibratedVoltage = (rawVoltage * _calibrationScale) + _calibrationOffset;

    // [ ]ADC建议校准
    calibratedVoltage=calibratedVoltage+0.8;
    return calibratedVoltage;
}
