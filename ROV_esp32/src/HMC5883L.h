#ifndef HMC5883L_H
#define HMC5883L_H

#include <Wire.h>
#include <math.h>

// HMC5883L寄存器定义
#define HMC5883L_ADDR 0x1E
#define HMC5883L_RA_CONFIG_A 0x00
#define HMC5883L_RA_CONFIG_B 0x01
#define HMC5883L_RA_MODE 0x02
#define HMC5883L_RA_DATA 0x03

// 测量范围定义
enum HMC5883L_Range {
    GA_0_88 = 0x00,
    GA_1_3 = 0x01,
    GA_1_9 = 0x02,
    GA_2_5 = 0x03,
    GA_4_0 = 0x04,
    GA_4_7 = 0x05,
    GA_5_6 = 0x06,
    GA_8_1 = 0x07
};

// 数据输出速率定义
enum HMC5883L_DataRate {
    DR_0_75HZ = 0x00,
    DR_1_5HZ = 0x01,
    DR_3HZ = 0x02,
    DR_7_5HZ = 0x03,
    DR_15HZ = 0x04,  // 默认值
    DR_30HZ = 0x05,
    DR_75HZ = 0x06   // 最高速率
};

// 测量模式定义
enum HMC5883L_Mode {
    CONTINUOUS = 0x00,
    SINGLE = 0x01,
    IDLE = 0x02
};

// 数据结构体
struct MagnetometerData {
    float x;
    float y;
    float z;
    float heading;
    unsigned long timestamp;
};

class HMC5883L {
public:
    HMC5883L();
    bool begin(uint8_t sda = 21, uint8_t scl = 22, uint32_t i2c_freq = 400000);
    void setRange(HMC5883L_Range range);
    void setDataRate(HMC5883L_DataRate rate);
    void setMeasurementMode(HMC5883L_Mode mode);
    MagnetometerData read();
    void calibrate(uint16_t samples = 500);
    void setDeclination(float declination);
    void enableHighSpeedMode(bool enable);
    
private:
    float _declination;
    float _x_offset, _y_offset;
    float _x_scale, _y_scale;
    bool _calibrated;
    bool _high_speed;
    uint32_t _i2c_freq;
    
    void writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    void configureForHighSpeed();
};

#endif
