#ifndef MPU6050_SENSOR_H
#define MPU6050_SENSOR_H

#include <Wire.h>
#include <MPU6050_tockn.h>

class MPU6050Sensor
{
public:
    MPU6050Sensor();   // 构造函数
    void begin();      // 初始化传感器
    void update();     // 更新传感器数据
    float getAngleX(); // 获取X轴角度
    float getAngleY(); // 获取Y轴角度
    float getAngleZ(); // 获取Z轴角度

    void calcGyroOffsets(bool printOutput = false); // 新增：校准陀螺仪偏移

private:
    MPU6050 mpu6050; // MPU6050 对象
};

#endif
