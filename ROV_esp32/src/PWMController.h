#ifndef PWMCONTROLLER_H
#define PWMCONTROLLER_H

#include <Arduino.h>
#include <ESP32Servo.h>

class PWMController {
public:
    // 构造函数
    PWMController(uint8_t pin1,uint8_t pin2,uint8_t pin3,uint8_t pin4,uint8_t pin5,uint8_t pin6);
    PWMController(uint8_t pin1,uint8_t pin2,uint8_t pin3,uint8_t pin4,uint8_t pin5);
    
    // 初始化所有PWM通道
    void begin();
    
    // 设置指定通道的PWM参数
    void setPWM(uint8_t channel, uint32_t frequency, uint16_t minPulseWidth, uint16_t maxPulseWidth, uint8_t value);
    
    // 更新指定通道的值
    void updateValue(uint8_t channel, uint8_t value);
    
    // 停止指定通道的PWM输出
    void stop(uint8_t channel);
    
    // 恢复指定通道的PWM输出
    void resume(uint8_t channel);

private:
    // PWM通道配置
    struct PWMChannel {
        uint8_t pin;
        Servo servo;
        bool isActive;
        uint32_t frequency;
        uint16_t minPulseWidth;  // 改为uint16_t
        uint16_t maxPulseWidth;  // 改为uint16_t
    };
    
    PWMChannel channels[6];
};

#endif
