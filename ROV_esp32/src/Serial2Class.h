#ifndef SERIAL2_CLASS_H
#define SERIAL2_CLASS_H

#include <HardwareSerial.h>

class Serial2Class
{
public:
    // 构造函数，可以指定引脚
    Serial2Class(int rxPin = -1, int txPin = -1);

    // 初始化串口2
    void begin(unsigned long baudrate);

    // 检查是否有数据可读
    bool available();

    // 读取一个字节
    char read();

    // 新增：读取字节并返回HEX字符串
    String readHex();

    // 新增：读取指定长度数据并返回HEX字符串
    String readHex(size_t length);

    // 新增：读取一行数据并返回HEX字符串
    String readHexLine(unsigned int timeout = 1000);

    // 读取字符串直到遇到终止符或超时
    String readStringUntil(char terminator, unsigned int timeout = 1000);

    // 写入数据
    void write(const char *data);
    void write(String data);
    void write(uint8_t data);

    // 新增：发送原始字节数组
    void writeBytes(const uint8_t *data, size_t length);

    HardwareSerial* serialCommon;

    // 打印数据（支持多种格式）
    template <typename T>
    void print(T data)
    {
        serialCommon->print(data);
    }

    // 打印数据并换行
    template <typename T>
    void println(T data)
    {
        serialCommon->println(data);
    }

    // 设置接收缓冲区大小
    void setRxBufferSize(size_t size);

private:
    int _rxPin;
    int _txPin;
};

#endif
