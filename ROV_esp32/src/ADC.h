#ifndef ADC_H
#define ADC_H

#include <Arduino.h>
#include <driver/adc.h>

class ADC {
public:
    // 构造函数: 引脚, 分压比(默认1/3对应9V量程), 衰减(默认11dB)
    ADC(uint8_t pin, float voltageDivider = 0.3333, adc_atten_t attenuation = ADC_ATTEN_DB_11);
    
    // 初始化ADC
    void begin();
    
    // 读取原始ADC值(0-4095)
    int readRaw();
    
    // 读取分压前的实际电压(V)
    float readVoltage();
    
    // 设置自定义分压比
    void setVoltageDivider(float ratio);
    
    // 设置衰减
    void setAttenuation(adc_atten_t attenuation);

    // ADC校准功能
    void setCalibration(float offset, float scale = 1.0);
    void calibrateWithReference(float measuredVoltage, float referenceVoltage);
    float getCalibrationOffset() const;
    float getCalibrationScale() const;

    // 读取校准后的电压
    float readCalibratedVoltage();

private:
    uint8_t _pin;
    float _voltageDivider;
    adc_atten_t _attenuation;
    adc1_channel_t _channel;

    // 校准参数
    float _calibrationOffset;  // 电压偏移校准
    float _calibrationScale;   // 电压比例校准
    
    // 将引脚号转换为ADC通道
    adc1_channel_t pinToChannel(uint8_t pin);
    
    // 计算实际电压
    float calculateVoltage(int rawValue);
};

#endif
