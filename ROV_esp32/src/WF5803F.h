#ifndef WF5803F_H
#define WF5803F_H

#include <Wire.h>
#include <Arduino.h>
#include <cmath>

class SimpleKalman {
private:
    float Q; // 过程噪声协方差
    float R; // 测量噪声协方差
    float P; // 估计误差协方差
    float K; // 卡尔曼增益
    float X; // 估计值
    
public:
    SimpleKalman(float q, float r);
    float update(float measurement);
};

class WF5803F {
public:
    WF5803F(uint8_t sdaPin = 21, uint8_t sclPin = 22, uint8_t address = 0x6D);
    ~WF5803F();

    bool init=false;
    
    bool begin();
    void scanI2CDevices();
    
    // 千帕
    // float offset=-4.24;
    float offset=0;

    // 基础读取方法
    float readTemperature();
    float readPressure();
    
    // 滤波读取方法
    float readTemperatureKalman();
    float readPressureKalman();
    
    // 海拔高度计算
    float readAltitude(float seaLevelhPa = 1013.25); // 默认海平面气压
    
    // 配置方法
    void setKalmanParams(float q, float r);
    void setAverageCount(uint8_t count);
    void writeRegister(uint8_t reg, uint8_t value);

private:
    uint8_t _sdaPin;
    uint8_t _sclPin;
    uint8_t _address;
    uint8_t _averageCount;
    
    SimpleKalman* tempKalman;
    SimpleKalman* pressKalman;
    
    float _readRawTemperature();
    float _readRawPressure();
    
    // 气压转海拔辅助函数
    float _pressureToAltitude(float pressure, float seaLevel);
};

#endif
