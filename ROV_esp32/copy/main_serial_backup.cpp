// 备用方案：使用Serial代替Serial2进行通信
// 如果Serial2有问题，可以用这个版本替换main.cpp中的相关部分

// 在全局变量部分替换：
// Serial2Class mySerial2(16, 17); 
// 改为：
// #define mySerial2 Serial

// 在serial2Init函数中替换：
void serial2Init_backup()
{
	Serial.println("Using Serial for communication (backup mode)");
	Serial.println("Serial ready for protocol communication");
	
	// 不需要额外初始化，Serial已经在setup中初始化
}

// 在handleSerialData函数中替换：
void handleSerialData_backup()
{
	static unsigned long lastDataTime = 0;
	
	while (Serial.available())
	{
		uint8_t receivedByte = Serial.read();

		// 注意：这里不能用Serial.printf，因为会干扰通信
		// 可以用LED或其他方式指示收到数据
		
		lastDataTime = millis();

		// 防止缓冲区溢出
		if (rxIndex >= sizeof(rxBuffer))
		{
			rxIndex = 0;
		}

		rxBuffer[rxIndex++] = receivedByte;

		// 尝试解析帧
		parseFrame();
	}
}

// 使用说明：
// 1. 将 Serial2Class mySerial2(16, 17); 改为 #define mySerial2 Serial
// 2. 将 serial2Init() 改为 serial2Init_backup()
// 3. 将 handleSerialData() 改为 handleSerialData_backup()
// 4. 注释掉所有 Serial.print 语句，避免干扰通信
// 5. 重新编译上传
