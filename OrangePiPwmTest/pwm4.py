import time

PWM_CHIP = 2
PWM_CHANNEL = 0  # PWM通道号 0
PWM_PATH = f"/sys/class/pwm/pwmchip{PWM_CHIP}"

def close():
    cmd=f"{PWM_PATH}/pwm0/enable"
    print(cmd)
    # 禁用PWM
    with open(cmd, "w") as f:
        f.write("0")

    # 取消导出PWM
    cmd=f"{PWM_PATH}/unexport"
    print(cmd)
    with open(cmd, "w") as f:
        f.write(str(PWM_CHANNEL))

def set_pulse_width(us):
    with open(f"{PWM_PATH}/pwm0/duty_cycle", "w") as f:
        f.write(str(us * 1000))  # 转换为纳秒

def start():
    # # 导出PWM
    cmd=f"{PWM_PATH}/export"
    print(cmd)
    with open(cmd, "w") as f:
        f.write(str(PWM_CHANNEL))

    try:
        while True:
            set_pulse_width(1000)  # 1ms
            time.sleep(1)
            set_pulse_width(1500)  # 1.5ms
            time.sleep(1)
            set_pulse_width(2000)  # 2ms
            time.sleep(1)

    except KeyboardInterrupt:
        with open(f"{PWM_PATH}/enable", "w") as f:
            f.write("0")  # 关闭 PWM

# close()
start()
