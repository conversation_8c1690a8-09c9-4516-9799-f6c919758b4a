import wiringpi
import time

# 软pwm 不稳定

# 初始化
wiringpi.wiringPiSetup()

# 设置 PWM 引脚 (使用 WiringPi 引脚编号)
pwm_pin = 2  # GPIO18, WiringPi 编号 1  blue
pwm_pin2 = 21  # GPIO18, WiringPi 编号 1   green

# 创建软件 PWM
# wiringpi.softPwmCreate(pwm_pin, 0, 100)  # 引脚, 初始值, 范围(100=100Hz)
wiringpi.softPwmCreate(pwm_pin2, 0, 100)  # 引脚, 初始值, 范围(100=100Hz)

try:
    while True:
        # wiringpi.softPwmWrite(pwm_pin, 50)
        wiringpi.softPwmWrite(pwm_pin2, 70)


except KeyboardInterrupt:
    # wiringpi.softPwmWrite(pwm_pin, 0)  # 停止 PWM
    wiringpi.softPwmWrite(pwm_pin2, 0)  # 停止 PWM
