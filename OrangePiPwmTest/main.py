import os
import time
import inputs
from inputs import get_gamepad

# 线轴，Z轴可以转

class PWMController:
    def __init__(self, pwm_chip=0, channel=0):
        print("初始化PWM控制器")
        self.pwm_path = f"/sys/class/pwm/pwmchip{pwm_chip}/pwm{channel}"
        self._setup_pwm()
    
    def _setup_pwm(self):
        print("配置PWM参数")
        try:
            with open(f"{self.pwm_path.replace('pwm0', 'unexport')}", "w") as f:
                f.write("0")
        except:
            pass
        
        with open(f"{self.pwm_path.replace('pwm0', 'export')}", "w") as f:
            f.write("0")
        time.sleep(0.2)
        
        # 设置50Hz频率(周期20ms)
        with open(f"{self.pwm_path}/period", "w") as f:
            f.write("20000000")  # 20ms in ns
        
        # 初始1.5ms脉宽
        self.set_pulse_width(1500)
        
        # 启用PWM
        with open(f"{self.pwm_path}/enable", "w") as f:
            f.write("1")
    
    def set_pulse_width(self, us):
         # 读取当前周期
        with open(f"{self.pwm_path}/period", "r") as f:
            period_ns = int(f.read().strip())
        
        # 计算占空比纳秒值
        duty_ns = int(us * 1000)
        duty_ns = period_ns - duty_ns

        # print("设置脉冲宽度(微秒)")
        # duty_ns = us * 1000  # 转换为纳秒
        with open(f"{self.pwm_path}/duty_cycle", "w") as f:
            f.write(str(duty_ns))
    
    def cleanup(self):
        print("清理资源")
        with open(f"{self.pwm_path}/enable", "w") as f:
            f.write("0")
        with open(f"{self.pwm_path.replace('pwm0', 'unexport')}", "w") as f:
            f.write("0")

class XboxController:
    def __init__(self):
        """初始化手柄控制器"""
        # PWM1控制变量(扳机键)
        self.left_trigger = 0  # 0-255
        self.right_trigger = 0  # 0-255
        
        # PWM2控制变量(右摇杆)
        self.right_stick_x = 0  # -32768到32767
        self.pwm2_value = 1500  # 初始1.5ms
        self.deadzone = 0   # 摇杆死区
    
    def get_controls(self):
        """获取手柄输入状态并转换为PWM值"""
        events = get_gamepad()
        for event in events:
            # 扳机键控制PWM1
            if event.code == "ABS_Z":  # 左扳机键
                self.left_trigger = event.state
            elif event.code == "ABS_RZ":  # 右扳机键
                self.right_trigger = event.state
            
            # 右摇杆控制PWM2
            elif event.code == "ABS_RX":  # 右摇杆左右轴
                self.right_stick_x = event.state
        
        # PWM1计算: 右扳机增加,左扳机减少 (范围1000-2000μs)
        pwm1 = 1500 + (self.right_trigger - self.left_trigger) * (1000/255)
        pwm1 = max(1000, min(2000, pwm1))  # 限制范围
        
        # PWM2计算: 右摇杆左右控制
        if abs(self.right_stick_x) > self.deadzone:
            self.pwm2_value = 1000 + (self.right_stick_x / 255) * 1000
        
        print(self.left_trigger,self.right_trigger,self.right_stick_x)
        
        return pwm1, self.pwm2_value

# 主程序
if __name__ == "__main__":
   # 初始化两路PWM
    pwm1 = PWMController(pwm_chip=1, channel=0)  # 第一路PWM
    pwm2 = PWMController(pwm_chip=2, channel=0)  # 第二路PWM
        
    try:
        xbox = XboxController()
        
        print("Xbox控制器PWM控制已启动")
        print("控制方式:")
        print(" - 左/右扳机键控制PWM1(模拟量)")
        print(" - 右摇杆左右控制PWM2(模拟量)")
        print("按Ctrl+C退出")
        
        while True:
            # 左，右扳机控制卷线
            # 右摇杆左右控制Z轴转
            pulse1, pulse2 = xbox.get_controls()
            
            # 更新PWM输出
            pwm1.set_pulse_width(int(pulse1))
            pwm2.set_pulse_width(int(pulse2))
            
            # 显示当前值
            print(f"\rPWM1: {pulse1:.0f}μs | PWM2: {pulse2:.0f}μs", end="", flush=True)
            # time.sleep(0.02)
            
    except (FileNotFoundError, PermissionError, OSError,KeyboardInterrupt):
        print("\n程序终止")
    except inputs.UnpluggedError:
        print("错误：未检测到Xbox控制器")
    finally:
        pwm1.cleanup()
        pwm2.cleanup()
