import os
import time
# 可以使用
class DualPWMController:
    def __init__(self, pwm_chips=(1, 2)):
        """初始化双PWM控制器
        
        Args:
            pwm_chips: 使用的两个PWM控制器编号(通常1和2)
        """
        self.pwm_chips = pwm_chips
        self.channel = 0  # 固定使用每个控制器的通道0
        
        # 初始化两个PWM控制器
        for chip in pwm_chips:
            self._release_pwm(chip, self.channel)
            self._export_pwm(chip, self.channel)
            time.sleep(0.1)  # 等待设备创建
    
    def _export_pwm(self, chip, channel):
        """导出PWM通道"""
        with open(f"/sys/class/pwm/pwmchip{chip}/export", "w") as f:
            f.write(str(channel))
    
    def _release_pwm(self, chip, channel):
        """释放PWM通道"""
        try:
            cmd=f"/sys/class/pwm/pwmchip{chip}/unexport"
            print(cmd)
            with open(cmd, "w") as f:
                f.write(str(channel))
        except (FileNotFoundError, PermissionError, OSError):
            pass

    def setup_pwm(self, pwm_id, freq_hz=50, duty_us=1500, invert=False):
        """配置单个PWM控制器
        
        Args:
            pwm_id: 0或1，指定要配置的PWM控制器(对应pwm_chips中的索引)
            freq_hz: PWM频率(Hz)
            duty_us: 脉冲宽度(微秒)
            invert: 是否反转电平极性
        """
        chip = self.pwm_chips[pwm_id]
        path = f"/sys/class/pwm/pwmchip{chip}/pwm{self.channel}"
        
        # 设置周期(频率)
        period_ns = int(1e9 / freq_hz)
        print(f"{path}/period")
        print(period_ns)
        with open(f"{path}/period", "w") as f:
            f.write(str(period_ns))
        
        # 设置脉宽
        self.set_duty(pwm_id, duty_us, invert)
        
        # 启用PWM
        with open(f"{path}/enable", "w") as f:
            f.write("1")
    
    def set_duty(self, pwm_id, duty_us, invert=False):
        """设置单个PWM控制器的脉宽
        
        Args:
            pwm_id: 0或1，指定要设置的PWM控制器
            duty_us: 脉冲宽度(微秒)
            invert: 是否反转电平极性
        """
        chip = self.pwm_chips[pwm_id]
        path = f"/sys/class/pwm/pwmchip{chip}/pwm{self.channel}"
        
        # 读取当前周期
        with open(f"{path}/period", "r") as f:
            period_ns = int(f.read().strip())
        
        # 计算占空比纳秒值
        duty_ns = int(duty_us * 1000)
        if invert:
            duty_ns = period_ns - duty_ns
        
        # 写入占空比
        with open(f"{path}/duty_cycle", "w") as f:
            f.write(str(duty_ns))
    
    def disable_pwm(self, pwm_id):
        """禁用指定PWM控制器"""
        chip = self.pwm_chips[pwm_id]
        path = f"/sys/class/pwm/pwmchip{chip}/pwm{self.channel}"
        with open(f"{path}/enable", "w") as f:
            f.write("0")
    
    def cleanup(self):
        """清理所有PWM资源"""
        for chip in self.pwm_chips:
            self.disable_pwm(self.pwm_chips.index(chip))
            self._release_pwm(chip, self.channel)

# 使用示例
if __name__ == "__main__":
    # 初始化双PWM控制器(pwm_chip1和pwm_chip2的通道0)
    pwm = DualPWMController(pwm_chips=(1, 2))
    
    try:
        # 独立配置两路PWM
        pwm.setup_pwm(0, freq_hz=50, duty_us=1500, invert=True)    # PWM控制器1: 50Hz, 1ms
        pwm.setup_pwm(1, freq_hz=50, duty_us=1500, invert=True)  
        
        # 独立操作两路PWM
        for i in range(1000):
            # PWM控制器1在1ms和2ms之间交替
            width_a = 1000 if i % 2 == 0 else 2000
            pwm.set_duty(0, width_a, invert=True)
            
            # PWM控制器2在0.5ms和1.5ms之间交替
            width_b = 500 if i % 3 == 0 else 1500
            pwm.set_duty(1, width_b, invert=True)
            
            time.sleep(1)
            
    except (KeyboardInterrupt):
        print("\n程序终止")
    finally:
        pwm.cleanup()
