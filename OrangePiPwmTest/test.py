import time
import os

# 可以使用

PWM_CHIP = 1
PWM_CHANNEL = 0
PWM_PATH = f"/sys/class/pwm/pwmchip{PWM_CHIP}/pwm{PWM_CHANNEL}"
PERIOD_NS = 20000000  # 50Hz (20ms)

def setup_pwm(invert=False):
    try:
        # 取消导出（如果已存在）
        cmd=f"/sys/class/pwm/pwmchip{PWM_CHIP}/unexport"
        print(cmd)
        with open(cmd, "w") as f:
            f.write(str(PWM_CHANNEL))
    except (FileNotFoundError, PermissionError, OSError):
        pass
    
    # 导出PWM通道
    with open(f"/sys/class/pwm/pwmchip{PWM_CHIP}/export", "w") as f:
        f.write(str(PWM_CHANNEL))
    
    time.sleep(0.1)  # 等待设备创建
    
    # 设置周期
    with open(f"{PWM_PATH}/period", "w") as f:
        f.write(str(PERIOD_NS))
    
    # 初始占空比（根据是否反转设置）
    initial_duty = PERIOD_NS - 1500000 if invert else 1500000  # 1.5ms
    with open(f"{PWM_PATH}/duty_cycle", "w") as f:
        f.write(str(initial_duty))
    
    # 启用PWM
    with open(f"{PWM_PATH}/enable", "w") as f:
        f.write("1")
    
    return invert

def set_pulse_width(us, invert=False):
    duty_ns = us * 1000  # 微秒转纳秒
    if invert:
        duty_ns = PERIOD_NS - duty_ns  # 电平反转计算
        
    with open(f"{PWM_PATH}/duty_cycle", "w") as f:
        f.write(str(duty_ns))

# 使用示例
try:
    invert_mode = setup_pwm(invert=True)  # True表示电平反转
    
    while True:
        set_pulse_width(1000, invert_mode)  # 1ms脉冲（反转后）
        time.sleep(1)
        set_pulse_width(1500, invert_mode)  # 1.5ms脉冲
        time.sleep(1)
        set_pulse_width(2000, invert_mode)  # 2ms脉冲
        time.sleep(1)

except KeyboardInterrupt:
    with open(f"{PWM_PATH}/enable", "w") as f:
        f.write("0")
    with open(f"/sys/class/pwm/pwmchip{PWM_CHIP}/unexport", "w") as f:
        f.write(str(PWM_CHANNEL))
