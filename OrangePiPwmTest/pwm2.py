import time

# 直接操作

# 设置PWM通道 (根据你的香橙派型号和使用的引脚)
PWM_CHIP = 1     # PWM芯片号 2
PWM_CHANNEL = 0  # PWM通道号 0
PWM_PATH = f"/sys/class/pwm/pwmchip{PWM_CHIP}/pwm{PWM_CHANNEL}"

# 导出PWM
cmd=f"/sys/class/pwm/pwmchip{PWM_CHIP}/export"
print(cmd)
with open(cmd, "w") as f:
    f.write(str(PWM_CHANNEL))

# 设置参数
period_ns = 20000000  # 周期20ms (50Hz)
duty_ns =   10000000    # 脉宽1.5ms (7.5%)

try:
    while 1:
        # 设置周期和占空比
        with open(f"{PWM_PATH}/period", "w") as f:
            f.write(str(period_ns))
        
        with open(f"{PWM_PATH}/duty_cycle", "w") as f:
            f.write(str(duty_ns))
        
        # 启用PWM
        with open(f"{PWM_PATH}/enable", "w") as f:
            f.write("1")

finally:
    # 禁用PWM
    with open(f"{PWM_PATH}/enable", "w") as f:
        f.write("0")
    
    # 取消导出PWM
    with open(f"/sys/class/pwm/pwmchip{PWM_CHIP}/unexport", "w") as f:
        f.write(str(PWM_CHANNEL))
