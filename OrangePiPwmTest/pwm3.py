import OPi.GPIO as GPIO
import time

# 不可用

# 设置 PWM 引脚（香橙派3B 的硬件PWM引脚，如 PA6 = GPIO6）
PWM_PIN = 32  # 使用 BCM 编号（可通过 `gpio readall` 查询）

# 初始化
GPIO.setmode(GPIO.BOARD)  # 使用物理引脚编号
GPIO.setup(PWM_PIN, GPIO.OUT)

# 设置 PWM（频率 50Hz）
pwm = GPIO.PWM(PWM_PIN, 50)
pwm.start(0)  # 初始占空比 0%

try:
    while True:
        # 设置脉宽 1000μs（1ms）
        pwm.ChangeDutyCycle(5)  # 5% ≈ 1000 / 20000 * 100
        time.sleep(1)
        
        # 设置脉宽 1500μs（1.5ms）
        pwm.ChangeDutyCycle(7.5)  # 7.5% ≈ 1500 / 20000 * 100
        time.sleep(1)
        
        # 设置脉宽 2000μs（2ms）
        pwm.ChangeDutyCycle(10)  # 10% ≈ 2000 / 20000 * 100
        time.sleep(1)

except KeyboardInterrupt:
    pwm.stop()
    GPIO.cleanup()
