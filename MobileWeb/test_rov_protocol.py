#!/usr/bin/env python3
"""
ROV协议专门测试工具
测试与ESP32的完整协议通信
"""

import serial
import struct
import time
import threading
import sys

class ROVProtocolTester:
    def __init__(self, port='/dev/tty.usbserial-0001', baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        self.response_buffer = b''
        
    def calculate_crc8(self, data):
        """计算CRC8校验码"""
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc
    
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=0.1)
            print(f"✅ 连接成功: {self.port}")
            self.running = True
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def send_command(self, cmd, data=b''):
        """发送协议命令"""
        try:
            # 构建命令帧
            frame = bytearray([0xAA, cmd, len(data)])
            frame.extend(data)
            crc = self.calculate_crc8(frame[1:])
            frame.append(crc)
            
            print(f"📤 发送命令 0x{cmd:02X}: {frame.hex().upper()}")
            self.ser.write(frame)
            return True
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def listen_responses(self):
        """监听响应线程"""
        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting)
                    self.response_buffer += data
                    
                    print(f"📥 收到数据: {data.hex().upper()}")
                    
                    # 尝试解析响应
                    self.parse_responses()
                    
            except Exception as e:
                print(f"❌ 监听错误: {e}")
                break
            time.sleep(0.01)
    
    def parse_responses(self):
        """解析响应数据"""
        while len(self.response_buffer) >= 4:  # 最小响应长度
            # 查找响应帧头
            header_pos = self.response_buffer.find(0x55)
            if header_pos == -1:
                # 没找到帧头，清空缓冲区
                if len(self.response_buffer) > 100:  # 避免缓冲区过大
                    self.response_buffer = self.response_buffer[-50:]
                break
            
            # 移除帧头前的数据
            if header_pos > 0:
                self.response_buffer = self.response_buffer[header_pos:]
            
            # 检查是否有完整的响应
            if len(self.response_buffer) < 4:
                break
                
            status = self.response_buffer[1]
            data_len = self.response_buffer[2]
            total_len = 4 + data_len  # 帧头+状态+长度+数据+CRC
            
            if len(self.response_buffer) < total_len:
                break  # 数据不完整
            
            # 提取完整响应
            response_frame = self.response_buffer[:total_len]
            self.response_buffer = self.response_buffer[total_len:]
            
            # 验证CRC
            payload = response_frame[1:-1]  # 状态+长度+数据
            received_crc = response_frame[-1]
            calculated_crc = self.calculate_crc8(payload)
            
            if received_crc != calculated_crc:
                print(f"❌ CRC错误: 期望{calculated_crc:02X}, 收到{received_crc:02X}")
                continue
            
            # 解析响应内容
            response_data = response_frame[3:-1] if data_len > 0 else b''
            self.handle_response(status, response_data)
    
    def handle_response(self, status, data):
        """处理响应数据"""
        print(f"✅ 收到响应: 状态=0x{status:02X}, 数据长度={len(data)}")
        
        if status == 0x00:  # 成功
            if len(data) == 4:  # 心跳响应
                timestamp = struct.unpack('<I', data)[0]
                print(f"   💓 心跳响应: 运行时间 {timestamp} ms")
                
            elif len(data) == 12:  # 气压计数据
                temp = struct.unpack('<f', data[0:4])[0]
                pressure = struct.unpack('<f', data[4:8])[0]
                depth = struct.unpack('<f', data[8:12])[0]
                print(f"   🌊 气压计数据:")
                print(f"      温度: {temp:.2f}°C")
                print(f"      压力: {pressure:.2f} hPa")
                print(f"      深度: {depth:.2f} m")
                
            elif len(data) == 24:  # 完整传感器数据
                temp = struct.unpack('<f', data[0:4])[0]
                pressure = struct.unpack('<f', data[4:8])[0]
                depth = struct.unpack('<f', data[8:12])[0]
                angle_x = struct.unpack('<f', data[12:16])[0]
                angle_y = struct.unpack('<f', data[16:20])[0]
                angle_z = struct.unpack('<f', data[20:24])[0]
                print(f"   📊 完整传感器数据:")
                print(f"      温度: {temp:.2f}°C, 压力: {pressure:.2f} hPa, 深度: {depth:.2f} m")
                print(f"      姿态: X={angle_x:.1f}°, Y={angle_y:.1f}°, Z={angle_z:.1f}°")
                
            elif len(data) == 0:  # 控制命令响应
                print(f"   ✅ 控制命令执行成功")
                
            else:
                print(f"   📦 未知数据: {data.hex().upper()}")
        else:
            print(f"   ❌ 命令失败: 状态码 0x{status:02X}")
    
    def run_test_sequence(self):
        """运行测试序列"""
        print("🚀 开始协议测试序列")
        print("=" * 50)
        
        # 启动监听线程
        listen_thread = threading.Thread(target=self.listen_responses, daemon=True)
        listen_thread.start()
        
        # 等待ESP32启动
        print("⏳ 等待ESP32启动...")
        time.sleep(3)
        
        tests = [
            ("心跳测试", 0xFF, b''),
            ("气压计数据", 0x02, b''),
            ("所有传感器数据", 0x01, b''),
            ("LED测试(红色)", 0x11, bytes([255, 0, 0, 128])),
            ("LED测试(绿色)", 0x11, bytes([0, 255, 0, 128])),
            ("LED测试(蓝色)", 0x11, bytes([0, 0, 255, 128])),
            ("PWM测试", 0x10, bytes([0, 90])),
        ]
        
        for test_name, cmd, data in tests:
            print(f"\n🧪 {test_name}")
            print("-" * 30)
            
            if self.send_command(cmd, data):
                time.sleep(2)  # 等待响应
            else:
                print(f"❌ {test_name} 发送失败")
        
        print(f"\n🎉 测试序列完成")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n🎮 进入交互模式")
        print("命令:")
        print("  h - 心跳包")
        print("  p - 气压计数据")
        print("  s - 所有传感器数据")
        print("  l - LED测试")
        print("  w - PWM测试")
        print("  q - 退出")
        
        # 启动监听线程
        listen_thread = threading.Thread(target=self.listen_responses, daemon=True)
        listen_thread.start()
        
        while self.running:
            try:
                cmd = input("\n> ").strip().lower()
                
                if cmd == 'q':
                    break
                elif cmd == 'h':
                    self.send_command(0xFF)
                elif cmd == 'p':
                    self.send_command(0x02)
                elif cmd == 's':
                    self.send_command(0x01)
                elif cmd == 'l':
                    # 随机颜色LED
                    import random
                    r, g, b = random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)
                    self.send_command(0x11, bytes([r, g, b, 128]))
                elif cmd == 'w':
                    # PWM测试
                    self.send_command(0x10, bytes([0, 90]))
                else:
                    print("未知命令")
                    
            except KeyboardInterrupt:
                break
    
    def close(self):
        """关闭连接"""
        self.running = False
        if self.ser:
            self.ser.close()
        print("🔌 连接已关闭")

def main():
    """主函数"""
    print("🤖 ROV协议测试工具")
    print("=" * 50)
    
    tester = ROVProtocolTester()
    
    if not tester.connect():
        return
    
    try:
        # 运行测试序列
        tester.run_test_sequence()
        
        # 进入交互模式
        tester.interactive_mode()
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    finally:
        tester.close()

if __name__ == "__main__":
    main()
