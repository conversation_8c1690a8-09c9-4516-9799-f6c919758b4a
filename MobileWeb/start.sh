#!/bin/bash

# ROV Web控制系统启动脚本

echo "🤖 启动ROV Web控制系统..."

# 检查Python版本
python_version=$(python3 --version 2>&1)
echo "Python版本: $python_version"

# 检查并安装依赖
echo "检查依赖包..."

# 检查是否安装了pip包
check_package() {
    python3 -c "import $1" 2>/dev/null
    return $?
}

install_if_missing() {
    if ! check_package $1; then
        echo "安装 $1..."
        pip3 install $2
    else
        echo "✓ $1 已安装"
    fi
}

# 安装必要的依赖
install_if_missing "websockets" "websockets"
install_if_missing "flask" "flask"
install_if_missing "serial" "pyserial"
install_if_missing "requests" "requests"
install_if_missing "cv2" "opencv-python"
install_if_missing "numpy" "numpy"

echo ""
echo "🚀 启动服务器..."
echo "Web界面地址: http://localhost:8088"
echo "WebSocket地址: ws://localhost:8765"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================="

# 启动Python服务器
python3 server.py
