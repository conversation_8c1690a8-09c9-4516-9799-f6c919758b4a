<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置模式控制测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #45a049;
        }
        
        .demo-info {
            color: #ffffff;
            margin: 10px 0;
        }
        
        .sensor-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .sensor-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sensor-label {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .sensor-value {
            color: #ffffff;
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">设置模式控制测试</h1>
            
            <div class="demo-section">
                <h3 style="color: white;">模拟传感器数据</h3>
                <div class="demo-info">
                    <p>当前模拟的传感器数据，用于测试设置模式功能：</p>
                </div>
                <div class="sensor-display">
                    <div class="sensor-item">
                        <div class="sensor-label">当前深度</div>
                        <div class="sensor-value" id="currentDepth">5.2 m</div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-label">当前航向</div>
                        <div class="sensor-value" id="currentHeading">135°</div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-label">水温</div>
                        <div class="sensor-value" id="currentTemperature">24.3°C</div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-label">压力</div>
                        <div class="sensor-value" id="currentPressure">1520.4 hPa</div>
                    </div>
                </div>
                <button class="demo-button" onclick="updateSensorData()">更新传感器数据</button>
            </div>
            
            <!-- 设置模式控制面板 -->
            <div class="demo-section">
                <h3 style="color: white;">设置模式控制</h3>
                <div class="control-group">
                    <h4>设置模式</h4>
                    <div class="button-group">
                        <button class="control-button mode-button" id="depthHoldBtn" data-mode="depth">
                            <span class="mode-icon">🌊</span>
                            <span class="mode-text">定深模式</span>
                            <span class="mode-status" id="depthHoldStatus">关闭</span>
                        </button>
                        <button class="control-button mode-button" id="headingHoldBtn" data-mode="heading">
                            <span class="mode-icon">🧭</span>
                            <span class="mode-text">锁定航向</span>
                            <span class="mode-status" id="headingHoldStatus">关闭</span>
                        </button>
                    </div>
                    <div class="mode-info">
                        <p><strong>手柄快捷键:</strong></p>
                        <p>🎮 A键 - 定深模式 | 🎮 X键 - 锁定航向</p>
                        <div class="current-targets">
                            <span>目标深度: <span id="targetDepth">--</span>m</span>
                            <span>目标航向: <span id="targetHeading">--</span>°</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="demo-section">
                <h3 style="color: white;">功能说明</h3>
                <div class="demo-info">
                    <p><strong>定深模式 (🌊):</strong></p>
                    <ul>
                        <li>点击按钮或按手柄A键启用定深模式</li>
                        <li>系统会锁定当前深度作为目标深度</li>
                        <li>ROV会自动调整推进器保持目标深度</li>
                        <li>再次点击或按键可以禁用定深模式</li>
                    </ul>
                    
                    <p><strong>锁定航向 (🧭):</strong></p>
                    <ul>
                        <li>点击按钮或按手柄X键启用锁定航向</li>
                        <li>系统会锁定当前航向作为目标航向</li>
                        <li>ROV会自动调整方向保持目标航向</li>
                        <li>再次点击或按键可以禁用锁定航向</li>
                    </ul>
                    
                    <p><strong>ESP32端函数:</strong></p>
                    <ul>
                        <li><code>handleSetDepthControl()</code> - 处理定深控制</li>
                        <li><code>handleSetHeadingControl()</code> - 处理航向控制</li>
                        <li>命令码: 0x13 (定深), 0x14 (航向)</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-section">
                <h3 style="color: white;">模拟手柄按键</h3>
                <div class="demo-info">
                    <p>模拟Xbox手柄按键操作：</p>
                </div>
                <button class="demo-button" onclick="simulateAButton()">🎮 模拟A键 (定深)</button>
                <button class="demo-button" onclick="simulateXButton()">🎮 模拟X键 (航向)</button>
                <button class="demo-button" onclick="resetModes()">🔄 重置所有模式</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟传感器数据
        let sensorData = {
            depth: 5.2,
            rovHeading: 135,
            temperature: 24.3,
            pressure: 1520.4
        };

        // 模式状态
        let modeStates = {
            depthHold: false,
            headingHold: false,
            targetDepth: 0,
            targetHeading: 0
        };

        // 更新传感器数据显示
        function updateSensorDataDisplay() {
            document.getElementById('currentDepth').textContent = `${sensorData.depth.toFixed(1)} m`;
            document.getElementById('currentHeading').textContent = `${sensorData.rovHeading}°`;
            document.getElementById('currentTemperature').textContent = `${sensorData.temperature.toFixed(1)}°C`;
            document.getElementById('currentPressure').textContent = `${sensorData.pressure.toFixed(1)} hPa`;
        }

        // 更新模式显示
        function updateModeDisplay() {
            // 更新定深模式按钮
            const depthHoldBtn = document.getElementById('depthHoldBtn');
            const depthHoldStatus = document.getElementById('depthHoldStatus');
            const targetDepthSpan = document.getElementById('targetDepth');
            
            if (depthHoldBtn && depthHoldStatus) {
                if (modeStates.depthHold) {
                    depthHoldBtn.classList.add('active');
                    depthHoldStatus.textContent = '启用';
                    if (targetDepthSpan) {
                        targetDepthSpan.textContent = modeStates.targetDepth.toFixed(2);
                    }
                } else {
                    depthHoldBtn.classList.remove('active');
                    depthHoldStatus.textContent = '关闭';
                    if (targetDepthSpan) {
                        targetDepthSpan.textContent = '--';
                    }
                }
            }

            // 更新锁定航向按钮
            const headingHoldBtn = document.getElementById('headingHoldBtn');
            const headingHoldStatus = document.getElementById('headingHoldStatus');
            const targetHeadingSpan = document.getElementById('targetHeading');
            
            if (headingHoldBtn && headingHoldStatus) {
                if (modeStates.headingHold) {
                    headingHoldBtn.classList.add('active');
                    headingHoldStatus.textContent = '启用';
                    if (targetHeadingSpan) {
                        targetHeadingSpan.textContent = modeStates.targetHeading;
                    }
                } else {
                    headingHoldBtn.classList.remove('active');
                    headingHoldStatus.textContent = '关闭';
                    if (targetHeadingSpan) {
                        targetHeadingSpan.textContent = '--';
                    }
                }
            }
        }

        // 切换定深模式
        function toggleDepthHold() {
            if (!modeStates.depthHold) {
                modeStates.depthHold = true;
                modeStates.targetDepth = sensorData.depth;
                console.log(`🌊 启用定深模式，目标深度: ${sensorData.depth.toFixed(2)}m`);
            } else {
                modeStates.depthHold = false;
                console.log('🌊 禁用定深模式');
            }
            updateModeDisplay();
        }

        // 切换锁定航向
        function toggleHeadingHold() {
            if (!modeStates.headingHold) {
                modeStates.headingHold = true;
                modeStates.targetHeading = sensorData.rovHeading;
                console.log(`🧭 启用锁定航向，目标航向: ${sensorData.rovHeading}°`);
            } else {
                modeStates.headingHold = false;
                console.log('🧭 禁用锁定航向');
            }
            updateModeDisplay();
        }

        // 更新传感器数据
        function updateSensorData() {
            sensorData.depth = Math.random() * 20;
            sensorData.rovHeading = Math.floor(Math.random() * 360);
            sensorData.temperature = Math.random() * 15 + 15;
            sensorData.pressure = Math.random() * 1000 + 1000;
            updateSensorDataDisplay();
        }

        // 模拟A键按下
        function simulateAButton() {
            console.log('🎮 模拟A键按下 - 定深控制');
            toggleDepthHold();
        }

        // 模拟X键按下
        function simulateXButton() {
            console.log('🎮 模拟X键按下 - 锁定航向');
            toggleHeadingHold();
        }

        // 重置所有模式
        function resetModes() {
            modeStates.depthHold = false;
            modeStates.headingHold = false;
            updateModeDisplay();
            console.log('🔄 重置所有模式');
        }

        // 初始化事件监听器
        function initEventListeners() {
            const depthHoldBtn = document.getElementById('depthHoldBtn');
            const headingHoldBtn = document.getElementById('headingHoldBtn');

            if (depthHoldBtn) {
                depthHoldBtn.addEventListener('click', toggleDepthHold);
            }

            if (headingHoldBtn) {
                headingHoldBtn.addEventListener('click', toggleHeadingHold);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateSensorDataDisplay();
            updateModeDisplay();
            initEventListeners();
            console.log('设置模式控制测试页面已加载');
        });
    </script>
</body>
</html>
