#!/usr/bin/env python3
"""
测试串口数据帧头对齐功能
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server import ROVWebServer

def test_frame_alignment():
    """测试帧头对齐功能"""
    print("🔧 测试串口数据帧头对齐功能")
    print("=" * 50)
    
    # 创建ROV服务器实例
    server = ROVWebServer()
    
    # 测试数据：模拟各种情况的串口数据
    test_cases = [
        {
            "name": "正常完整帧",
            "data": bytes([0x55, 0x00, 0x02, 0x01, 0xFF, 0x57]),  # 正常的初始化响应
            "expected": "应该正常解析"
        },
        {
            "name": "帧头前有垃圾数据",
            "data": bytes([0xAA, 0xBB, 0xCC, 0x55, 0x00, 0x02, 0x01, 0xFF, 0x57]),
            "expected": "应该丢弃垃圾数据，正常解析帧"
        },
        {
            "name": "分片数据 - 第一部分",
            "data": bytes([0x55, 0x00, 0x02]),  # 不完整的帧
            "expected": "应该缓存数据，等待更多数据"
        },
        {
            "name": "分片数据 - 第二部分",
            "data": bytes([0x01, 0xFF, 0x57]),  # 完成之前的帧
            "expected": "应该组合数据并解析完整帧"
        },
        {
            "name": "多个帧连续",
            "data": bytes([
                0x55, 0x00, 0x02, 0x01, 0xFF, 0x57,  # 第一个帧
                0x55, 0x00, 0x02, 0x01, 0xFF, 0x57   # 第二个帧
            ]),
            "expected": "应该解析两个完整帧"
        },
        {
            "name": "错误的帧头",
            "data": bytes([0xAA, 0x00, 0x02, 0x01, 0xFF, 0x57]),
            "expected": "应该丢弃所有数据"
        },
        {
            "name": "CRC错误的帧",
            "data": bytes([0x55, 0x00, 0x02, 0x01, 0xFF, 0x00]),  # 错误的CRC
            "expected": "应该检测到CRC错误"
        },
        {
            "name": "32字节传感器数据帧",
            "data": bytes([0x55, 0x00, 0x20] + [0x00] * 32 + [0x73]),  # 模拟32字节数据
            "expected": "应该解析为扩展传感器数据"
        }
    ]
    
    print("\n🧪 开始测试各种数据情况...")
    print("-" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[测试 {i}] {test_case['name']}")
        print(f"输入数据: {test_case['data'].hex().upper()}")
        print(f"预期结果: {test_case['expected']}")
        
        # 清空缓冲区
        server.serial_buffer.clear()
        
        try:
            # 处理测试数据
            server.handle_serial_data(test_case['data'])
            
            # 显示缓冲区状态
            if server.serial_buffer:
                print(f"缓冲区剩余: {server.serial_buffer.hex().upper()}")
            else:
                print("缓冲区已清空")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("-" * 30)
    
    # 测试连续数据流
    print(f"\n[特殊测试] 连续数据流处理")
    print("模拟真实串口环境中的连续数据接收...")
    
    # 清空缓冲区
    server.serial_buffer.clear()
    
    # 模拟连续接收的数据片段
    continuous_data = [
        bytes([0xAA, 0xBB]),  # 垃圾数据
        bytes([0x55, 0x00]),  # 帧头开始
        bytes([0x02, 0x01]),  # 继续
        bytes([0xFF, 0x57]),  # 完成第一帧
        bytes([0xCC, 0xDD, 0x55]),  # 垃圾数据 + 新帧头
        bytes([0x00, 0x02, 0x01, 0xFF, 0x57])  # 完成第二帧
    ]
    
    for j, data_chunk in enumerate(continuous_data):
        print(f"接收数据片段 {j+1}: {data_chunk.hex().upper()}")
        server.handle_serial_data(data_chunk)
        if server.serial_buffer:
            print(f"  缓冲区状态: {server.serial_buffer.hex().upper()}")
        else:
            print("  缓冲区已清空")
    
    print("\n✅ 帧头对齐功能测试完成")

if __name__ == "__main__":
    test_frame_alignment()
