<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水密封性能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-controls {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-button {
            margin: 5px;
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.warning {
            background: #FF9800;
        }
        
        .test-button.danger {
            background: #F44336;
        }
        
        .test-info {
            color: #ffffff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: white; text-align: center; margin: 20px 0;">水密封性能测试</h1>
        
        <div class="test-controls">
            <h3 style="color: white;">模拟不同的水密封状态</h3>
            <div class="test-info">
                <p>点击下面的按钮模拟不同的水密封状态：</p>
            </div>
            <button class="test-button" onclick="setWaterSealStatus(0)">密封良好 (状态 0)</button>
            <button class="test-button warning" onclick="setWaterSealStatus(1)">可能有湿气 (状态 1)</button>
            <button class="test-button warning" onclick="setWaterSealStatus(2)">轻微漏水 (状态 2)</button>
            <button class="test-button danger" onclick="setWaterSealStatus(3)">严重漏水 (状态 3)</button>
        </div>
        
        <section class="status-panel">
            <div class="status-grid">
                <!-- 环境传感器信息 -->
                <div class="status-card environment-card">
                    <div class="status-info">
                        <h3>环境传感器 (含水密封性能)</h3>
                        <div class="environment-grid">
                            <div class="env-item">
                                <span class="env-label">深度</span>
                                <span class="env-value" id="depthValue">2.5 m</span>
                                <span class="env-trend" id="depthTrend">稳定</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">水温</span>
                                <span class="env-value" id="temperatureValue">24.3°C</span>
                                <span class="env-trend" id="temperatureTrend">正常</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">压力</span>
                                <span class="env-value" id="pressureValue">1025.6 hPa</span>
                                <span class="env-trend" id="pressureTrend">正常</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">水密封</span>
                                <span class="env-value" id="waterSealValue">密封良好</span>
                                <span class="env-trend" id="waterSealTrend">正常</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="test-controls">
            <h3 style="color: white;">状态说明</h3>
            <div class="test-info">
                <p><strong>状态 0 - 密封良好:</strong> 触摸传感器值正常，无水分接触</p>
                <p><strong>状态 1 - 可能有湿气:</strong> 触摸传感器值略高，可能有轻微湿气</p>
                <p><strong>状态 2 - 轻微漏水:</strong> 触摸传感器值较高，检测到水分</p>
                <p><strong>状态 3 - 严重漏水:</strong> 触摸传感器值很高，严重漏水</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟传感器数据对象
        const sensorData = {
            waterSealStatus: 0,
            waterSealText: '密封良好'
        };

        // 状态映射
        const statusMap = {
            0: { text: '密封良好', trend: '正常', color: '#4CAF50' },
            1: { text: '可能有湿气', trend: '注意', color: '#FF9800' },
            2: { text: '轻微漏水', trend: '警告', color: '#FF5722' },
            3: { text: '严重漏水', trend: '危险', color: '#F44336' }
        };

        // 设置水密封状态
        function setWaterSealStatus(status) {
            sensorData.waterSealStatus = status;
            sensorData.waterSealText = statusMap[status].text;
            
            updateWaterSealDisplay();
            
            console.log(`设置水密封状态: ${status} - ${statusMap[status].text}`);
        }

        // 更新水密封显示
        function updateWaterSealDisplay() {
            const waterSealValue = document.getElementById('waterSealValue');
            const waterSealTrend = document.getElementById('waterSealTrend');
            const status = sensorData.waterSealStatus;
            
            if (waterSealValue && waterSealTrend && statusMap[status]) {
                waterSealValue.textContent = statusMap[status].text;
                waterSealTrend.textContent = statusMap[status].trend;
                waterSealTrend.style.color = statusMap[status].color;
            }
        }

        // 模拟实时数据更新
        function simulateDataUpdate() {
            const depth = (Math.random() * 5 + 1).toFixed(1);
            const temp = (Math.random() * 10 + 20).toFixed(1);
            const pressure = (Math.random() * 50 + 1000).toFixed(1);
            
            document.getElementById('depthValue').textContent = depth + ' m';
            document.getElementById('temperatureValue').textContent = temp + '°C';
            document.getElementById('pressureValue').textContent = pressure + ' hPa';
        }

        // 每5秒更新一次其他数据
        setInterval(simulateDataUpdate, 5000);
        
        // 初始化显示
        updateWaterSealDisplay();
        
        console.log('水密封性能测试页面已加载');
    </script>
</body>
</html>
