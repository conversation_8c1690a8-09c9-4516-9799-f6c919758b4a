// ROV控制系统JavaScript
class ROVController {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.sensorData = {
            depth: 0,
            rovHeading: 0,
            phoneHeading: 0,
            temperature: 0,
            pressure: 0
        };
        this.deviceOrientation = null;

        // 趋势跟踪变量
        this.lastDepth = undefined;
        this.lastPressure = undefined;

        // 手机罗盘相关
        this.compassSupported = false;
        this.compassPermissionGranted = false;
        this.lastPhoneHeading = 0;
        this.compassDataReceived = false;
        this.compassDebugLogged = false;

        // 串口响应监控
        this.lastPingTime = null;
        this.lastSerialResponseTime = null;
        this.serialResponseTimeout = null;

        this.pingIntervalObj = null

        this.checkTimeoutIntervalObj = null;

        this.received = true;

        this.surfacePressure=0;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupWebSocket();
        this.setupDeviceOrientation();
        this.setupVideoPlayer();
        this.setupSensorOverlay();
        this.setupModeControls();
        this.startDataUpdate();

        // 初始化手机罗盘
        this.initPhoneCompass();

        // 初始化串口响应状态为黑色（未连接）
        this.updateSerialResponseStatus('disconnected');

        // 隐藏加载界面
        setTimeout(() => {
            document.getElementById('loadingOverlay').style.display = 'none';
            // 检查视频流状态
            this.checkVideoStreamStatus();
        }, 2000);

        // clearInterval(this.checkTimeoutIntervalObj);
        // this.checkTimeoutIntervalObj=setInterval(()=>{
        //     document.getElementById('latency').textContent = 'disconnected';
        // })
    }

    // WebSocket连接设置
    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.hostname}:8765`;

        try {
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.updateConnectionStatus('已连接');
                // WebSocket连接成功，但串口状态需要等待实际数据
                this.updateSerialResponseStatus('disconnected');
                this.startDataUpdate();
            };

            this.websocket.onmessage = (event) => {
                try {
                    // 检查数据是否为空或无效
                    if (!event.data || typeof event.data !== 'string') {
                        console.warn('收到无效的WebSocket数据:', event.data);
                        return;
                    }

                    // 检查数据长度
                    if (event.data.length > 1000000) {
                        console.warn('收到过大的WebSocket数据:', event.data.length, '字符');
                        return;
                    }

                    // 检查是否包含无效字符
                    const invalidChars = /[\x00-\x1F\x7F-\x9F]/g;
                    if (invalidChars.test(event.data)) {
                        console.warn('数据包含无效控制字符');
                        // 清理数据
                        // const cleanData = event.data.replace(invalidChars, '');
                        // const data = JSON.parse(cleanData);
                        // this.handleSensorData(data);
                        return;
                    }

                    // 检查数据是否以{开头和}结尾（基本JSON格式检查）
                    const trimmedData = event.data.trim();
                    if (!trimmedData.startsWith('{') || !trimmedData.endsWith('}')) {
                        console.warn('数据不是完整的JSON格式:');
                        console.warn(trimmedData)
                        console.warn('开头50字符:', trimmedData.substring(0, 50));
                        console.warn('结尾50字符:', trimmedData.substring(trimmedData.length - 50));
                        return;
                    }

                    // 尝试解析JSON数据
                    const data = JSON.parse(trimmedData);

                    // 调试：显示接收到的数据长度
                    // console.log('收到WebSocket数据长度:', event.data.length, data);

                    // 验证解析后的数据结构
                    if (typeof data !== 'object' || data === null) {
                        console.warn('解析后的数据不是有效对象:', data);
                        return;
                    }

                    this.received = true;
                    this.handleSensorData(data);
                } catch (error) {
                    console.error('解析WebSocket数据失败:', error.message);
                    console.error('原始数据:', event.data);
                    console.error('数据长度:', event.data ? event.data.length : 'undefined');
                    console.error('数据类型:', typeof event.data);

                    // 显示错误位置附近的字符
                    if (event.data && typeof event.data === 'string') {
                        console.error('数据前100字符:', event.data.substring(0, 100));
                        console.error('数据后100字符:', event.data.substring(Math.max(0, event.data.length - 100)));

                        // 尝试找到JSON错误位置
                        const match = error.message.match(/position (\d+)/);
                        if (match) {
                            const pos = parseInt(match[1]);
                            const start = Math.max(0, pos - 20);
                            const end = Math.min(event.data.length, pos + 20);
                            console.error(`错误位置${pos}附近:`, event.data.substring(start, end));
                            console.error(`错误字符: "${event.data[pos]}" (ASCII: ${event.data.charCodeAt(pos)})`);
                        }
                    }
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                this.updateConnectionStatus('断开连接');
                this.updateSerialResponseStatus('disconnected');
                // 尝试重连
                setTimeout(() => this.setupWebSocket(), 3000);
            };

            this.websocket.onerror = (error) => {
                // console.error('WebSocket错误:', error);
                this.showError('WebSocket连接失败');
            };
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            this.showError('无法建立WebSocket连接');
        }
    }

    // 处理传感器数据，来自websocket
    handleSensorData(data) {
        if (data.type === 'sensor_data') {
            this.sensorData = { ...this.sensorData, ...data.data };
            this.lastSerialResponseTime = Date.now();
        } else if (data.type === 'mode_status') {
            // 处理模式状态更新
            this.handleModeStatusUpdate(data);
            return;
        }

        if (data.type === 'sensor_data') {

            // 根据串口延迟判断响应状态
            if (this.sensorData.serialLatency !== undefined && this.sensorData.serialLatency >= 0) {

                const latency = this.sensorData.serialLatency;
                if (latency < 20) {
                    this.updateSerialResponseStatus('success');
                    // console.log(`串口延迟: ${latency}ms - 绿色`);
                } else if (latency < 120) {
                    this.updateSerialResponseStatus('slow');
                    // console.warn(`串口延迟: ${latency}ms - 黄色`);
                } else if (latency < 150) {
                    this.updateSerialResponseStatus('error');
                    // console.error(`串口延迟: ${latency}ms - 红色`);
                } else {
                    this.updateSerialResponseStatus('disconnected');
                    // console.error(`串口延迟: ${latency}ms - 红色`);
                }

            } else {
                // 没有有效的延迟数据，显示为未连接状态
                this.updateSerialResponseStatus('disconnected');
            }

            this.updateUI();

            // 设置串口响应超时检查 - 3秒没有新数据则标记为未连接
            if (this.serialResponseTimeout) {
                clearTimeout(this.serialResponseTimeout);
            }
            this.serialResponseTimeout = setTimeout(() => {
                this.updateSerialResponseStatus('disconnected');
                console.log('串口响应超时，标记为未连接状态');
            }, 3000); // 3秒没有新数据则标记为未连接

            if (this.lastPingTime) {
                const latency = Date.now() - this.lastPingTime;
                document.getElementById('latency').textContent = latency;
                // console.log(`🏓 sensor_data延迟: ${latency}ms`);
            }

        } else if (data.type === 'pong') {
            // 处理ping响应，计算延迟
            if (this.lastPingTime) {
                const latency = Date.now() - this.lastPingTime;
                document.getElementById('latency').textContent = latency;
                console.log(`🏓 Ping延迟: ${latency}ms`);
            }
        }
    }

    // 更新串口响应状态指示器
    updateSerialResponseStatus(status) {
        // console.log(status)
        const indicator = document.getElementById('serialResponseStatus');
        if (indicator) {
            // 移除所有状态类
            indicator.className = 'response-indicator';

            if (status === 'success') {
                indicator.classList.add('success');
                indicator.textContent = '●';
                indicator.title = '串口响应正常 (<20ms)';
            } else if (status === 'slow') {
                indicator.classList.add('timeout');
                indicator.textContent = '●';
                indicator.title = '串口响应较慢 (20-100ms)';
            } else if (status === 'error') {
                indicator.classList.add('error');
                indicator.textContent = '●';
                indicator.title = '串口响应超时 (>250ms)';
            } else if (status === 'disconnected') {
                indicator.classList.add('disconnected');
                indicator.textContent = '●';
                indicator.title = '串口未连接/无响应';
            } else {
                // 默认状态
                indicator.classList.add('disconnected');
                indicator.textContent = '●';
                indicator.title = '串口状态未知';
            }
        }
    }

    // 设备方向传感器
    setupDeviceOrientation() {
        if ('DeviceOrientationEvent' in window) {
            // 请求权限（iOS 13+需要）
            if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                DeviceOrientationEvent.requestPermission()
                    .then(response => {
                        if (response === 'granted') {
                            this.enableDeviceOrientation();
                        }
                    })
                    .catch(console.error);
            } else {
                this.enableDeviceOrientation();
            }
        }
    }

    enableDeviceOrientation() {
        window.addEventListener('deviceorientation', (event) => {
            // 获取手机航向角（0-360度）
            let heading = event.alpha;
            if (heading !== null) {
                // 处理负值
                if (heading < 0) heading += 360;
                this.sensorData.phoneHeading = Math.round(heading);
                this.updatePhoneHeading();
            }
        });
    }

    // 视频播放器设置
    setupVideoPlayer() {
        const videoStream = document.getElementById('videoStream');
        const video = document.getElementById('videoPlayer');
        const videoContainer = document.querySelector('.video-container');
        const videoStatus = document.getElementById('videoStatus');

        // 默认使用MJPEG流（img标签）
        this.currentVideoMode = 'mjpeg';

        // 设置MJPEG流源
        const setupMJPEGStream = () => {
            const videoSources = [
                '/video_feed',  // 本地服务器的视频流
                `http://${window.location.hostname}:8088/video_feed`,  // 动态获取主机IP
                'http://localhost:8088/video_feed',
                'http://localhost:5000/video_feed'  // 独立的RTMP服务
            ];

            let currentSourceIndex = 0;

            const tryNextSource = () => {
                if (currentSourceIndex < videoSources.length) {
                    console.log(`尝试视频源: ${videoSources[currentSourceIndex]}`);
                    videoStream.src = videoSources[currentSourceIndex];
                    currentSourceIndex++;
                } else {
                    this.showError('无法连接MJPEG视频流');
                    videoStatus.textContent = '视频流连接失败';
                }
            };

            videoStream.onload = () => {
                console.log('MJPEG流加载成功');
                videoStatus.textContent = 'MJPEG流 - 正常';
                videoStatus.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
            };

            videoStream.onerror = () => {
                console.log('MJPEG流加载失败，尝试下一个源');
                videoStatus.textContent = 'MJPEG流 - 连接中...';
                videoStatus.style.backgroundColor = 'rgba(255, 165, 0, 0.7)';
                setTimeout(tryNextSource, 1000); // 延迟1秒后尝试下一个源
            };

            // 添加图像加载监听
            videoStream.addEventListener('load', () => {
                videoStatus.textContent = 'MJPEG流 - 已连接';
                videoStatus.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
            });

            videoStream.addEventListener('error', () => {
                videoStatus.textContent = 'MJPEG流 - 连接失败';
                videoStatus.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';

                // 5秒后尝试重新连接
                setTimeout(() => {
                    if (this.currentVideoMode === 'mjpeg') {
                        console.log('尝试重新连接视频流...');
                        this.reloadVideoStream();
                    }
                }, 5000);
            });

            // 初始加载
            tryNextSource();
        };

        // 初始化MJPEG流
        setupMJPEGStream();

        // 添加页面可见性变化监听，确保视频流持续更新
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.currentVideoMode === 'mjpeg') {
                // 页面重新可见时，刷新视频流
                this.reloadVideoStream();
            }
        });

        // 模式切换控制
        document.getElementById('switchMode').addEventListener('click', () => {
            this.switchVideoMode();
        });

        // 全屏控制
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            if (videoContainer.requestFullscreen) {
                videoContainer.requestFullscreen();
            } else if (videoContainer.webkitRequestFullscreen) {
                videoContainer.webkitRequestFullscreen();
            }
        });

        // 录制控制
        document.getElementById('recordBtn').addEventListener('click', () => {
            this.toggleRecording();
        });
    }

    // 切换视频模式
    switchVideoMode() {
        const videoStream = document.getElementById('videoStream');
        const video = document.getElementById('videoPlayer');
        const videoStatus = document.getElementById('videoStatus');

        if (this.currentVideoMode === 'mjpeg') {
            // 切换到video标签模式
            videoStream.style.display = 'none';
            video.style.display = 'block';
            video.src = '/video_feed';
            this.currentVideoMode = 'video';
            videoStatus.textContent = 'Video标签模式';
            console.log('切换到Video标签模式');
        } else {
            // 切换到MJPEG流模式
            video.style.display = 'none';
            videoStream.style.display = 'block';
            this.currentVideoMode = 'mjpeg';
            videoStatus.textContent = 'MJPEG流模式';
            console.log('切换到MJPEG流模式');
        }
    }

    // 事件监听器设置
    setupEventListeners() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tabName = btn.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 灯光控制
        document.getElementById('lightToggle').addEventListener('click', () => {
            this.sendCommand('light_toggle');
        });

        document.getElementById('lightBrightness').addEventListener('input', (e) => {
            const brightness = e.target.value;
            document.querySelector('.slider-value').textContent = brightness + '%';
            this.sendCommand('light_brightness', { value: brightness });
        });

        // 相机控制
        ['cameraUp', 'cameraDown', 'cameraLeft', 'cameraRight', 'cameraCenter'].forEach(id => {
            document.getElementById(id).addEventListener('click', () => {
                const direction = id.replace('camera', '').toLowerCase();
                this.sendCommand('camera_control', { direction });
            });
        });

        // 航向校准
        document.getElementById('calibrateHeading').addEventListener('click', () => {
            this.sendCommand('calibrate_heading');
            this.showSuccess('航向校准已启动');
        });

        // ADC校准
        document.getElementById('calibrateAdcBtn').addEventListener('click', () => {
            this.calibrateADC();
        });

        // 深度偏移设置
        document.getElementById('depthOffset').addEventListener('change', (e) => {
            this.sendCommand('depth_offset', { value: parseFloat(e.target.value) });
        });

        // 视频设置
        document.getElementById('videoQuality').addEventListener('change', (e) => {
            this.sendCommand('video_quality', { quality: e.target.value });
        });

        document.getElementById('frameRate').addEventListener('change', (e) => {
            this.sendCommand('frame_rate', { rate: parseInt(e.target.value) });
        });

        // 错误提示关闭
        document.querySelector('.close-btn').addEventListener('click', () => {
            document.getElementById('errorToast').style.display = 'none';
        });

        // 添加视频流测试按钮（调试用）
        if (window.location.search.includes('debug')) {
            this.addDebugControls();
        }
    }

    // 发送命令到后端
    sendCommand(command, data = {}) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            const message = {
                type: 'command',
                command: command,
                data: data,
                timestamp: Date.now()
            };
            this.websocket.send(JSON.stringify(message));
        } else {
            this.showError('连接已断开，无法发送命令');
        }
    }

    // 切换标签页
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }

    // 更新连接状态Socket
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');

        if (typeof status === 'string') {
            // 直接设置状态文本
            statusElement.textContent = status;
            this.isConnected = (status === '已连接');
        } else {
            // 兼容布尔值参数
            this.isConnected = status;
            const dot = statusElement.querySelector('.status-dot');
            const text = statusElement.querySelector('span:last-child');

            if (dot && text) {
                if (status) {
                    dot.className = 'status-dot online';
                    text.textContent = '在线';
                } else {
                    dot.className = 'status-dot offline';
                    text.textContent = '离线';
                }
            } else {
                // 如果没有dot和text元素，直接设置文本
                statusElement.textContent = status ? '已连接' : '断开连接';
            }
        }
    }

    // 处理模式状态更新
    handleModeStatusUpdate(data) {
        const { mode, enabled, target } = data;

        console.log(`🎮 收到模式状态更新: ${mode}, 启用: ${enabled}, 目标: ${target}`);

        if (mode === 'depth_hold') {
            this.modeStates.depthHold = enabled;
            if (enabled) {
                this.modeStates.targetDepth = target;
            }
        } else if (mode === 'heading_hold') {
            this.modeStates.headingHold = enabled;
            if (enabled) {
                this.modeStates.targetHeading = target;
            }
        }

        // 更新UI显示
        this.updateModeDisplay();
    }

    // 更新UI显示
    updateUI() {
        // 更新深度
        const depthElement = document.getElementById('depthValue');
        if (depthElement) {
            // 处理负数深度（水下为负值是正常的）
            const depthValue = this.sensorData.depth; // 取绝对值显示
            const depthText = `${depthValue.toFixed(3)} m`;
            depthElement.textContent = depthText;
        }
        this.updateDepthTrend();

        // 更新ROV航向 - 优先使用HMC5883罗盘数据
        const rovHeading = this.sensorData.compassHeading !== undefined ?
            this.sensorData.compassHeading : this.sensorData.rovHeading;
        document.getElementById('rovHeading').textContent = `${rovHeading.toFixed(1)}°`;
        this.updateCompass('rovCompass', rovHeading);

        // 更新温度
        if (this.sensorData.temperature !== undefined) {
            document.getElementById('temperatureValue').textContent = `${this.sensorData.temperature.toFixed(2)}°C`;
            this.updateTemperatureTrend();
        }

        // 更新压力
        if (this.sensorData.pressure !== undefined) {
            document.getElementById('pressureValue').textContent = `${this.sensorData.pressure.toFixed(3)} hPa`;
            this.updatePressureTrend();
        }

        // 更新水密封性能
        if (this.sensorData.waterSealValue !== undefined) {
            document.getElementById('waterSealValue').textContent = '(' + this.sensorData.waterSealValue + ')';

            this.updateWaterSealTrend();
        }

        // 更新入水状态
        // if (this.sensorData.isUnderwater !== undefined) {
        //     const underwaterText = this.sensorData.isUnderwater ? '入水' : '水面';
        //     document.getElementById('underwaterValue').textContent = underwaterText;
        //     this.updateUnderwaterTrend();
        // }

        // 更新视频覆盖层的传感器数据
        this.updateVideoOverlay();

        // 更新系统状态
        document.getElementById('systemStatus').textContent = this.isConnected ? '正常' : '离线';

        // 更新串口延迟显示
        if (this.sensorData.serialLatency !== undefined) {
            document.getElementById('serialLatency').textContent = this.sensorData.serialLatency;
        }

        // 更新ADC电压显示
        if (this.sensorData.adcVoltage !== undefined) {
            const adcElement = document.getElementById('adcValue');
            if (adcElement) {
                // 只显示ADC电压值
                if (this.sensorData.adcVoltage < 3.6 * 6) {
                    adcElement.style.color = '#ff6b6b';
                    adcElement.style.fontSize = '100px'
                } else {
                    adcElement.style.color = '#ffffff';
                    adcElement.style.fontSize = '20px'
                }
                adcElement.textContent = `${this.sensorData.adcVoltage.toFixed(3)}V`;
            }
        }

        // 更新MPU6050姿态数据显示
        if (this.sensorData.roll !== undefined) {
            this.sensorData.roll = this.sensorData.roll + 174
            if (this.sensorData.roll > 300) {
                this.sensorData.roll = this.sensorData.roll - 360
            }
            document.getElementById('rollAngle').textContent = `${this.sensorData.roll.toFixed(1)}°`;
        }
        if (this.sensorData.pitch !== undefined) {
            this.sensorData.pitch = this.sensorData.pitch - 179
            if (this.sensorData.pitch < -300) {
                this.sensorData.pitch = 360 + this.sensorData.pitch
            }
            document.getElementById('pitchAngle').textContent = `${this.sensorData.pitch.toFixed(1)}°`;
        }
        // if (this.sensorData.yaw !== undefined) {
        //     document.getElementById('yawAngle').textContent = `${this.sensorData.yaw.toFixed(1)}°`;
        // }
    }

    // 更新手机航向
    updatePhoneHeading() {
        document.getElementById('phoneHeading').textContent = `${this.sensorData.phoneHeading}°`;
        this.updateCompass('phoneCompass', this.sensorData.phoneHeading);
    }

    // 更新指南针显示
    updateCompass(compassId, heading) {
        const compass = document.getElementById(compassId);
        if (compass) {
            compass.style.transform = `translate(-50%, -100%) rotate(${heading}deg)`;
        }
    }

    // 更新深度趋势
    updateDepthTrend() {
        const depthTrendElement = document.getElementById('depthTrend');
        const currentDepth = this.sensorData.depth;

        if (this.lastDepth !== undefined) {
            const depthChange = currentDepth - this.lastDepth;
            if (Math.abs(depthChange) < 0.3) {
                depthTrendElement.textContent = '稳定';
                depthTrendElement.style.color = '#4CAF50';
            } else if (depthChange < 0) {
                // 深度变得更负 = 下潜更深
                depthTrendElement.textContent = '下潜';
                depthTrendElement.style.color = '#2196F3';
            } else {
                // 深度变得不那么负 = 上浮
                depthTrendElement.textContent = '上浮';
                depthTrendElement.style.color = '#FF9800';
            }
        } else {
            depthTrendElement.textContent = '稳定';
            depthTrendElement.style.color = '#4CAF50';
        }
        this.lastDepth = currentDepth;
    }

    // 更新温度趋势
    updateTemperatureTrend() {
        const tempTrendElement = document.getElementById('temperatureTrend');
        const currentTemp = this.sensorData.temperature;

        if (currentTemp < 10) {
            tempTrendElement.textContent = '低温';
            tempTrendElement.style.color = '#2196F3';
        } else if (currentTemp > 30) {
            tempTrendElement.textContent = '高温';
            tempTrendElement.style.color = '#FF5722';
        } else {
            tempTrendElement.textContent = '正常';
            tempTrendElement.style.color = '#4CAF50';
        }
    }

    // 更新压力趋势
    updatePressureTrend() {
        const pressureTrendElement = document.getElementById('pressureTrend');
        const currentPressure = this.sensorData.pressure;

        if (this.lastPressure !== undefined) {
            const pressureChange = currentPressure - this.lastPressure;
            if (Math.abs(pressureChange) < 1) {
                pressureTrendElement.textContent = '稳定';
                pressureTrendElement.style.color = '#4CAF50';
            } else if (pressureChange > 0) {
                pressureTrendElement.textContent = '上升';
                pressureTrendElement.style.color = '#FF9800';
            } else {
                pressureTrendElement.textContent = '下降';
                pressureTrendElement.style.color = '#2196F3';
            }
        } else {
            pressureTrendElement.textContent = '正常';
            pressureTrendElement.style.color = '#4CAF50';
        }
        this.lastPressure = currentPressure;
    }

    // 更新水密封性能趋势
    updateWaterSealTrend() {
        // this.sensorData.waterSealValue=30;
        // this.sensorData.waterDetectionValue=30;

        const waterSealTrendElement = document.getElementById('waterSealTrend');
        var waterSealStatus = this.sensorData.waterSealStatus;
        var waterSealValue = this.sensorData.waterSealValue;

        if (waterSealValue !== undefined) {
            waterSealTrendElement.style.fontSize = "14px";

            // switch (waterSealStatus) {
            //     case 0: // 密封良好
            //         waterSealTrendElement.textContent = '正常';
            //         waterSealTrendElement.style.color = '#4CAF50';
            //         break;
            //     case 1: // 可能有湿气
            //         waterSealTrendElement.textContent = '注意';
            //         waterSealTrendElement.style.color = '#FF9800';
            //         waterSealTrendElement.style.fontSize="40px"
            //         break;
            //     case 2: // 严重漏水
            //         waterSealTrendElement.textContent = '危险';
            //         waterSealTrendElement.style.color = '#F44336';
            //         waterSealTrendElement.style.fontSize="60px"
            //         break;
            //     default:
            //         waterSealTrendElement.textContent = '未知';
            //         waterSealTrendElement.style.color = '#9E9E9E';
            // }

            if (waterSealValue > 45) {
                // 密封良好
                waterSealTrendElement.textContent = '正常';
                waterSealTrendElement.style.color = '#4CAF50';
            } else if (waterSealValue > 20) {
                // 可能有湿气
                waterSealTrendElement.textContent = '注意';
                waterSealTrendElement.style.color = '#FF9800';
                waterSealTrendElement.style.fontSize = "30px"
            } else {
                // 严重漏水
                waterSealTrendElement.textContent = '危险';
                waterSealTrendElement.style.color = '#F44336';
                waterSealTrendElement.style.fontSize = "40px"
            }

        }
    }

    // 更新入水状态趋势
    updateUnderwaterTrend() {
        // const underwaterTrendElement = document.getElementById('underwaterTrend');
        // const isUnderwater = this.sensorData.isUnderwater;
        // const waterDetectionValue = this.sensorData.waterDetectionValue;

        // if (isUnderwater !== undefined) {
        //     if (isUnderwater) {
        //         underwaterTrendElement.textContent = '入水(' + waterDetectionValue + ')';
        //         underwaterTrendElement.style.color = '#2196F3'; // 蓝色表示入水
        //     } else {
        //         underwaterTrendElement.textContent = '水面(' + waterDetectionValue + ')';
        //         underwaterTrendElement.style.color = '#4CAF50'; // 绿色表示水面
        //     }
        // }
    }

    // 设置模式控制
    setupModeControls() {
        // 定深模式按钮
        const depthHoldBtn = document.getElementById('depthHoldBtn');
        const headingHoldBtn = document.getElementById('headingHoldBtn');

        // 模式状态
        this.modeStates = {
            depthHold: false,
            headingHold: false,
            targetDepth: 0,
            targetHeading: 0
        };

        // 定深模式控制
        if (depthHoldBtn) {
            depthHoldBtn.addEventListener('click', () => {
                this.toggleDepthHold();
            });
        }

        // 锁定航向控制
        if (headingHoldBtn) {
            headingHoldBtn.addEventListener('click', () => {
                this.toggleHeadingHold();
            });
        }

        // 更新模式显示
        this.updateModeDisplay();
    }

    // 切换定深模式
    toggleDepthHold() {
        const currentDepth = this.sensorData.depth || 0;

        if (!this.modeStates.depthHold) {
            // 启用定深模式
            this.modeStates.depthHold = true;
            this.modeStates.targetDepth = currentDepth;

            // 发送定深控制命令到服务器
            this.sendModeCommand('depth_hold', {
                enable: true,
                target: currentDepth
            });

            console.log(`🌊 启用定深模式，目标深度: ${currentDepth.toFixed(2)}m`);
        } else {
            // 禁用定深模式
            this.modeStates.depthHold = false;

            // 发送禁用命令
            this.sendModeCommand('depth_hold', {
                enable: false
            });

            console.log('🌊 禁用定深模式');
        }

        this.updateModeDisplay();
    }

    // 切换锁定航向
    toggleHeadingHold() {
        const currentHeading = this.sensorData.rovHeading || 0;

        if (!this.modeStates.headingHold) {
            // 启用锁定航向
            this.modeStates.headingHold = true;
            this.modeStates.targetHeading = currentHeading;

            // 发送锁定航向命令到服务器
            this.sendModeCommand('heading_hold', {
                enable: true,
                target: currentHeading
            });

            console.log(`🧭 启用锁定航向，目标航向: ${currentHeading}°`);
        } else {
            // 禁用锁定航向
            this.modeStates.headingHold = false;

            // 发送禁用命令
            this.sendModeCommand('heading_hold', {
                enable: false
            });

            console.log('🧭 禁用锁定航向');
        }

        this.updateModeDisplay();
    }

    // 发送模式控制命令
    sendModeCommand(mode, data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            const command = {
                type: 'mode_control',
                mode: mode,
                data: data
            };
            this.websocket.send(JSON.stringify(command));
        }
    }

    // 更新模式显示
    updateModeDisplay() {
        // 更新定深模式按钮
        const depthHoldBtn = document.getElementById('depthHoldBtn');
        const depthHoldStatus = document.getElementById('depthHoldStatus');
        const targetDepthSpan = document.getElementById('targetDepth');

        if (depthHoldBtn && depthHoldStatus) {
            if (this.modeStates.depthHold) {
                depthHoldBtn.classList.add('active');
                depthHoldStatus.textContent = '启用';
                if (targetDepthSpan) {
                    targetDepthSpan.textContent = this.modeStates.targetDepth.toFixed(2);
                }
            } else {
                depthHoldBtn.classList.remove('active');
                depthHoldStatus.textContent = '关闭';
                if (targetDepthSpan) {
                    targetDepthSpan.textContent = '--';
                }
            }
        }

        // 更新锁定航向按钮
        const headingHoldBtn = document.getElementById('headingHoldBtn');
        const headingHoldStatus = document.getElementById('headingHoldStatus');
        const targetHeadingSpan = document.getElementById('targetHeading');

        if (headingHoldBtn && headingHoldStatus) {
            if (this.modeStates.headingHold) {
                headingHoldBtn.classList.add('active');
                headingHoldStatus.textContent = '启用';
                if (targetHeadingSpan) {
                    targetHeadingSpan.textContent = this.modeStates.targetHeading;
                }
            } else {
                headingHoldBtn.classList.remove('active');
                headingHoldStatus.textContent = '关闭';
                if (targetHeadingSpan) {
                    targetHeadingSpan.textContent = '--';
                }
            }
        }
    }

    // 设置传感器覆盖层控制
    setupSensorOverlay() {
        const toggleOverlayBtn = document.getElementById('toggleOverlay');
        const toggleOpacityBtn = document.getElementById('toggleOverlayOpacity');
        const sensorOverlay = document.getElementById('sensorOverlay');

        // 覆盖层显示/隐藏控制
        if (toggleOverlayBtn && sensorOverlay) {
            toggleOverlayBtn.addEventListener('click', () => {
                sensorOverlay.classList.toggle('hidden');
                const isHidden = sensorOverlay.classList.contains('hidden');
                toggleOverlayBtn.textContent = isHidden ? '📊' : '🚫';
                toggleOverlayBtn.title = isHidden ? '显示传感器数据' : '隐藏传感器数据';

                // 保存用户偏好
                localStorage.setItem('sensorOverlayVisible', !isHidden);
            });

            // 恢复用户偏好
            const savedVisible = localStorage.getItem('sensorOverlayVisible');
            if (savedVisible === 'false') {
                sensorOverlay.classList.add('hidden');
                toggleOverlayBtn.textContent = '📊';
                toggleOverlayBtn.title = '显示传感器数据';
            }
        }

        // 透明度控制
        if (toggleOpacityBtn && sensorOverlay) {
            let opacityLevel = 0; // 0: 正常, 1: 半透明, 2: 更透明

            toggleOpacityBtn.addEventListener('click', () => {
                opacityLevel = (opacityLevel + 1) % 3;

                sensorOverlay.classList.remove('semi-transparent');
                sensorOverlay.style.opacity = '';

                switch (opacityLevel) {
                    case 0:
                        sensorOverlay.style.opacity = '0.9';
                        toggleOpacityBtn.textContent = '👁️';
                        toggleOpacityBtn.title = '调整透明度: 正常';
                        break;
                    case 1:
                        sensorOverlay.classList.add('semi-transparent');
                        toggleOpacityBtn.textContent = '👁️‍🗨️';
                        toggleOpacityBtn.title = '调整透明度: 半透明';
                        break;
                    case 2:
                        sensorOverlay.style.opacity = '0.3';
                        toggleOpacityBtn.textContent = '👻';
                        toggleOpacityBtn.title = '调整透明度: 很透明';
                        break;
                }

                // 保存用户偏好
                localStorage.setItem('sensorOverlayOpacity', opacityLevel);
            });

            // 恢复用户偏好
            const savedOpacity = localStorage.getItem('sensorOverlayOpacity');
            if (savedOpacity) {
                opacityLevel = parseInt(savedOpacity);
                // 触发一次点击来应用保存的透明度
                toggleOpacityBtn.click();
            }
        }
    }

    // 更新视频覆盖层的传感器数据
    updateVideoOverlay() {
        // console.log("joystickStatus",this.sensorData.joystickStatus);

        // esp32
        const esp32CommunicationError = document.getElementById('esp32CommunicationError');
        if (esp32CommunicationError && this.sensorData.esp32CommunicationError !== undefined) {
            esp32CommunicationError.textContent = `${this.sensorData.esp32CommunicationError}`;
        }

        const esp32CommunicationResponseText = document.getElementById('esp32CommunicationResponseText');
        if (esp32CommunicationResponseText && this.sensorData.esp32CommunicationResponseText !== undefined) {
            esp32CommunicationResponseText.innerHTML = `${this.sensorData.esp32CommunicationResponseText}`;
        }
        

        // 更新模式
        const mode = document.getElementById('mode');
        if (mode && this.sensorData.mode !== undefined) {
            if (this.sensorData.mode == 0) {
                mode.textContent = "正常"
            } else if (this.sensorData.mode == 1) {
                mode.textContent = "定深"
            }
        }

        // 更新深度
        const overlayDepth = document.getElementById('overlayDepth');
        if (overlayDepth && this.sensorData.depth !== undefined) {
            if(this.sensorData.isUnderwater){
                overlayDepth.textContent = `-${this.sensorData.depth.toFixed(2)}m`;
            }else{
                overlayDepth.textContent = `+${this.sensorData.depth.toFixed(2)}m`;
            }
        }

        // 更新温度
        const overlayTemperature = document.getElementById('overlayTemperature');
        if (overlayTemperature && this.sensorData.temperature !== undefined) {
            overlayTemperature.textContent = `${this.sensorData.temperature.toFixed(1)}°C`;
        }

        // 更新压力
        const overlayPressure = document.getElementById('overlayPressure');
        if (overlayPressure && this.sensorData.pressure !== undefined) {
            overlayPressure.textContent = `${this.sensorData.pressure.toFixed(1)}hPa`;
        }

        // 更新入水状态
        const overlayUnderwaterStatus = document.getElementById('overlayUnderwaterStatus');
        const overlayUnderwaterTrend = document.getElementById('overlayUnderwaterTrend');

        if (overlayUnderwaterStatus && this.sensorData.isUnderwater !== undefined) {
            if (this.sensorData.isUnderwater) {

                if(this.surfacePressure==0){
                    this.surfacePressure=this.sensorData.pressure;
                    localStorage.setItem("ROV_surfacePressure",this.sensorData.pressure);
                    console.warn(this.surfacePressure)
                }

                overlayUnderwaterStatus.textContent = '入水';
                overlayUnderwaterStatus.style.color = '#2196F3'; // 蓝色表示入水
            } else {
                overlayUnderwaterStatus.textContent = '水面';
                overlayUnderwaterStatus.style.color = '#4CAF50'; // 绿色表示水面
            }

            // this.sensorData.waterDetectionValue=100

            if (this.sensorData.waterDetectionValue < 30) {
                overlayUnderwaterTrend.style.color = "#FF0000"
                // overlayUnderwaterTrend.style.fontSize = "60px"
            } else {
                overlayUnderwaterTrend.style.color = '#4CAF50'
                // overlayUnderwaterTrend.style.fontSize = "14px"

            }
            overlayUnderwaterTrend.textContent = '(' + this.sensorData.waterDetectionValue + ')'
        }

        // 更新电池电压
        const overlayBattery = document.getElementById('overlayBattery');
        if (overlayBattery && this.sensorData.adcVoltage !== undefined) {
            overlayBattery.textContent = `${this.sensorData.adcVoltage.toFixed(2)}V`;

            // 根据电压设置颜色
            if (this.sensorData.adcVoltage < 1.0) {
                overlayBattery.style.color = '#ff6b6b';
                overlayBattery.style.fontSize = '20px'
            } else if (this.sensorData.adcVoltage < 3.5 * 6) {
                overlayBattery.style.color = '#ff6b6b';
                overlayBattery.style.fontSize = '80px';
            } else if (this.sensorData.adcVoltage < 3.7 * 6) {
                overlayBattery.style.color = '#ffa726';
                overlayBattery.style.fontSize = '60px'
            } else {
                overlayBattery.style.color = '#4CAF50';
                overlayBattery.style.fontSize = '20px'
            }
        }

        // 更新航向
        const overlayHeading = document.getElementById('overlayHeading');
        if (overlayHeading && this.sensorData.rovHeading !== undefined) {
            overlayHeading.textContent = `${this.sensorData.rovHeading}°`;
        }

        // 更新姿态（显示Roll角度）
        // const overlayAttitude = document.getElementById('overlayAttitude');
        // if (overlayAttitude && this.sensorData.roll !== undefined) {
        //     overlayAttitude.textContent = `${this.sensorData.roll.toFixed(1)}°`;
        // }

        // 更新延迟
        // TODO sever未启动时，网络显示断开
        const overlayLatency = document.getElementById('overlayLatency');
        if (overlayLatency && this.sensorData.serialLatency !== undefined) {
            overlayLatency.textContent = `${this.sensorData.serialLatency}ms`;
            // 根据延迟设置颜色
            overlayLatency.style.fontSize = "16px";
            if (this.sensorData.serialLatency < 20) {
                overlayLatency.style.color = '#4CAF50';
            } else if (this.sensorData.serialLatency < 100) {
                overlayLatency.style.color = '#ffa726';
            } else {
                overlayLatency.style.color = '#ff2222';
                overlayLatency.style.fontSize = "40px";
            }
            if (this.sensorData.serialResponseStatus != "success") {
                overlayLatency.style.color = '#ff2222';
                overlayLatency.style.fontSize = "1rem";
                overlayLatency.textContent = this.sensorData.serialResponseStatus
            }
        }
    }

    // 初始化手机罗盘
    async initPhoneCompass() {
        console.log('🧭 初始化手机罗盘...');

        // 检查设备是否支持罗盘
        const isSupported = this.isCompassSupported();
        if (!isSupported) {
            console.log('⚠️ 设备可能不支持罗盘功能，但仍尝试启动');
            this.showCompassStatus('尝试启动...', '#ff9800');
        } else {
            this.compassSupported = true;
            console.log('✅ 设备支持罗盘功能');
        }

        // 检查是否需要用户交互来请求权限
        if (typeof DeviceOrientationEvent.requestPermission === 'function') {
            // iOS 13+ 需要用户交互
            console.log('📱 iOS设备，显示权限按钮');
            this.showCompassPermissionButton();
        } else {
            // 其他平台直接尝试启动
            console.log('🤖 Android或其他平台，直接尝试启动');
            try {
                await this.requestCompassPermission();
                this.startCompassReading();
            } catch (error) {
                console.error('❌ 罗盘初始化失败:', error);
                this.showCompassStatus('初始化失败', '#ff6b35');
                // 即使失败也显示按钮，让用户手动尝试
                this.showCompassPermissionButton();
            }
        }
    }

    // 显示权限请求按钮
    showCompassPermissionButton() {
        console.log('🔘 显示罗盘权限按钮');
        const button = document.getElementById('compassPermissionBtn');
        if (button) {
            button.style.display = 'block';
            button.textContent = '启用罗盘';
            button.onclick = async () => {
                console.log('👆 用户点击启用罗盘按钮');
                button.textContent = '请求权限...';
                this.showCompassStatus('请求权限...', '#45b7d1');

                try {
                    await this.requestCompassPermission();
                    this.startCompassReading();
                    button.style.display = 'none';
                } catch (error) {
                    console.error('❌ 权限请求失败:', error);

                    // 提供更详细的错误信息和解决方案
                    if (error.message.includes('denied') || error.message.includes('拒绝')) {
                        this.showCompassStatus('权限被拒绝', '#ff6b35');
                        button.textContent = '查看帮助';

                        // 显示iOS权限指导
                        setTimeout(() => {
                            this.showIOSPermissionGuide();
                            this.showCompassStatus('点击查看帮助', '#ff9800');
                        }, 1000);
                    } else {
                        this.showCompassStatus('启动失败', '#ff6b35');
                        button.textContent = '重试';
                    }
                }
            };
        } else {
            console.error('❌ 找不到罗盘权限按钮元素');
        }
        this.showCompassStatus('点击启用', '#ff9800');
    }

    // 检查设备是否支持罗盘
    isCompassSupported() {
        console.log('🔍 开始检测设备罗盘支持...');

        // 检查基本的DeviceOrientationEvent支持
        if (!('DeviceOrientationEvent' in window)) {
            console.log('❌ DeviceOrientationEvent 不支持');
            return false;
        }

        // 检查是否在安全上下文中
        const isSecureContext = window.isSecureContext ||
            location.protocol === 'https:' ||
            location.hostname === 'localhost' ||
            location.hostname === '127.0.0.1' ||
            location.hostname.startsWith('192.168.') ||
            location.hostname.startsWith('10.') ||
            location.hostname.startsWith('172.');

        console.log('🔒 安全上下文检查:', isSecureContext);
        console.log('🔒 当前协议:', window.location.protocol);
        console.log('🔒 当前主机:', window.location.hostname);

        // 检查是否在移动设备上
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        console.log('📱 移动设备检测:', isMobile);
        console.log('📱 用户代理:', navigator.userAgent);

        // 尝试创建一个测试事件监听器来验证支持
        try {
            const testHandler = () => { };
            window.addEventListener('deviceorientation', testHandler);
            window.removeEventListener('deviceorientation', testHandler);
            console.log('✅ deviceorientation 事件监听器测试通过');
        } catch (error) {
            console.log('⚠️ deviceorientation 事件监听器测试失败:', error);
        }

        // 即使检测不完美也返回true，让用户尝试
        console.log('✅ DeviceOrientationEvent 支持检测完成，允许尝试');
        return true;
    }

    // 请求罗盘权限
    async requestCompassPermission() {
        console.log('🔐 开始请求罗盘权限...');

        // iOS 13+ 需要请求权限
        if (typeof DeviceOrientationEvent.requestPermission === 'function') {
            console.log('📱 检测到iOS设备，请求权限...');

            try {
                // 先检查当前权限状态
                console.log('📱 正在请求DeviceOrientationEvent权限...');
                const permission = await DeviceOrientationEvent.requestPermission();
                console.log('📱 iOS权限请求结果:', permission);

                if (permission === 'granted') {
                    console.log('✅ iOS设备方向权限已授予');
                    this.compassPermissionGranted = true;
                    return true;
                } else if (permission === 'denied') {
                    console.log('❌ iOS设备方向权限被明确拒绝');
                    throw new Error('权限被用户拒绝，请在Safari设置中启用运动与方向权限');
                } else {
                    console.log('⚠️ iOS设备方向权限状态未知:', permission);
                    throw new Error(`权限状态未知: ${permission}`);
                }
            } catch (error) {
                console.error('❌ iOS权限请求异常:', error);

                // 如果是权限相关错误，提供更具体的指导
                if (error.name === 'NotAllowedError' || error.message.includes('denied')) {
                    throw new Error('权限被拒绝，请检查Safari浏览器设置中的运动与方向权限');
                } else {
                    throw new Error(`权限请求失败: ${error.message}`);
                }
            }
        } else {
            // 非iOS或旧版本iOS，直接授予权限
            console.log('🤖 非iOS设备或旧版本iOS，直接授予权限');
            this.compassPermissionGranted = true;
            return true;
        }
    }

    // 开始读取罗盘数据
    startCompassReading() {
        console.log('🧭 开始读取手机罗盘数据...');
        console.log('🔍 权限状态:', this.compassPermissionGranted);
        console.log('🔍 支持状态:', this.compassSupported);

        this.showCompassStatus('正在读取...', '#45b7d1');

        // 监听设备方向变化
        const handleOrientation = (event) => {
            this.handleCompassData(event);
        };

        window.addEventListener('deviceorientation', handleOrientation);
        console.log('📡 已添加deviceorientation事件监听器');

        // 设置超时检测
        setTimeout(() => {
            if (this.lastPhoneHeading === 0 && !this.compassDataReceived) {
                console.log('⚠️ 3秒内未检测到罗盘数据');
                this.showCompassStatus('移动设备试试', '#ff9800');

                // 再等5秒，如果还没有数据就显示调试信息
                setTimeout(() => {
                    if (!this.compassDataReceived) {
                        console.log('❌ 8秒内仍未收到罗盘数据，可能不支持');
                        this.showCompassStatus('可能不支持', '#ff6b35');
                    }
                }, 5000);
            }
        }, 3000);
    }

    // 处理罗盘数据
    handleCompassData(event) {
        let heading = null;
        let source = '';

        // 获取航向数据
        if (event.webkitCompassHeading !== undefined && event.webkitCompassHeading !== null) {
            // iOS Safari
            heading = event.webkitCompassHeading;
            source = 'iOS';
        } else if (event.alpha !== null && event.alpha !== undefined) {
            // Android Chrome 和其他浏览器
            heading = 360 - event.alpha;
            source = 'Android';
        }

        if (heading !== null && !isNaN(heading)) {
            // 确保航向在0-360度范围内
            heading = ((heading % 360) + 360) % 360;

            // 更新手机航向数据
            this.sensorData.phoneHeading = Math.round(heading);
            this.lastPhoneHeading = heading;

            // 更新UI显示
            this.updatePhoneHeadingUI();

            // 首次获取数据时更新状态和日志
            if (this.lastPhoneHeading > 0 && !this.compassDataReceived) {
                this.compassDataReceived = true;
                console.log(`✅ 手机罗盘数据正常 (${source}): ${heading.toFixed(1)}°`);
                this.showCompassStatus('正常', '#4CAF50');
            }
        } else {
            // 调试信息
            if (!this.compassDebugLogged) {
                console.log('🔍 罗盘调试信息:', {
                    webkitCompassHeading: event.webkitCompassHeading,
                    alpha: event.alpha,
                    beta: event.beta,
                    gamma: event.gamma
                });
                this.compassDebugLogged = true;
            }
        }
    }

    // 更新手机航向UI显示
    updatePhoneHeadingUI() {
        // 更新手机航向数值
        const phoneHeadingElement = document.getElementById('phoneHeading');
        if (phoneHeadingElement) {
            phoneHeadingElement.textContent = `${this.sensorData.phoneHeading}°`;
        }

        // 更新手机指南针
        this.updateCompass('phoneCompass', this.sensorData.phoneHeading);
    }

    // 显示罗盘状态
    showCompassStatus(status, color) {
        const phoneHeadingElement = document.getElementById('phoneHeading');
        if (phoneHeadingElement) {
            phoneHeadingElement.textContent = status;
            phoneHeadingElement.style.color = color;
        }
    }

    // 检查iOS权限状态
    async checkiOSPermissionStatus() {
        if (typeof DeviceOrientationEvent.requestPermission === 'function') {
            try {
                // 先尝试检查当前权限状态
                const permission = await DeviceOrientationEvent.requestPermission();
                console.log('📱 iOS权限状态检查结果:', permission);
                return permission;
            } catch (error) {
                console.log('📱 iOS权限状态检查失败:', error);
                return 'unknown';
            }
        }
        return 'not-ios';
    }

    // 显示iOS权限指导
    showIOSPermissionGuide() {
        const button = document.getElementById('compassPermissionBtn');
        if (button) {
            button.textContent = '强制尝试';
            button.onclick = () => {
                // 显示帮助信息
                const showHelp = confirm(`iOS罗盘权限被拒绝！

解决方案：
1. 打开 iPhone 设置 → Safari → 隐私与安全性 → 运动与方向 → 开启
2. 重新加载页面并重试

点击"确定"强制尝试启动（可能无效）
点击"取消"查看详细帮助`);

                if (showHelp) {
                    // 强制尝试启动，即使没有权限
                    console.log('🚀 强制尝试启动罗盘...');
                    this.showCompassStatus('强制启动中...', '#ff9800');
                    this.compassPermissionGranted = true; // 强制设置为true
                    this.startCompassReading();
                    button.style.display = 'none';
                } else {
                    // 显示详细帮助
                    alert(`详细设置步骤：

📱 Safari浏览器设置：
1. 打开"设置"应用
2. 向下滚动找到"Safari"
3. 点击"隐私与安全性"
4. 找到"运动与方向"
5. 确保开关是绿色（开启状态）

🔄 重试步骤：
1. 完成上述设置后
2. 关闭Safari并重新打开
3. 重新访问此页面
4. 点击"启用罗盘"按钮

💡 其他浏览器：
如果Safari不工作，可以尝试：
- Chrome浏览器
- Firefox浏览器
- Edge浏览器`);
                }
            };
        }
    }

    // [ ]开始数据更新循环 - 主动请求数据
    startDataUpdate() {
        clearInterval(this.pingIntervalObj)

        // 保留心跳包用于连接检测，但降低频率
        this.pingIntervalObj = setInterval(() => {
            if (this.isConnected && this.received) {
                // this.sendCommand('ping');

                this.lastPingTime = Date.now();

                this.received = false;
                this.sendCommand('get_sensor_data');

                document.getElementById('latency').style.color = '#FFFFFF'
            }
            if (!this.isConnected || !this.received) {
                const latency = Date.now() - this.lastPingTime;
                // console.log('latency',latency)
                if (latency > 1000) {
                    this.lastPingTime = Date.now();

                    document.getElementById('latency').style.color = '#FF0000';
                    document.getElementById('latency').textContent = "Timeout";
                }
            }
        }, 200);

        console.log('📡 数据更新模式：被动接收（server主动推送）');
    }

    // 切换录制状态
    toggleRecording() {
        const btn = document.getElementById('recordBtn');
        const isRecording = btn.classList.contains('recording');

        if (isRecording) {
            btn.textContent = '🔴';
            btn.classList.remove('recording');
            this.sendCommand('stop_recording');
        } else {
            btn.textContent = '⏹️';
            btn.classList.add('recording');
            this.sendCommand('start_recording');
        }
    }

    // 显示错误信息
    showError(message) {
        const toast = document.getElementById('errorToast');
        toast.querySelector('.error-message').textContent = message;
        toast.style.display = 'flex';

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.style.display = 'none';
        }, 3000);
    }

    // 显示成功信息
    showSuccess(message) {
        // 可以添加成功提示的UI
        console.log('成功:', message);
    }

    // 添加调试控制（仅在debug模式下）
    addDebugControls() {
        const debugPanel = document.createElement('div');
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 12px;
        `;

        debugPanel.innerHTML = `
            <h4>调试控制</h4>
            <button onclick="window.rovController.testVideoStream()">测试视频流</button>
            <button onclick="window.rovController.switchVideoMode()">切换模式</button>
            <button onclick="window.rovController.reloadVideoStream()">重新加载</button>
        `;

        document.body.appendChild(debugPanel);
    }

    // 测试视频流连接
    testVideoStream() {
        const videoStream = document.getElementById('videoStream');
        const currentSrc = videoStream.src;
        console.log('当前视频源:', currentSrc);

        // 测试视频源是否可访问
        fetch('/video_feed')
            .then(response => {
                if (response.ok) {
                    console.log('✅ 视频流端点可访问');
                    this.showSuccess('视频流端点正常');
                } else {
                    console.log('❌ 视频流端点返回错误:', response.status);
                    this.showError(`视频流端点错误: ${response.status}`);
                }
            })
            .catch(error => {
                console.log('❌ 视频流端点不可访问:', error);
                this.showError('视频流端点不可访问');
            });
    }

    // 重新加载视频流
    reloadVideoStream() {
        const videoStream = document.getElementById('videoStream');
        const currentSrc = videoStream.src;

        if (currentSrc) {
            // 添加时间戳强制重新加载
            const separator = currentSrc.includes('?') ? '&' : '?';
            videoStream.src = currentSrc + separator + 't=' + Date.now();
            console.log('重新加载视频流:', videoStream.src);
        } else {
            // 如果没有源，重新初始化
            videoStream.src = '/video_feed?t=' + Date.now();
            console.log('初始化视频流:', videoStream.src);
        }
    }

    // 检查视频流状态
    checkVideoStreamStatus() {
        const videoStream = document.getElementById('videoStream');
        const videoStatus = document.getElementById('videoStatus');

        // 检查视频流是否已加载
        if (videoStream.complete && videoStream.naturalWidth > 0) {
            videoStatus.textContent = 'MJPEG流 - 已连接';
            videoStatus.style.backgroundColor = 'rgba(0, 128, 0, 0.7)';
            console.log('✅ 视频流状态正常');
        } else {
            videoStatus.textContent = 'MJPEG流 - 连接中...';
            videoStatus.style.backgroundColor = 'rgba(255, 165, 0, 0.7)';
            console.log('⚠️ 视频流未加载，尝试重新连接');

            // 如果视频流没有源，设置默认源
            if (!videoStream.src) {
                videoStream.src = '/video_feed?t=' + Date.now();
            }
        }
    }

    // ADC校准功能
    calibrateADC() {
        const referenceVoltageInput = document.getElementById('referenceVoltage');
        const calibrationResult = document.getElementById('calibrationResult');

        const referenceVoltage = parseFloat(referenceVoltageInput.value);

        if (!referenceVoltage || referenceVoltage <= 0 || referenceVoltage > 10) {
            calibrationResult.textContent = '请输入有效的参考电压值 (0-10V)';
            calibrationResult.style.color = '#ff6b6b';
            return;
        }

        // 发送校准命令
        this.sendCommand('calibrate_adc', { voltage: referenceVoltage });

        calibrationResult.textContent = `正在校准ADC，参考电压: ${referenceVoltage.toFixed(3)}V...`;
        calibrationResult.style.color = '#ffd93d';

        console.log(`发送ADC校准命令，参考电压: ${referenceVoltage}V`);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.rovController = new ROVController();
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('页面隐藏，暂停更新');
    } else {
        console.log('页面显示，恢复更新');
    }
});
