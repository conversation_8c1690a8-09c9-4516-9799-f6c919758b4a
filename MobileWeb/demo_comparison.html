<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境传感器卡片 - 合并前后对比</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .comparison-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .comparison-title {
            color: #ffffff;
            font-size: 1.5rem;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .comparison-subtitle {
            color: #b0b0b0;
            font-size: 1rem;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }
        
        .stat-item {
            text-align: center;
            color: #ffffff;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4ade80;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #b0b0b0;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: white; text-align: center; margin: 20px 0;">环境传感器卡片优化对比</h1>
        
        <!-- 合并后的新设计 -->
        <div class="comparison-section">
            <h2 class="comparison-title">✨ 合并后 - 环境传感器统一卡片</h2>
            <p class="comparison-subtitle">将深度、水温、压力合并在一个卡片中，节省空间，信息更集中</p>
            
            <div class="status-grid">
                <div class="status-card environment-card">
                    <div class="status-icon">🌊</div>
                    <div class="status-info">
                        <h3>环境传感器</h3>
                        <div class="environment-grid">
                            <div class="env-item">
                                <span class="env-label">深度</span>
                                <span class="env-value" id="depthValue1">2.5 m</span>
                                <span class="env-trend">稳定</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">水温</span>
                                <span class="env-value" id="temperatureValue1">24.3°C</span>
                                <span class="env-trend">正常</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">压力</span>
                                <span class="env-value" id="pressureValue1">1025.6 hPa</span>
                                <span class="env-trend">正常</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value">1</div>
                    <div class="stat-label">卡片数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">33%</div>
                    <div class="stat-label">空间节省</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">高</div>
                    <div class="stat-label">信息密度</div>
                </div>
            </div>
        </div>

        <!-- 合并前的原设计 -->
        <div class="comparison-section">
            <h2 class="comparison-title">📊 合并前 - 独立的三个卡片</h2>
            <p class="comparison-subtitle">深度、水温、压力分别使用独立卡片，占用更多空间</p>
            
            <div class="status-grid">
                <!-- 深度卡片 -->
                <div class="status-card" style="border-left: 4px solid #00d4ff;">
                    <div class="status-icon" style="color: #00d4ff;">🌊</div>
                    <div class="status-info">
                        <h3>深度</h3>
                        <div class="status-value" id="depthValue2">2.5 m</div>
                        <div class="status-trend">稳定</div>
                    </div>
                </div>

                <!-- 水温卡片 -->
                <div class="status-card" style="border-left: 4px solid #ff6b35;">
                    <div class="status-icon" style="color: #ff6b35;">🌡️</div>
                    <div class="status-info">
                        <h3>水温</h3>
                        <div class="status-value" id="temperatureValue2">24.3°C</div>
                        <div class="status-trend">正常</div>
                    </div>
                </div>

                <!-- 压力卡片 -->
                <div class="status-card" style="border-left: 4px solid #4ecdc4;">
                    <div class="status-icon" style="color: #4ecdc4;">📊</div>
                    <div class="status-info">
                        <h3>压力</h3>
                        <div class="status-value" id="pressureValue2">1025.6 hPa</div>
                        <div class="status-trend">正常</div>
                    </div>
                </div>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value">3</div>
                    <div class="stat-label">卡片数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">100%</div>
                    <div class="stat-label">原始空间</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">低</div>
                    <div class="stat-label">信息密度</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        function updateTestData() {
            const depth = (Math.random() * 5 + 1).toFixed(1);
            const temp = (Math.random() * 10 + 20).toFixed(1);
            const pressure = (Math.random() * 50 + 1000).toFixed(1);
            
            // 更新合并后的卡片
            document.getElementById('depthValue1').textContent = depth + ' m';
            document.getElementById('temperatureValue1').textContent = temp + '°C';
            document.getElementById('pressureValue1').textContent = pressure + ' hPa';
            
            // 更新合并前的卡片
            document.getElementById('depthValue2').textContent = depth + ' m';
            document.getElementById('temperatureValue2').textContent = temp + '°C';
            document.getElementById('pressureValue2').textContent = pressure + ' hPa';
        }

        // 每3秒更新一次数据
        setInterval(updateTestData, 3000);
        
        console.log('环境传感器卡片对比演示页面已加载');
    </script>
</body>
</html>
