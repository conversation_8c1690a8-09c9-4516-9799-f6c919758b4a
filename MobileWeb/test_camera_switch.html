<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头切换功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .camera-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .camera-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .camera-item.active {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
        }
        
        .camera-preview {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .camera-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.05) 10px,
                rgba(255, 255, 255, 0.05) 20px
            );
        }
        
        .camera-info {
            color: #ffffff;
        }
        
        .camera-label {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .camera-status {
            font-size: 0.9rem;
            color: #b0b0b0;
        }
        
        .camera-item.active .camera-status {
            color: #4CAF50;
        }
        
        .button-demo {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .demo-button {
            padding: 12px 24px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .demo-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .demo-button.lb {
            background: #2196F3;
        }
        
        .demo-button.lb:hover {
            background: #1976D2;
        }
        
        .demo-button.rb {
            background: #FF9800;
        }
        
        .demo-button.rb:hover {
            background: #F57C00;
        }
        
        .controller-visual {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        
        .controller {
            width: 300px;
            height: 150px;
            background: #333;
            border-radius: 30px;
            position: relative;
            border: 3px solid #555;
        }
        
        .shoulder-button {
            position: absolute;
            width: 40px;
            height: 20px;
            background: #666;
            border-radius: 5px;
            top: -15px;
            border: 2px solid #888;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .shoulder-button.left {
            left: 30px;
        }
        
        .shoulder-button.right {
            right: 30px;
        }
        
        .shoulder-button.active {
            background: #4CAF50;
            border-color: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }
        
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .log-entry {
            margin: 2px 0;
            word-wrap: break-word;
        }
        
        .log-info { color: #0ff; }
        .log-success { color: #0f0; }
        .log-warning { color: #ff0; }
        .log-error { color: #f00; }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">摄像头切换功能测试</h1>
            
            <!-- 功能说明 -->
            <div class="demo-section">
                <h3 style="color: white;">功能说明</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>摄像头切换功能:</strong></p>
                    <ul>
                        <li>🎮 <strong>LB键</strong>: 切换到摄像头0 (主摄像头)</li>
                        <li>🎮 <strong>RB键</strong>: 切换到摄像头2 (副摄像头)</li>
                        <li>📹 <strong>自动检测</strong>: 如果目标摄像头无法打开，自动回退到原摄像头</li>
                        <li>⚡ <strong>实时切换</strong>: 无需重启系统，实时切换摄像头</li>
                    </ul>
                    
                    <p><strong>技术实现:</strong></p>
                    <ul>
                        <li>上位机检测LB/RB按键并发送到下位机</li>
                        <li>下位机调用<code>switch_camera()</code>函数切换摄像头</li>
                        <li>使用OpenCV的<code>cv2.VideoCapture</code>进行摄像头管理</li>
                        <li>支持V4L2驱动的USB摄像头</li>
                    </ul>
                </div>
            </div>
            
            <!-- 摄像头状态显示 -->
            <div class="demo-section">
                <h3 style="color: white;">摄像头状态</h3>
                <div class="camera-display">
                    <div class="camera-item active" id="camera0">
                        <div class="camera-preview">
                            <div style="z-index: 1;">📹 摄像头 0</div>
                        </div>
                        <div class="camera-info">
                            <div class="camera-label">主摄像头</div>
                            <div class="camera-status">当前激活</div>
                        </div>
                    </div>
                    
                    <div class="camera-item" id="camera2">
                        <div class="camera-preview">
                            <div style="z-index: 1;">📹 摄像头 2</div>
                        </div>
                        <div class="camera-info">
                            <div class="camera-label">副摄像头</div>
                            <div class="camera-status">待机</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 手柄控制器可视化 -->
            <div class="demo-section">
                <h3 style="color: white;">Xbox手柄控制</h3>
                <div class="controller-visual">
                    <div class="controller">
                        <div class="shoulder-button left" id="lbButton" onclick="simulateLB()">LB</div>
                        <div class="shoulder-button right" id="rbButton" onclick="simulateRB()">RB</div>
                    </div>
                </div>
                <div style="text-align: center; color: #b0b0b0; margin-top: 10px;">
                    点击LB/RB按钮模拟手柄操作
                </div>
            </div>
            
            <!-- 测试按钮 -->
            <div class="demo-section">
                <h3 style="color: white;">测试控制</h3>
                <div class="button-demo">
                    <button class="demo-button lb" onclick="switchToCamera0()">🎮 LB - 切换到摄像头0</button>
                    <button class="demo-button rb" onclick="switchToCamera2()">🎮 RB - 切换到摄像头2</button>
                    <button class="demo-button" onclick="resetCameras()">🔄 重置状态</button>
                    <button class="demo-button" onclick="clearLog()">🗑️ 清空日志</button>
                </div>
            </div>
            
            <!-- 数据流程 -->
            <div class="demo-section">
                <h3 style="color: white;">数据流程</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; color: #0ff; overflow-x: auto;">
┌─────────────┐    按键检测    ┌─────────────┐    WebSocket    ┌─────────────┐
│ Xbox手柄    │ ──────────► │ 上位机      │ ──────────► │ 下位机      │
│ LB/RB按键   │              │ server.py   │              │ OrangePiZero3│
└─────────────┘              └─────────────┘              └─────────────┘
                                     │                            │
                                     ▼                            ▼
                             ┌─────────────┐              ┌─────────────┐
                             │ joystick_   │              │ switch_     │
                             │ control消息 │              │ camera()    │
                             └─────────────┘              └─────────────┘
                                     │                            │
                                     ▼                            ▼
                             ┌─────────────┐              ┌─────────────┐
                             │ LB: 1 或    │              │ cv2.Video   │
                             │ RB: 1       │              │ Capture切换 │
                             └─────────────┘              └─────────────┘</pre>
                </div>
            </div>
            
            <!-- 通信日志 -->
            <div class="demo-section">
                <h3 style="color: white;">操作日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[INFO] 摄像头切换功能测试页面已加载</div>
                    <div class="log-entry log-success">[SUCCESS] 当前摄像头: 摄像头0 (主摄像头)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentCamera = 0;
        let switchCount = 0;

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条数
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // 更新摄像头状态显示
        function updateCameraDisplay(cameraIndex) {
            // 移除所有active状态
            document.getElementById('camera0').classList.remove('active');
            document.getElementById('camera2').classList.remove('active');
            
            // 更新状态文本
            document.querySelector('#camera0 .camera-status').textContent = '待机';
            document.querySelector('#camera2 .camera-status').textContent = '待机';
            
            // 设置当前摄像头为active
            const activeCamera = document.getElementById(`camera${cameraIndex}`);
            if (activeCamera) {
                activeCamera.classList.add('active');
                activeCamera.querySelector('.camera-status').textContent = '当前激活';
            }
            
            currentCamera = cameraIndex;
        }

        // 模拟LB按键
        function simulateLB() {
            const lbButton = document.getElementById('lbButton');
            lbButton.classList.add('active');
            
            setTimeout(() => {
                lbButton.classList.remove('active');
            }, 200);
            
            switchToCamera0();
        }

        // 模拟RB按键
        function simulateRB() {
            const rbButton = document.getElementById('rbButton');
            rbButton.classList.add('active');
            
            setTimeout(() => {
                rbButton.classList.remove('active');
            }, 200);
            
            switchToCamera2();
        }

        // 切换到摄像头0
        function switchToCamera0() {
            if (currentCamera === 0) {
                addLog('摄像头已经是0，无需切换', 'warning');
                return;
            }
            
            addLog('🎮 LB键按下 - 切换到摄像头0', 'info');
            addLog('📹 释放当前摄像头[2]', 'warning');
            addLog('🔄 正在初始化摄像头[0]...', 'info');
            
            setTimeout(() => {
                updateCameraDisplay(0);
                addLog('✅ 摄像头[0]切换成功 - 分辨率: 640x480, 帧率: 30', 'success');
                switchCount++;
            }, 500);
        }

        // 切换到摄像头2
        function switchToCamera2() {
            if (currentCamera === 2) {
                addLog('摄像头已经是2，无需切换', 'warning');
                return;
            }
            
            addLog('🎮 RB键按下 - 切换到摄像头2', 'info');
            addLog('📹 释放当前摄像头[0]', 'warning');
            addLog('🔄 正在初始化摄像头[2]...', 'info');
            
            setTimeout(() => {
                updateCameraDisplay(2);
                addLog('✅ 摄像头[2]切换成功 - 分辨率: 640x480, 帧率: 30', 'success');
                switchCount++;
            }, 500);
        }

        // 重置摄像头状态
        function resetCameras() {
            addLog('🔄 重置摄像头状态', 'info');
            updateCameraDisplay(0);
            switchCount = 0;
            addLog('✅ 已重置到摄像头0', 'success');
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 模拟错误情况
        function simulateError() {
            addLog('❌ 无法打开摄像头[2]，回退到摄像头[0]', 'error');
            addLog('⚠️ 已回退到摄像头[0]', 'warning');
            updateCameraDisplay(0);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateCameraDisplay(0);
            addLog('摄像头切换功能测试页面已初始化', 'success');
            addLog('当前支持的摄像头: 摄像头0, 摄像头2', 'info');
            
            // 模拟定期状态更新
            setInterval(() => {
                if (Math.random() > 0.95) {
                    addLog(`📊 摄像头[${currentCamera}]状态正常 - 已切换${switchCount}次`, 'info');
                }
            }, 5000);
        });
    </script>
</body>
</html>
