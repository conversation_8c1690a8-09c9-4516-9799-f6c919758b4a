#!/usr/bin/env python3
"""
测试server.py中的协议通信
"""

import sys
import os
import time

# 添加App目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from App.serial_port import SerialPort
except ImportError:
    print("❌ 无法导入串口模块")
    sys.exit(1)

def test_serial_send():
    """简单测试串口发送"""
    print("🧪 测试串口发送功能")
    
    try:
        # 创建串口实例
        serial_port = SerialPort(port='/dev/tty.usbserial-0001', baudrate=115200)
        
        # 打开串口
        if not serial_port.open():
            print("❌ 串口打开失败")
            return
            
        print("✅ 串口打开成功")
        
        # 等待ESP32启动
        time.sleep(2)
        
        # 测试send_hex方法
        print("📤 测试send_hex方法...")
        
        # 发送心跳包: AA FF 00 D7
        heartbeat = bytes([0xAA, 0xFF, 0x00, 0xD7])
        success = serial_port.send_hex(heartbeat)
        
        if success:
            print(f"✅ 心跳包发送成功: {heartbeat.hex().upper()}")
        else:
            print(f"❌ 心跳包发送失败")
            
        # 等待响应
        time.sleep(2)
        
        # 发送气压计请求: AA 02 00 2A
        pressure_req = bytes([0xAA, 0x02, 0x00, 0x2A])
        success = serial_port.send_hex(pressure_req)
        
        if success:
            print(f"✅ 气压计请求发送成功: {pressure_req.hex().upper()}")
        else:
            print(f"❌ 气压计请求发送失败")
            
        # 等待响应
        time.sleep(2)
        
        print("🎉 发送测试完成")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        try:
            serial_port.close()
            print("🔌 串口已关闭")
        except:
            pass

if __name__ == "__main__":
    test_serial_send()
