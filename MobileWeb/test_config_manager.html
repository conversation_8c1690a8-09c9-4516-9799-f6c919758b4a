<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理功能测试</title>
<link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-button {
            margin: 5px;
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .demo-button.save { background: #2196F3; }
        .demo-button.load { background: #FF9800; }
        .demo-button.reset { background: #f44336; }
        .demo-button.info { background: #9C27B0; }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .setting-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .setting-label {
            color: #4CAF50;
            font-weight: bold;
            margin-bottom: 8px;
            display: block;
        }
        
        .setting-input {
            width: 100%;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: white;
            font-family: 'Courier New', monospace;
        }
        
        .setting-value {
            color: #ffffff;
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 4px;
            margin-top: 5px;
        }
        
        .config-info {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #f44336; }
        
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .log-entry {
            margin: 2px 0;
            word-wrap: break-word;
        }
        
        .log-info { color: #0ff; }
        .log-success { color: #0f0; }
        .log-warning { color: #ff0; }
        .log-error { color: #f00; }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">配置管理功能测试</h1>
            
            <!-- 功能说明 -->
            <div class="demo-section">
                <h3 style="color: white;">配置管理功能说明</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>核心功能:</strong></p>
                    <ul style="color: #ffffff;">
                        <li>📁 <strong>配置文件管理</strong>: 自动保存和加载配置到data目录</li>
                        <li>⚙️ <strong>摄像头设置</strong>: 管理分辨率、帧率、图像处理参数</li>
                        <li>🔄 <strong>实时同步</strong>: 设置更改后自动保存到配置文件</li>
                        <li>🛡️ <strong>备份机制</strong>: 自动创建配置备份，防止数据丢失</li>
                        <li>✅ <strong>配置验证</strong>: 验证配置参数的有效性</li>
                        <li>🔧 <strong>默认值重置</strong>: 支持重置到默认配置</li>
                    </ul>
                    
                    <p><strong>配置文件位置:</strong></p>
                    <div class="config-info">
                        <div style="color: #0ff;">配置目录: rovRemote/data/</div>
                        <div style="color: #0ff;">配置文件: rov_config.json</div>
                        <div style="color: #0ff;">备份文件: rov_config_backup_*.json</div>
                    </div>
                </div>
            </div>
            
            <!-- 配置操作 -->
            <div class="demo-section">
                <h3 style="color: white;">配置操作</h3>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="demo-button save" onclick="saveConfig()">💾 保存配置</button>
                    <button class="demo-button load" onclick="loadConfig()">📂 重新加载配置</button>
                    <button class="demo-button reset" onclick="resetConfig()">🔄 重置配置</button>
                    <button class="demo-button info" onclick="getConfigInfo()">📊 获取配置信息</button>
                    <button class="demo-button" onclick="clearLog()">🗑️ 清空日志</button>
                </div>
            </div>
            
            <!-- 摄像头设置 -->
            <div class="demo-section">
                <h3 style="color: white;">摄像头设置</h3>
                <div class="settings-grid">
                    <div class="setting-item">
                        <label class="setting-label">分辨率 - 宽度</label>
                        <input type="number" class="setting-input" id="width" value="1280" min="320" max="1920">
                        <div class="setting-value" id="widthValue">1280</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">分辨率 - 高度</label>
                        <input type="number" class="setting-input" id="height" value="720" min="240" max="1080">
                        <div class="setting-value" id="heightValue">720</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">帧率 (FPS)</label>
                        <input type="number" class="setting-input" id="fps" value="30" min="1" max="60">
                        <div class="setting-value" id="fpsValue">30</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">图像质量</label>
                        <input type="number" class="setting-input" id="quality" value="85" min="1" max="100">
                        <div class="setting-value" id="qualityValue">85</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">对比度</label>
                        <input type="number" class="setting-input" id="contrast" value="1.0" min="0.5" max="4.0" step="0.1">
                        <div class="setting-value" id="contrastValue">1.0</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">饱和度</label>
                        <input type="number" class="setting-input" id="saturation" value="1.0" min="0.0" max="5.0" step="0.1">
                        <div class="setting-value" id="saturationValue">1.0</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">锐化强度</label>
                        <input type="number" class="setting-input" id="sharpness" value="0.0" min="0.0" max="3.0" step="0.1">
                        <div class="setting-value" id="sharpnessValue">0.0</div>
                    </div>
                    
                    <div class="setting-item">
                        <label class="setting-label">锐化方法</label>
                        <select class="setting-input" id="sharpenMethod">
                            <option value="standard">标准锐化</option>
                            <option value="unsharp_mask">Unsharp Mask</option>
                            <option value="laplacian">拉普拉斯</option>
                        </select>
                        <div class="setting-value" id="sharpenMethodValue">standard</div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 20px 0;">
                    <button class="demo-button" onclick="updateCameraSettings()">📹 更新摄像头设置</button>
                    <button class="demo-button" onclick="loadCurrentSettings()">🔄 加载当前设置</button>
                </div>
            </div>
            
            <!-- 配置状态 -->
            <div class="demo-section">
                <h3 style="color: white;">配置状态</h3>
                <div class="config-info" id="configStatus">
                    <div><span class="status-indicator status-success"></span>配置管理器已初始化</div>
                    <div>配置文件: 等待获取...</div>
                    <div>最后修改: 等待获取...</div>
                    <div>配置验证: 等待检查...</div>
                </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="demo-section">
                <h3 style="color: white;">操作日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[INFO] 配置管理功能测试页面已加载</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let currentSettings = {};

        // 初始化WebSocket连接
        function initWebSocket() {
            try {
                websocket = new WebSocket('ws://localhost:8765');
                
                websocket.onopen = function(event) {
                    addLog('WebSocket连接已建立', 'success');
                    getConfigInfo();
                    loadCurrentSettings();
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (e) {
                        addLog(`WebSocket消息解析错误: ${e}`, 'error');
                    }
                };
                
                websocket.onclose = function(event) {
                    addLog('WebSocket连接已关闭', 'warning');
                };
                
                websocket.onerror = function(error) {
                    addLog(`WebSocket错误: ${error}`, 'error');
                };
                
            } catch (e) {
                addLog(`WebSocket初始化失败: ${e}`, 'error');
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            if (data.type === 'response') {
                addLog(`命令响应: ${data.command} - ${data.message}`, 
                       data.success ? 'success' : 'error');
                
                if (data.camera_settings) {
                    updateSettingsDisplay(data.camera_settings);
                }
            } else if (data.type === 'config_info') {
                updateConfigStatus(data);
                if (data.camera_settings) {
                    updateSettingsDisplay(data.camera_settings);
                }
            }
        }

        // 发送WebSocket命令
        function sendCommand(command, data = {}) {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'command',
                    command: command,
                    ...data,
                    timestamp: Date.now()
                };
                websocket.send(JSON.stringify(message));
                addLog(`发送命令: ${command}`, 'info');
            } else {
                addLog('WebSocket未连接', 'error');
            }
        }

        // 保存配置
        function saveConfig() {
            sendCommand('save_config');
        }

        // 重新加载配置
        function loadConfig() {
            sendCommand('load_config');
        }

        // 重置配置
        function resetConfig() {
            if (confirm('确定要重置所有配置到默认值吗？')) {
                sendCommand('reset_config');
            }
        }

        // 获取配置信息
        function getConfigInfo() {
            sendCommand('get_config_info');
        }

        // 更新摄像头设置
        function updateCameraSettings() {
            const settings = {
                width: parseInt(document.getElementById('width').value),
                height: parseInt(document.getElementById('height').value),
                fps: parseInt(document.getElementById('fps').value),
                quality: parseInt(document.getElementById('quality').value),
                contrast: parseFloat(document.getElementById('contrast').value),
                saturation: parseFloat(document.getElementById('saturation').value),
                sharpness: parseFloat(document.getElementById('sharpness').value),
                sharpen_method: document.getElementById('sharpenMethod').value
            };
            
            sendCommand('update_camera_settings', { settings: settings });
        }

        // 加载当前设置
        function loadCurrentSettings() {
            getConfigInfo();
        }

        // 更新设置显示
        function updateSettingsDisplay(settings) {
            currentSettings = settings;
            
            document.getElementById('width').value = settings.width || 1280;
            document.getElementById('height').value = settings.height || 720;
            document.getElementById('fps').value = settings.fps || 30;
            document.getElementById('quality').value = settings.quality || 85;
            document.getElementById('contrast').value = settings.contrast || 1.0;
            document.getElementById('saturation').value = settings.saturation || 1.0;
            document.getElementById('sharpness').value = settings.sharpness || 0.0;
            document.getElementById('sharpenMethod').value = settings.sharpen_method || 'standard';
            
            // 更新显示值
            document.getElementById('widthValue').textContent = settings.width || 1280;
            document.getElementById('heightValue').textContent = settings.height || 720;
            document.getElementById('fpsValue').textContent = settings.fps || 30;
            document.getElementById('qualityValue').textContent = settings.quality || 85;
            document.getElementById('contrastValue').textContent = settings.contrast || 1.0;
            document.getElementById('saturationValue').textContent = settings.saturation || 1.0;
            document.getElementById('sharpnessValue').textContent = settings.sharpness || 0.0;
            document.getElementById('sharpenMethodValue').textContent = settings.sharpen_method || 'standard';
            
            addLog('摄像头设置已更新到界面', 'success');
        }

        // 更新配置状态
        function updateConfigStatus(data) {
            const statusDiv = document.getElementById('configStatus');
            const configInfo = data.config_info;
            const validation = data.validation;
            
            let statusHtml = '';
            
            // 配置文件状态
            if (configInfo.config_exists) {
                statusHtml += `<div><span class="status-indicator status-success"></span>配置文件存在: ${configInfo.config_path}</div>`;
                statusHtml += `<div>文件大小: ${configInfo.config_size} 字节</div>`;
                statusHtml += `<div>最后修改: ${new Date(configInfo.last_modified).toLocaleString()}</div>`;
            } else {
                statusHtml += `<div><span class="status-indicator status-warning"></span>配置文件不存在: ${configInfo.config_path}</div>`;
            }
            
            // 配置验证状态
            if (validation.valid) {
                statusHtml += `<div><span class="status-indicator status-success"></span>配置验证: 通过</div>`;
            } else {
                statusHtml += `<div><span class="status-indicator status-error"></span>配置验证: 失败</div>`;
                validation.errors.forEach(error => {
                    statusHtml += `<div style="color: #f44336; margin-left: 20px;">错误: ${error}</div>`;
                });
            }
            
            if (validation.warnings.length > 0) {
                validation.warnings.forEach(warning => {
                    statusHtml += `<div style="color: #FF9800; margin-left: 20px;">警告: ${warning}</div>`;
                });
            }
            
            // 配置节信息
            statusHtml += `<div>配置节: ${configInfo.sections.join(', ')}</div>`;
            
            statusDiv.innerHTML = statusHtml;
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条数
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 监听输入变化
        function setupInputListeners() {
            const inputs = ['width', 'height', 'fps', 'quality', 'contrast', 'saturation', 'sharpness'];
            inputs.forEach(id => {
                const input = document.getElementById(id);
                const valueDisplay = document.getElementById(id + 'Value');
                
                input.addEventListener('input', (e) => {
                    valueDisplay.textContent = e.target.value;
                });
            });
            
            // 锐化方法选择
            document.getElementById('sharpenMethod').addEventListener('change', (e) => {
                document.getElementById('sharpenMethodValue').textContent = e.target.value;
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('配置管理功能测试页面已初始化', 'success');
            setupInputListeners();
            initWebSocket();
        });
    </script>
</body>
</html>
