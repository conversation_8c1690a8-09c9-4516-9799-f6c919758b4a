<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像锐化功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-label {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
        }
        
        .slider-container {
            margin: 10px 0;
        }
        
        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #333;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            border: none;
        }
        
        .value-display {
            color: #4CAF50;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            margin-left: 10px;
        }
        
        .method-selector {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        
        .method-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .method-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .method-btn.active {
            background: #4CAF50;
            border-color: #4CAF50;
        }
        
        .demo-button {
            margin: 5px;
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #45a049;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .preview-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .preview-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #333, #555);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .preview-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 5px,
                rgba(255, 255, 255, 0.1) 5px,
                rgba(255, 255, 255, 0.1) 10px
            );
        }
        
        .preview-label {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .preview-description {
            color: #b0b0b0;
            font-size: 0.9rem;
        }
        
        .algorithm-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .algorithm-info h4 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .algorithm-info pre {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            color: #0ff;
            font-size: 0.8rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">图像锐化功能测试</h1>
            
            <!-- 功能说明 -->
            <div class="demo-section">
                <h3 style="color: white;">锐化功能说明</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>图像锐化技术:</strong></p>
                    <ul>
                        <li>🔍 <strong>标准锐化</strong>: 使用卷积核进行边缘增强</li>
                        <li>🎯 <strong>Unsharp Mask</strong>: 高质量的锐化算法，适合细节增强</li>
                        <li>⚡ <strong>拉普拉斯锐化</strong>: 基于边缘检测的锐化方法</li>
                        <li>🎛️ <strong>实时调节</strong>: 支持实时调整锐化强度和方法</li>
                    </ul>
                    
                    <p><strong>应用场景:</strong></p>
                    <ul>
                        <li>水下摄像头图像清晰度提升</li>
                        <li>低光环境下的细节增强</li>
                        <li>模糊图像的边缘锐化</li>
                        <li>实时视频流的图像优化</li>
                    </ul>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="demo-section">
                <h3 style="color: white;">锐化参数控制</h3>
                <div class="control-panel">
                    <div class="control-group">
                        <label class="control-label">锐化强度</label>
                        <div class="slider-container">
                            <input type="range" min="0" max="3" step="0.1" value="1.0" class="slider" id="sharpnessSlider">
                            <span class="value-display" id="sharpnessValue">1.0</span>
                        </div>
                        <div style="color: #b0b0b0; font-size: 0.8rem; margin-top: 5px;">
                            0.0 = 不锐化, 1.0 = 正常锐化, 3.0 = 强锐化
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">锐化方法</label>
                        <div class="method-selector">
                            <button class="method-btn active" data-method="standard">标准锐化</button>
                            <button class="method-btn" data-method="unsharp_mask">Unsharp Mask</button>
                            <button class="method-btn" data-method="laplacian">拉普拉斯</button>
                        </div>
                        <div style="color: #b0b0b0; font-size: 0.8rem; margin-top: 5px;">
                            选择不同的锐化算法
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 20px 0;">
                    <button class="demo-button" onclick="applySharpening()">应用锐化设置</button>
                    <button class="demo-button" onclick="resetSettings()">重置设置</button>
                    <button class="demo-button" onclick="previewEffects()">预览效果</button>
                </div>
            </div>
            
            <!-- 效果预览 -->
            <div class="demo-section">
                <h3 style="color: white;">锐化效果预览</h3>
                <div class="preview-container">
                    <div class="preview-item">
                        <div class="preview-image">
                            <div style="z-index: 1;">原始图像</div>
                        </div>
                        <div class="preview-label">原始</div>
                        <div class="preview-description">未处理的原始图像</div>
                    </div>
                    
                    <div class="preview-item">
                        <div class="preview-image" style="background: linear-gradient(45deg, #444, #666);">
                            <div style="z-index: 1;">标准锐化</div>
                        </div>
                        <div class="preview-label">标准锐化</div>
                        <div class="preview-description">使用卷积核的基础锐化</div>
                    </div>
                    
                    <div class="preview-item">
                        <div class="preview-image" style="background: linear-gradient(45deg, #555, #777);">
                            <div style="z-index: 1;">Unsharp Mask</div>
                        </div>
                        <div class="preview-label">Unsharp Mask</div>
                        <div class="preview-description">高质量的专业锐化</div>
                    </div>
                    
                    <div class="preview-item">
                        <div class="preview-image" style="background: linear-gradient(45deg, #666, #888);">
                            <div style="z-index: 1;">拉普拉斯锐化</div>
                        </div>
                        <div class="preview-label">拉普拉斯</div>
                        <div class="preview-description">基于边缘检测的锐化</div>
                    </div>
                </div>
            </div>
            
            <!-- 算法详解 -->
            <div class="demo-section">
                <h3 style="color: white;">锐化算法详解</h3>
                
                <div class="algorithm-info">
                    <h4>1. 标准锐化 (Standard Sharpening)</h4>
                    <p style="color: #ffffff;">使用卷积核对图像进行锐化处理：</p>
                    <pre>
锐化核矩阵:
[  0, -s,  0]
[ -s, 1+4s, -s]
[  0, -s,  0]

其中 s = 锐化强度 (0.0-3.0)
                    </pre>
                </div>
                
                <div class="algorithm-info">
                    <h4>2. Unsharp Mask 锐化</h4>
                    <p style="color: #ffffff;">专业的图像锐化技术：</p>
                    <pre>
1. 创建原图的高斯模糊版本
2. 计算原图与模糊图的差异 (mask)
3. 将差异按强度加回原图
锐化图像 = 原图 + 强度 × (原图 - 模糊图)
                    </pre>
                </div>
                
                <div class="algorithm-info">
                    <h4>3. 拉普拉斯锐化 (Laplacian Sharpening)</h4>
                    <p style="color: #ffffff;">基于边缘检测的锐化方法：</p>
                    <pre>
1. 使用拉普拉斯算子检测边缘
2. 将检测到的边缘信息加回原图
3. 增强图像的边缘和细节
锐化图像 = 原图 + 强度 × 拉普拉斯边缘
                    </pre>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="demo-section">
                <h3 style="color: white;">使用说明</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>参数设置:</strong></p>
                    <ul>
                        <li><code>sharpness</code>: 锐化强度 (0.0-3.0)</li>
                        <li><code>sharpen_method</code>: 锐化方法 ('standard', 'unsharp_mask', 'laplacian')</li>
                    </ul>
                    
                    <p><strong>函数调用示例:</strong></p>
                    <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; color: #0ff;">
# 设置锐化参数
rov_server.set_image_properties(sharpness=1.5, sharpen_method='unsharp_mask')

# 在generate_frames中自动应用
frame = self.adaptive_sharpen_frame(frame, sharpness, method)
                    </pre>
                    
                    <p><strong>推荐设置:</strong></p>
                    <ul>
                        <li><strong>水下清晰度提升</strong>: sharpness=1.0-1.5, method='unsharp_mask'</li>
                        <li><strong>低光环境</strong>: sharpness=0.8-1.2, method='standard'</li>
                        <li><strong>边缘增强</strong>: sharpness=1.2-2.0, method='laplacian'</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSettings = {
            sharpness: 1.0,
            method: 'standard'
        };

        // 初始化控件
        function initControls() {
            const sharpnessSlider = document.getElementById('sharpnessSlider');
            const sharpnessValue = document.getElementById('sharpnessValue');
            const methodButtons = document.querySelectorAll('.method-btn');

            // 锐化强度滑块
            sharpnessSlider.addEventListener('input', (e) => {
                currentSettings.sharpness = parseFloat(e.target.value);
                sharpnessValue.textContent = currentSettings.sharpness.toFixed(1);
                updatePreview();
            });

            // 锐化方法按钮
            methodButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    methodButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    currentSettings.method = btn.dataset.method;
                    updatePreview();
                });
            });
        }

        // 更新预览
        function updatePreview() {
            console.log(`当前设置: 锐化强度=${currentSettings.sharpness}, 方法=${currentSettings.method}`);
            
            // 更新预览图像的视觉效果
            const previewImages = document.querySelectorAll('.preview-image');
            previewImages.forEach((img, index) => {
                if (index > 0) {
                    const intensity = Math.min(currentSettings.sharpness / 3, 1);
                    const brightness = 1 + intensity * 0.3;
                    img.style.filter = `brightness(${brightness}) contrast(${1 + intensity * 0.5})`;
                }
            });
        }

        // 应用锐化设置
        function applySharpening() {
            console.log(`应用锐化设置: 强度=${currentSettings.sharpness}, 方法=${currentSettings.method}`);
            
            // 模拟发送设置到服务器
            const settings = {
                type: 'image_settings',
                sharpness: currentSettings.sharpness,
                sharpen_method: currentSettings.method
            };
            
            console.log('发送设置到服务器:', settings);
            alert(`锐化设置已应用!\n强度: ${currentSettings.sharpness}\n方法: ${currentSettings.method}`);
        }

        // 重置设置
        function resetSettings() {
            currentSettings = { sharpness: 0.0, method: 'standard' };
            
            document.getElementById('sharpnessSlider').value = 0.0;
            document.getElementById('sharpnessValue').textContent = '0.0';
            
            document.querySelectorAll('.method-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.method === 'standard') {
                    btn.classList.add('active');
                }
            });
            
            updatePreview();
            console.log('设置已重置');
        }

        // 预览效果
        function previewEffects() {
            const effects = [
                { sharpness: 0.0, method: 'standard', name: '无锐化' },
                { sharpness: 1.0, method: 'standard', name: '标准锐化' },
                { sharpness: 1.5, method: 'unsharp_mask', name: 'Unsharp Mask' },
                { sharpness: 2.0, method: 'laplacian', name: '拉普拉斯强锐化' }
            ];
            
            let index = 0;
            const interval = setInterval(() => {
                if (index >= effects.length) {
                    clearInterval(interval);
                    return;
                }
                
                const effect = effects[index];
                currentSettings = { ...effect };
                
                document.getElementById('sharpnessSlider').value = effect.sharpness;
                document.getElementById('sharpnessValue').textContent = effect.sharpness.toFixed(1);
                
                document.querySelectorAll('.method-btn').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.method === effect.method) {
                        btn.classList.add('active');
                    }
                });
                
                updatePreview();
                console.log(`预览效果 ${index + 1}: ${effect.name}`);
                
                index++;
            }, 2000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initControls();
            updatePreview();
            console.log('图像锐化功能测试页面已加载');
        });
    </script>
</body>
</html>
