<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频流测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .video-test {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        img, video {
            width: 100%;
            height: auto;
            display: block;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
        }
        .status.success { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.info { background: #2196F3; }
        button {
            background: #00d4ff;
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0099cc;
        }
        .info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎥 ROV视频流测试</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试不同的视频流显示方式，帮助诊断视频连接问题。</p>
            <p><strong>当前服务器:</strong> <span id="serverInfo"></span></p>
        </div>

        <!-- MJPEG流测试 (推荐) -->
        <div class="video-test">
            <h2>🖼️ MJPEG流测试 (推荐)</h2>
            <p>使用img标签显示MJPEG流，这是最兼容的方式</p>
            <div class="video-container">
                <img id="mjpegStream" alt="MJPEG视频流">
            </div>
            <div class="status info" id="mjpegStatus">准备测试...</div>
            <button onclick="testMJPEG()">开始测试</button>
            <button onclick="reloadMJPEG()">重新加载</button>
        </div>

        <!-- Video标签测试 -->
        <div class="video-test">
            <h2>📹 Video标签测试</h2>
            <p>使用video标签显示流，某些浏览器可能不支持MJPEG</p>
            <div class="video-container">
                <video id="videoStream" controls autoplay muted playsinline>
                    您的浏览器不支持视频播放
                </video>
            </div>
            <div class="status info" id="videoStatus">准备测试...</div>
            <button onclick="testVideo()">开始测试</button>
            <button onclick="reloadVideo()">重新加载</button>
        </div>

        <!-- 直接链接测试 -->
        <div class="video-test">
            <h2>🔗 直接链接测试</h2>
            <p>直接访问视频流URL进行测试</p>
            <div id="linkTests">
                <!-- 动态生成链接 -->
            </div>
            <button onclick="generateLinks()">生成测试链接</button>
        </div>

        <!-- 网络诊断 -->
        <div class="video-test">
            <h2>🌐 网络诊断</h2>
            <div id="networkDiag">
                <p>点击下方按钮进行网络连接测试</p>
            </div>
            <button onclick="testNetwork()">测试网络连接</button>
        </div>
    </div>

    <script>
        // 获取服务器信息
        document.getElementById('serverInfo').textContent = window.location.origin;

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function testMJPEG() {
            const img = document.getElementById('mjpegStream');
            const videoFeedUrl = '/video_feed';
            
            updateStatus('mjpegStatus', '正在连接MJPEG流...', 'info');
            
            img.onload = function() {
                updateStatus('mjpegStatus', '✅ MJPEG流连接成功！', 'success');
                console.log('MJPEG流加载成功');
            };
            
            img.onerror = function() {
                updateStatus('mjpegStatus', '❌ MJPEG流连接失败', 'error');
                console.log('MJPEG流加载失败');
            };
            
            // 添加时间戳避免缓存
            img.src = videoFeedUrl + '?t=' + Date.now();
        }

        function reloadMJPEG() {
            const img = document.getElementById('mjpegStream');
            const currentSrc = img.src;
            if (currentSrc) {
                const baseSrc = currentSrc.split('?')[0];
                img.src = baseSrc + '?t=' + Date.now();
                updateStatus('mjpegStatus', '重新加载中...', 'info');
            }
        }

        function testVideo() {
            const video = document.getElementById('videoStream');
            const videoFeedUrl = '/video_feed';
            
            updateStatus('videoStatus', '正在连接Video流...', 'info');
            
            video.onloadstart = function() {
                updateStatus('videoStatus', '开始加载视频...', 'info');
            };
            
            video.onloadeddata = function() {
                updateStatus('videoStatus', '✅ Video流数据加载成功！', 'success');
                console.log('Video流加载成功');
            };
            
            video.onerror = function() {
                updateStatus('videoStatus', '❌ Video流连接失败', 'error');
                console.log('Video流加载失败');
            };
            
            video.src = videoFeedUrl + '?t=' + Date.now();
        }

        function reloadVideo() {
            const video = document.getElementById('videoStream');
            const currentSrc = video.src;
            if (currentSrc) {
                const baseSrc = currentSrc.split('?')[0];
                video.src = baseSrc + '?t=' + Date.now();
                updateStatus('videoStatus', '重新加载中...', 'info');
            }
        }

        function generateLinks() {
            const linkContainer = document.getElementById('linkTests');
            const baseUrl = window.location.origin;
            
            const links = [
                '/video_feed',
                baseUrl + '/video_feed',
                'http://localhost:8088/video_feed',
                `http://${window.location.hostname}:8088/video_feed`
            ];
            
            linkContainer.innerHTML = '<h4>测试链接:</h4>';
            links.forEach((link, index) => {
                const linkElement = document.createElement('div');
                linkElement.innerHTML = `
                    <p><strong>链接 ${index + 1}:</strong> 
                    <a href="${link}" target="_blank" style="color: #00d4ff;">${link}</a>
                    <button onclick="testLink('${link}', ${index})">测试</button>
                    <span id="linkStatus${index}"></span>
                    </p>
                `;
                linkContainer.appendChild(linkElement);
            });
        }

        function testLink(url, index) {
            const statusSpan = document.getElementById(`linkStatus${index}`);
            statusSpan.textContent = '测试中...';
            statusSpan.style.color = 'orange';
            
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        statusSpan.textContent = '✅ 可访问';
                        statusSpan.style.color = 'green';
                    } else {
                        statusSpan.textContent = `❌ 错误 ${response.status}`;
                        statusSpan.style.color = 'red';
                    }
                })
                .catch(error => {
                    statusSpan.textContent = '❌ 连接失败';
                    statusSpan.style.color = 'red';
                    console.log('链接测试失败:', error);
                });
        }

        function testNetwork() {
            const diagContainer = document.getElementById('networkDiag');
            diagContainer.innerHTML = '<p>正在进行网络诊断...</p>';
            
            const tests = [
                { name: '服务器连接', url: '/' },
                { name: '视频流端点', url: '/video_feed' },
                { name: 'WebSocket端点', url: `ws://${window.location.hostname}:8765` }
            ];
            
            let results = '<h4>诊断结果:</h4>';
            let completedTests = 0;
            
            tests.forEach((test, index) => {
                if (test.url.startsWith('ws://')) {
                    // WebSocket测试
                    try {
                        const ws = new WebSocket(test.url);
                        ws.onopen = function() {
                            results += `<p>✅ ${test.name}: 连接成功</p>`;
                            ws.close();
                            updateDiagResults();
                        };
                        ws.onerror = function() {
                            results += `<p>❌ ${test.name}: 连接失败</p>`;
                            updateDiagResults();
                        };
                    } catch (error) {
                        results += `<p>❌ ${test.name}: ${error.message}</p>`;
                        updateDiagResults();
                    }
                } else {
                    // HTTP测试
                    fetch(test.url)
                        .then(response => {
                            if (response.ok) {
                                results += `<p>✅ ${test.name}: 状态 ${response.status}</p>`;
                            } else {
                                results += `<p>❌ ${test.name}: 错误 ${response.status}</p>`;
                            }
                            updateDiagResults();
                        })
                        .catch(error => {
                            results += `<p>❌ ${test.name}: 连接失败</p>`;
                            updateDiagResults();
                        });
                }
            });
            
            function updateDiagResults() {
                completedTests++;
                if (completedTests >= tests.length) {
                    diagContainer.innerHTML = results;
                }
            }
        }

        // 页面加载完成后自动生成链接
        window.onload = function() {
            generateLinks();
            console.log('视频流测试页面已加载');
            console.log('提示: 如果MJPEG流测试成功，说明视频流服务正常工作');
        };
    </script>
</body>
</html>
