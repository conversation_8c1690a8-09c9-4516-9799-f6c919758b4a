<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入水状态检测测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-controls {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-button {
            margin: 5px;
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.underwater {
            background: #2196F3;
        }
        
        .test-info {
            color: #ffffff;
            margin: 10px 0;
        }
        
        .sensor-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .sensor-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sensor-label {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .sensor-value {
            color: #ffffff;
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: white; text-align: center; margin: 20px 0;">入水状态检测测试</h1>
        
        <div class="test-controls">
            <h3 style="color: white;">模拟入水检测传感器值</h3>
            <div class="test-info">
                <p>TouchSensor D15引脚检测值：当值 < 10时认为未入水，≥ 10时认为已入水</p>
            </div>
            <button class="test-button" onclick="setWaterDetection(5)">水面状态 (值: 5)</button>
            <button class="test-button" onclick="setWaterDetection(8)">接近水面 (值: 8)</button>
            <button class="test-button underwater" onclick="setWaterDetection(15)">刚入水 (值: 15)</button>
            <button class="test-button underwater" onclick="setWaterDetection(50)">完全入水 (值: 50)</button>
            <button class="test-button underwater" onclick="setWaterDetection(100)">深度入水 (值: 100)</button>
        </div>
        
        <div class="sensor-display">
            <div class="sensor-item">
                <div class="sensor-label">入水检测值</div>
                <div class="sensor-value" id="detectionValue">0</div>
            </div>
            <div class="sensor-item">
                <div class="sensor-label">入水状态</div>
                <div class="sensor-value" id="underwaterStatus">水面</div>
            </div>
            <div class="sensor-item">
                <div class="sensor-label">当前压力</div>
                <div class="sensor-value" id="currentPressure">1013.25 hPa</div>
            </div>
            <div class="sensor-item">
                <div class="sensor-label">基准压力</div>
                <div class="sensor-value" id="surfacePressure">1013.25 hPa</div>
            </div>
            <div class="sensor-item">
                <div class="sensor-label">计算深度</div>
                <div class="sensor-value" id="calculatedDepth">0.00 m</div>
            </div>
        </div>
        
        <section class="status-panel">
            <div class="status-grid">
                <!-- 环境传感器信息 -->
                <div class="status-card environment-card">
                    <div class="status-info">
                        <h3>环境传感器 (含入水状态)</h3>
                        <div class="environment-grid">
                            <div class="env-item">
                                <span class="env-label">深度</span>
                                <span class="env-value" id="depthValue">0.00 m</span>
                                <span class="env-trend" id="depthTrend">稳定</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">水温</span>
                                <span class="env-value" id="temperatureValue">24.3°C</span>
                                <span class="env-trend" id="temperatureTrend">正常</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">压力</span>
                                <span class="env-value" id="pressureValue">1013.25 hPa</span>
                                <span class="env-trend" id="pressureTrend">正常</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">入水状态</span>
                                <span class="env-value" id="underwaterValue">水面</span>
                                <span class="env-trend" id="underwaterTrend">正常</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="test-controls">
            <h3 style="color: white;">深度计算逻辑说明</h3>
            <div class="test-info">
                <p><strong>水面状态 (检测值 < 10):</strong></p>
                <ul>
                    <li>深度强制归零</li>
                    <li>记录当前压力作为基准压力</li>
                    <li>为后续入水深度计算做准备</li>
                </ul>
                <p><strong>入水状态 (检测值 ≥ 10):</strong></p>
                <ul>
                    <li>使用基准压力计算深度</li>
                    <li>深度 = (当前压力 - 基准压力) × 0.01 米</li>
                    <li>1hPa压力差 ≈ 1cm水深</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 模拟传感器数据
        let sensorData = {
            waterDetectionValue: 0,
            isUnderwater: false,
            pressure: 1013.25,
            surfacePressure: 1013.25,
            depth: 0.0
        };

        // 设置入水检测值
        function setWaterDetection(value) {
            sensorData.waterDetectionValue = value;
            sensorData.isUnderwater = value >= 10;
            
            // 模拟压力变化
            if (sensorData.isUnderwater) {
                // 入水时增加压力（模拟深度）
                sensorData.pressure = sensorData.surfacePressure + (value - 10) * 0.5;
                sensorData.depth = (sensorData.pressure - sensorData.surfacePressure) * 0.01;
            } else {
                // 水面时重置基准压力
                sensorData.surfacePressure = sensorData.pressure;
                sensorData.depth = 0.0;
            }
            
            updateDisplay();
            console.log(`设置入水检测值: ${value}, 入水状态: ${sensorData.isUnderwater}`);
        }

        // 更新显示
        function updateDisplay() {
            // 更新传感器显示
            document.getElementById('detectionValue').textContent = sensorData.waterDetectionValue;
            document.getElementById('underwaterStatus').textContent = sensorData.isUnderwater ? '入水' : '水面';
            document.getElementById('currentPressure').textContent = sensorData.pressure.toFixed(2) + ' hPa';
            document.getElementById('surfacePressure').textContent = sensorData.surfacePressure.toFixed(2) + ' hPa';
            document.getElementById('calculatedDepth').textContent = sensorData.depth.toFixed(2) + ' m';
            
            // 更新环境传感器卡片
            document.getElementById('depthValue').textContent = sensorData.depth.toFixed(2) + ' m';
            document.getElementById('pressureValue').textContent = sensorData.pressure.toFixed(2) + ' hPa';
            document.getElementById('underwaterValue').textContent = sensorData.isUnderwater ? '入水' : '水面';
            
            // 更新趋势颜色
            const underwaterTrend = document.getElementById('underwaterTrend');
            if (sensorData.isUnderwater) {
                underwaterTrend.textContent = '入水';
                underwaterTrend.style.color = '#2196F3';
            } else {
                underwaterTrend.textContent = '水面';
                underwaterTrend.style.color = '#4CAF50';
            }
        }

        // 模拟实时数据更新
        function simulateDataUpdate() {
            const temp = (Math.random() * 5 + 22).toFixed(1);
            document.getElementById('temperatureValue').textContent = temp + '°C';
        }

        // 每3秒更新一次温度数据
        setInterval(simulateDataUpdate, 3000);
        
        // 初始化显示
        updateDisplay();
        
        console.log('入水状态检测测试页面已加载');
    </script>
</body>
</html>
