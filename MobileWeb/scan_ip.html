<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>局域网 IP 扫描工具</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet"> -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444',
                        dark: '#1F2937',
                        light: '#F3F4F6',
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .animate-pulse-slow {
                animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .animate-progress {
                animation: progress 1.5s ease-in-out infinite;
            }
            .grid-ip {
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            }
        }
    </style>
</head>

<body class="bg-gray-50 font-inter text-gray-800 min-h-screen flex flex-col">
    <header class="bg-gradient-to-r from-primary to-blue-700 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <h1 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold flex items-center">
                <i class="fa fa-wifi mr-3"></i>局域网 IP 扫描工具
            </h1>
            <p class="mt-2 text-blue-100 max-w-2xl">扫描局域网内开放 80 端口的设备，输入 IP 前缀并点击扫描按钮开始探测</p>
        </div>
    </header>

    <main class="flex-grow container mx-auto px-4 py-8">
        <!-- 控制面板 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8 transform transition-all duration-300 hover:shadow-lg">
            <div class="flex flex-col md:flex-row gap-4 items-center">
                <div class="flex-grow">
                    <label for="ipPrefix" class="block text-sm font-medium text-gray-700 mb-1">IP 前缀</label>
                    <div class="flex">
                        <input type="text" id="ipPrefix" placeholder="例如: 192.168.1"
                            class="flex-grow border border-gray-300 rounded-l-lg px-4 py-2 focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-all"
                            value="192.168.138">
                        <span
                            class="inline-flex items-center px-3 text-gray-500 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg">
                            .0-255
                        </span>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">请输入正确的 IP 前缀（如 192.168.1、10.0.0 等）</p>
                </div>

                <div class="w-full md:w-auto">
                    <label class="block text-sm font-medium text-gray-700 mb-1">扫描选项</label>
                    <div class="flex gap-2">
                        <button id="startScan"
                            class="bg-primary hover:bg-primary/90 text-white font-medium py-2 px-6 rounded-lg shadow transition-all transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <i class="fa fa-play mr-2"></i>开始扫描
                        </button>
                        <button id="stopScan"
                            class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg shadow transition-all transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-400/50"
                            disabled>
                            <i class="fa fa-stop mr-2"></i>停止
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 扫描进度 -->
        <div id="scanProgress" class="bg-white rounded-xl shadow-md p-6 mb-8 hidden">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">扫描进度</h2>
                <span id="scanStatus" class="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    <i class="fa fa-circle-o-notch fa-spin mr-1"></i>扫描中...
                </span>
            </div>

            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                <div id="progressBar" class="bg-primary h-2.5 rounded-full" style="width: 0%"></div>
            </div>

            <div class="flex justify-between text-sm text-gray-600">
                <span id="progressText">0/255</span>
                <span id="scanSpeed">速度: 0 IP/秒</span>
            </div>

            <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div class="flex items-center">
                        <i class="fa fa-check-circle text-success text-xl mr-2"></i>
                        <div>
                            <p class="text-sm text-gray-600">端口开放</p>
                            <p id="onlineCount" class="text-lg font-semibold text-success">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div class="flex items-center">
                        <i class="fa fa-times-circle text-danger text-xl mr-2"></i>
                        <div>
                            <p class="text-sm text-gray-600">端口关闭</p>
                            <p id="offlineCount" class="text-lg font-semibold text-danger">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div class="flex items-center">
                        <i class="fa fa-clock-o text-warning text-xl mr-2"></i>
                        <div>
                            <p class="text-sm text-gray-600">未响应</p>
                            <p id="timeoutCount" class="text-lg font-semibold text-warning">0</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 扫描结果 -->
        <div id="scanResults" class="bg-white rounded-xl shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">扫描结果</h2>
                <div class="flex items-center">
                    <span class="mr-2 text-sm text-gray-600">显示:</span>
                    <div class="inline-flex rounded-md shadow-sm" role="group">
                        <button type="button"
                            class="result-filter px-3 py-1 text-xs font-medium bg-primary text-white rounded-l-lg"
                            data-filter="all">全部</button>
                        <button type="button"
                            class="result-filter px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700"
                            data-filter="online">开放</button>
                        <button type="button"
                            class="result-filter px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-r-lg"
                            data-filter="offline">关闭</button>
                    </div>
                </div>
            </div>

            <div id="ipGrid" class="grid grid-ip gap-3 mb-4">
                <!-- IP 状态卡片将动态添加到这里 -->
                <div class="col-span-full text-center py-10 text-gray-500">
                    <i class="fa fa-search text-4xl mb-3 text-gray-300"></i>
                    <p>点击"开始扫描"按钮开始探测局域网设备</p>
                </div>
            </div>

            <div id="detailedResults" class="mt-6 border-t pt-6 hidden">
                <h3 class="text-md font-medium text-gray-800 mb-3">详细结果</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    IP 地址</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    端口状态</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    响应时间</th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    主机信息</th>
                            </tr>
                        </thead>
                        <tbody id="detailedResultsBody" class="bg-white divide-y divide-gray-200">
                            <!-- 详细结果将动态添加到这里 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gray-800 text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="mb-4 md:mb-0">© 2025 局域网 IP 扫描工具 | 用于网络诊断和设备发现</p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fa fa-question-circle mr-1"></i>帮助
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fa fa-info-circle mr-1"></i>关于
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 使用另一个服务
        fetch('https://ipapi.co/json/')
            .then(response => response.json())
            .then(data => {
                console.log('IP地址:', data.ip);
                console.log('国家:', data.country_name);
                console.log('城市:', data.city);
                // 还可以获取更多地理位置信息
            })
            .catch(error => console.error('获取失败:', error));

        // 扫描状态和配置
        const scanConfig = {
            timeout: 1000, // 超时时间（毫秒）
            concurrent: 80, // 并发请求数量
            progress: 0,
            online: 0,
            offline: 0,
            startTime: 0,
            isScanning: false,
            ipResults: []
        };

        // DOM 元素
        const startScanBtn = document.getElementById('startScan');
        const stopScanBtn = document.getElementById('stopScan');
        const ipPrefixInput = document.getElementById('ipPrefix');
        const scanProgress = document.getElementById('scanProgress');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const scanSpeed = document.getElementById('scanSpeed');
        const scanStatus = document.getElementById('scanStatus');
        const onlineCount = document.getElementById('onlineCount');
        const offlineCount = document.getElementById('offlineCount');
        const timeoutCount = document.getElementById('timeoutCount');
        const ipGrid = document.getElementById('ipGrid');
        const detailedResults = document.getElementById('detailedResults');
        const detailedResultsBody = document.getElementById('detailedResultsBody');
        const resultFilters = document.querySelectorAll('.result-filter');

        // 正则表达式验证 IP 前缀
        function validateIpPrefix(prefix) {
            // 检查是否是有效的 IP 前缀（例如：192.168.1 或 10.0.0）
            const regex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){0,2}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            return regex.test(prefix);
        }

        // 创建 IP 状态卡片
        function createIpCard(ip, status, responseTime, info) {
            const card = document.createElement('div');
            card.className = `ip-card rounded-lg p-3 text-center transition-all duration-300 ${status === 'online' ? 'bg-green-50 border border-green-200' : status === 'offline' ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200'}`;
            card.dataset.ip = ip;
            card.dataset.status = status;

            let iconClass = status === 'online' ? 'fa-check-circle text-success' : status === 'offline' ? 'fa-times-circle text-danger' : 'fa-clock-o text-warning';
            let statusText = status === 'online' ? '开放' : status === 'offline' ? '关闭' : '超时';

            card.innerHTML = `
                <div class="font-medium">${ip}</div>
                <div class="mt-1 text-xs">
                    <i class="fa ${iconClass}"></i> ${statusText}
                </div>
                ${responseTime ? `<div class="mt-1 text-xs text-gray-500">${responseTime}ms</div>` : ''}
                ${info ? `<div class="mt-1 text-xs text-gray-600 truncate">${info}</div>` : ''}
            `;

            return card;
        }

        // 更新详细结果表格
        function updateDetailedResults() {
            detailedResultsBody.innerHTML = '';

            scanConfig.ipResults.forEach(result => {
                const row = document.createElement('tr');
                row.className = result.status === 'online' ? 'hover:bg-green-50' : result.status === 'offline' ? 'hover:bg-red-50' : 'hover:bg-yellow-50';

                let statusClass = result.status === 'online' ? 'text-success' : result.status === 'offline' ? 'text-danger' : 'text-warning';
                let statusText = result.status === 'online' ? '开放' : result.status === 'offline' ? '关闭' : '超时';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${result.ip}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="${statusClass}">${statusText}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${result.responseTime || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${result.info || '-'}</td>
                `;

                detailedResultsBody.appendChild(row);
            });

            detailedResults.classList.remove('hidden');
        }

        // 过滤结果
        function filterResults(filter) {
            const cards = document.querySelectorAll('.ip-card');

            cards.forEach(card => {
                if (filter === 'all' || card.dataset.status === filter) {
                    card.classList.remove('hidden');
                } else {
                    card.classList.add('hidden');
                }
            });

            // 更新按钮样式
            resultFilters.forEach(btn => {
                if (btn.dataset.filter === filter) {
                    btn.classList.remove('bg-gray-100', 'text-gray-700');
                    btn.classList.add('bg-primary', 'text-white');
                } else {
                    btn.classList.remove('bg-primary', 'text-white');
                    btn.classList.add('bg-gray-100', 'text-gray-700');
                }
            });
        }

        // 开始扫描
        async function startScan() {
            const prefix = ipPrefixInput.value.trim();

            if (!validateIpPrefix(prefix)) {
                alert('请输入有效的 IP 前缀（例如：192.168.1 或 10.0.0）');
                return;
            }

            // 重置扫描状态
            scanConfig.progress = 0;
            scanConfig.online = 0;
            scanConfig.offline = 0;
            scanConfig.timeout = 1000;
            scanConfig.ipResults = [];
            scanConfig.isScanning = true;
            scanConfig.startTime = Date.now();

            // 更新 UI
            startScanBtn.disabled = true;
            stopScanBtn.disabled = false;
            scanProgress.classList.remove('hidden');
            ipGrid.innerHTML = '';

            // 显示扫描进度条
            progressBar.style.width = '0%';
            progressText.textContent = '0/255';
            scanSpeed.textContent = '速度: 0 IP/秒';
            onlineCount.textContent = '0';
            offlineCount.textContent = '0';
            timeoutCount.textContent = '0';

            // 执行扫描
            await performScan(prefix);

            // 扫描完成
            scanConfig.isScanning = false;
            startScanBtn.disabled = false;
            stopScanBtn.disabled = true;
            scanStatus.innerHTML = '<i class="fa fa-check mr-1"></i>扫描完成';

            // 显示详细结果
            updateDetailedResults();
        }

        // 停止扫描
        function stopScan() {
            scanConfig.isScanning = false;
            startScanBtn.disabled = false;
            stopScanBtn.disabled = true;
            scanStatus.innerHTML = '<i class="fa fa-pause mr-1"></i>扫描已停止';
        }

        // 执行扫描
        async function performScan(prefix) {
            // 创建 IP 列表
            const ipList = [];
            for (let i = 1; i <= 255; i++) {
                ipList.push(`${prefix}.${i}`);
            }

            // 分批扫描 IP
            let batchSize = scanConfig.concurrent;
            let batches = Math.ceil(ipList.length / batchSize);

            for (let batch = 0; batch < batches && scanConfig.isScanning; batch++) {
                const start = batch * batchSize;
                const end = Math.min(start + batchSize, ipList.length);
                const currentBatch = ipList.slice(start, end);

                // 并行处理当前批次的 IP
                await Promise.all(currentBatch.map(ip => checkPort80(ip)));

                // 更新进度
                scanConfig.progress = end;
                updateProgress();

                // 小延迟，避免 UI 卡顿
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        }

        // 检查 IP 的 80 端口是否开放
        function checkPort80(ip) {
            return new Promise(resolve => {
                const startTime = Date.now();
                let responded = false;

                // 创建 XHR 请求
                const xhr = new XMLHttpRequest();
                const url = `http://${ip}:8088`;

                // 超时处理
                const timeoutId = setTimeout(() => {
                    if (!responded) {
                        responded = true;
                        handleResponse(ip, 'timeout');
                        resolve();
                    }
                }, scanConfig.timeout);

                // 成功回调
                xhr.onload = function () {
                    if (!responded) {
                        responded = true;
                        clearTimeout(timeoutId);
                        const responseTime = Date.now() - startTime;
                        handleResponse(ip, 'online', responseTime, getServerInfo(xhr));
                        resolve();
                    }
                };

                // 错误回调
                xhr.onerror = function () {
                    if (!responded) {
                        responded = true;
                        clearTimeout(timeoutId);

                        // 检查错误类型
                        if (xhr.status === 0) {
                            // 状态码为 0 可能表示连接被拒绝（端口关闭）
                            handleResponse(ip, 'offline');
                        } else {
                            // 其他错误（如 CORS 阻止）可能表示端口开放但不允许跨域访问
                            const responseTime = Date.now() - startTime;
                            handleResponse(ip, 'online', responseTime, '可能开放（受 CORS 限制）');
                        }
                        resolve();
                    }
                };

                try {
                    // 尝试发送请求
                    xhr.open('GET', url, true);
                    xhr.timeout = scanConfig.timeout;
                    xhr.send();
                } catch (error) {
                    if (!responded) {
                        responded = true;
                        clearTimeout(timeoutId);
                        handleResponse(ip, 'offline');
                        resolve();
                    }
                }
            });
        }

        // 从响应头获取服务器信息
        function getServerInfo(xhr) {
            try {
                const server = xhr.getResponseHeader('Server');
                const contentType = xhr.getResponseHeader('Content-Type');

                if (server) {
                    return server;
                } else if (contentType) {
                    return contentType;
                }

                return 'Web 服务器';
            } catch (error) {
                return 'Web 服务器';
            }
        }

        // 处理响应
        function handleResponse(ip, status, responseTime, info) {
            // 更新统计
            if (status === 'online') {
                scanConfig.online++;

                console.log(ip, status, responseTime, info)

                window.location.href = `http://${ip}:8088`

            } else if (status === 'timeout') {
                scanConfig.timeout++;
            } else {
                scanConfig.offline++;
            }

            // 保存结果
            scanConfig.ipResults.push({
                ip,
                status,
                responseTime,
                info
            });

            // 更新 UI
            const card = createIpCard(ip, status, responseTime, info);
            ipGrid.appendChild(card);


            // 更新计数
            onlineCount.textContent = scanConfig.online;
            offlineCount.textContent = scanConfig.offline;
            timeoutCount.textContent = scanConfig.timeout;
        }

        // 更新进度
        function updateProgress() {
            const percent = (scanConfig.progress / 255) * 100;
            progressBar.style.width = `${percent}%`;
            progressText.textContent = `${scanConfig.progress}/255`;

            // 计算扫描速度
            const elapsedTime = (Date.now() - scanConfig.startTime) / 1000;
            const speed = elapsedTime > 0 ? Math.round(scanConfig.progress / elapsedTime) : 0;
            scanSpeed.textContent = `速度: ${speed} IP/秒`;
        }

        // 事件监听器
        startScanBtn.addEventListener('click', startScan);
        stopScanBtn.addEventListener('click', stopScan);

        resultFilters.forEach(btn => {
            btn.addEventListener('click', () => {
                filterResults(btn.dataset.filter);
            });
        });

        // 默认显示全部结果
        filterResults('all');
    </script>
</body>

</html>
