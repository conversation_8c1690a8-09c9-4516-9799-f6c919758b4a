<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境传感器卡片测试</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <section class="status-panel">
            <div class="status-grid">
                <!-- 环境传感器信息 -->
                <div class="status-card environment-card">
                    <div class="status-icon">🌊</div>
                    <div class="status-info">
                        <h3>环境传感器</h3>
                        <div class="environment-grid">
                            <div class="env-item">
                                <span class="env-label">深度</span>
                                <span class="env-value" id="depthValue">2.5 m</span>
                                <span class="env-trend" id="depthTrend">稳定</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">水温</span>
                                <span class="env-value" id="temperatureValue">24.3°C</span>
                                <span class="env-trend" id="temperatureTrend">正常</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">压力</span>
                                <span class="env-value" id="pressureValue">1025.6 hPa</span>
                                <span class="env-trend" id="pressureTrend">正常</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对比：原来的独立卡片样式 -->
                <div class="status-card depth-card" style="border-left: 4px solid #00d4ff;">
                    <div class="status-icon" style="color: #00d4ff;">🌊</div>
                    <div class="status-info">
                        <h3>深度（原样式）</h3>
                        <div class="status-value">2.5 m</div>
                        <div class="status-trend">稳定</div>
                    </div>
                </div>

                <div class="status-card temperature-card" style="border-left: 4px solid #ff6b35;">
                    <div class="status-icon" style="color: #ff6b35;">🌡️</div>
                    <div class="status-info">
                        <h3>水温（原样式）</h3>
                        <div class="status-value">24.3°C</div>
                        <div class="status-trend">正常</div>
                    </div>
                </div>

                <div class="status-card pressure-card" style="border-left: 4px solid #4ecdc4;">
                    <div class="status-icon" style="color: #4ecdc4;">📊</div>
                    <div class="status-info">
                        <h3>压力（原样式）</h3>
                        <div class="status-value">1025.6 hPa</div>
                        <div class="status-trend">正常</div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 测试动态更新数据
        function updateTestData() {
            const depth = (Math.random() * 5 + 1).toFixed(1);
            const temp = (Math.random() * 10 + 20).toFixed(1);
            const pressure = (Math.random() * 50 + 1000).toFixed(1);
            
            document.getElementById('depthValue').textContent = depth + ' m';
            document.getElementById('temperatureValue').textContent = temp + '°C';
            document.getElementById('pressureValue').textContent = pressure + ' hPa';
        }

        // 每2秒更新一次数据
        setInterval(updateTestData, 2000);
        
        console.log('环境传感器卡片测试页面已加载');
    </script>
</body>
</html>
