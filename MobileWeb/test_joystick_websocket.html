<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摇杆WebSocket发送测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .joystick-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .joystick {
            width: 200px;
            height: 200px;
            border: 3px solid #4CAF50;
            border-radius: 50%;
            position: relative;
            background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
            cursor: pointer;
            user-select: none;
        }
        
        .joystick-knob {
            width: 40px;
            height: 40px;
            background: #4CAF50;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.1s ease;
        }
        
        .joystick-label {
            text-align: center;
            color: white;
            margin-top: 15px;
            font-weight: bold;
        }
        
        .joystick-values {
            text-align: center;
            color: #b0b0b0;
            margin-top: 5px;
            font-family: 'Courier New', monospace;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }
        
        .status-disconnected {
            background: #f44336;
            box-shadow: 0 0 10px #f44336;
        }
        
        .data-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .data-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .data-label {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .data-value {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: bold;
            font-family: 'Courier New', monospace;
        }
        
        .demo-button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #45a049;
        }
        
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .log-entry {
            margin: 2px 0;
            word-wrap: break-word;
        }
        
        .log-info { color: #0ff; }
        .log-success { color: #0f0; }
        .log-warning { color: #ff0; }
        .log-error { color: #f00; }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">摇杆WebSocket发送测试</h1>
            
            <!-- 连接状态 -->
            <div class="demo-section">
                <h3 style="color: white;">
                    <span class="status-indicator status-disconnected" id="connectionStatus"></span>
                    OrangePiZero3连接状态
                </h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>下位机地址:</strong> orangepi3b.local:8766</p>
                    <p><strong>连接状态:</strong> <span id="connectionText">模拟测试</span></p>
                    <p><strong>发送频率:</strong> <span id="sendRate">0</span> 次/秒</p>
                    <p><strong>总发送次数:</strong> <span id="totalSent">0</span></p>
                </div>
            </div>
            
            <!-- 虚拟摇杆 -->
            <div class="demo-section">
                <h3 style="color: white;">虚拟摇杆控制</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p>拖拽摇杆来模拟Xbox手柄输入，数据将发送到OrangePiZero3下位机</p>
                </div>
                
                <div class="joystick-container">
                    <div>
                        <div class="joystick" id="leftJoystick">
                            <div class="joystick-knob" id="leftKnob"></div>
                        </div>
                        <div class="joystick-label">左摇杆 (运动控制)</div>
                        <div class="joystick-values" id="leftValues">X: 0, Y: 0</div>
                    </div>
                    
                    <div>
                        <div class="joystick" id="rightJoystick">
                            <div class="joystick-knob" id="rightKnob"></div>
                        </div>
                        <div class="joystick-label">右摇杆 (辅助控制)</div>
                        <div class="joystick-values" id="rightValues">X: 0, Y: 0</div>
                    </div>
                </div>
            </div>
            
            <!-- 数据显示 -->
            <div class="demo-section">
                <h3 style="color: white;">当前摇杆数据</h3>
                <div class="data-display">
                    <div class="data-item">
                        <div class="data-label">左摇杆 X (左右)</div>
                        <div class="data-value" id="leftXValue">0</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">左摇杆 Y (前后)</div>
                        <div class="data-value" id="leftYValue">0</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">右摇杆 X (左右)</div>
                        <div class="data-value" id="rightXValue">0</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">右摇杆 Y (前后)</div>
                        <div class="data-value" id="rightYValue">0</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能说明 -->
            <div class="demo-section">
                <h3 style="color: white;">架构说明</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>新架构设计:</strong></p>
                    <ul>
                        <li>🎮 <strong>上位机 (server.py)</strong>: 仅作为手柄数据采集工具</li>
                        <li>📡 <strong>WebSocket通信</strong>: 将手柄数据发送给下位机</li>
                        <li>🤖 <strong>下位机 (OrangePiZero3)</strong>: 接收手柄数据并控制ROV</li>
                        <li>⚡ <strong>实时传输</strong>: 低延迟的手柄数据传输</li>
                    </ul>
                    
                    <p><strong>数据格式:</strong></p>
                    <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; color: #0ff;">
{
  "type": "joystick_control",
  "data": {
    "lX": -50,    // 左摇杆X轴 [-100, 100]
    "lY": 75,     // 左摇杆Y轴 [-100, 100]
    "rX": 0,      // 右摇杆X轴 [-100, 100]
    "rY": -25     // 右摇杆Y轴 [-100, 100]
  },
  "timestamp": 1234567890.123
}</pre>
                </div>
            </div>
            
            <!-- 测试控制 -->
            <div class="demo-section">
                <h3 style="color: white;">测试控制</h3>
                <button class="demo-button" onclick="startAutoTest()">🔄 自动测试</button>
                <button class="demo-button" onclick="stopAutoTest()">⏹️ 停止测试</button>
                <button class="demo-button" onclick="resetJoysticks()">🎯 重置摇杆</button>
                <button class="demo-button" onclick="clearLog()">🗑️ 清空日志</button>
            </div>
            
            <!-- 通信日志 -->
            <div class="demo-section">
                <h3 style="color: white;">通信日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[INFO] 摇杆WebSocket发送测试页面已加载</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 摇杆数据
        let joystickData = {
            leftX: 0,
            leftY: 0,
            rightX: 0,
            rightY: 0
        };

        let sendCount = 0;
        let lastSendTime = Date.now();
        let autoTestInterval = null;

        // 摇杆控制类
        class VirtualJoystick {
            constructor(containerId, knobId, onUpdate) {
                this.container = document.getElementById(containerId);
                this.knob = document.getElementById(knobId);
                this.onUpdate = onUpdate;
                this.isDragging = false;
                this.centerX = 100;
                this.centerY = 100;
                this.maxRadius = 80;
                
                this.setupEvents();
            }
            
            setupEvents() {
                this.container.addEventListener('mousedown', this.onMouseDown.bind(this));
                document.addEventListener('mousemove', this.onMouseMove.bind(this));
                document.addEventListener('mouseup', this.onMouseUp.bind(this));
                
                // 触摸事件
                this.container.addEventListener('touchstart', this.onTouchStart.bind(this));
                document.addEventListener('touchmove', this.onTouchMove.bind(this));
                document.addEventListener('touchend', this.onTouchEnd.bind(this));
            }
            
            onMouseDown(e) {
                this.isDragging = true;
                this.updatePosition(e.clientX, e.clientY);
            }
            
            onMouseMove(e) {
                if (this.isDragging) {
                    this.updatePosition(e.clientX, e.clientY);
                }
            }
            
            onMouseUp() {
                this.isDragging = false;
                this.resetPosition();
            }
            
            onTouchStart(e) {
                e.preventDefault();
                this.isDragging = true;
                const touch = e.touches[0];
                this.updatePosition(touch.clientX, touch.clientY);
            }
            
            onTouchMove(e) {
                e.preventDefault();
                if (this.isDragging) {
                    const touch = e.touches[0];
                    this.updatePosition(touch.clientX, touch.clientY);
                }
            }
            
            onTouchEnd(e) {
                e.preventDefault();
                this.isDragging = false;
                this.resetPosition();
            }
            
            updatePosition(clientX, clientY) {
                const rect = this.container.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                let deltaX = clientX - centerX;
                let deltaY = clientY - centerY;
                
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                
                if (distance > this.maxRadius) {
                    deltaX = (deltaX / distance) * this.maxRadius;
                    deltaY = (deltaY / distance) * this.maxRadius;
                }
                
                this.knob.style.transform = `translate(${deltaX - 20}px, ${deltaY - 20}px)`;
                
                // 转换为 [-1, 1] 范围
                const normalizedX = deltaX / this.maxRadius;
                const normalizedY = -deltaY / this.maxRadius; // Y轴反转
                
                if (this.onUpdate) {
                    this.onUpdate(normalizedX, normalizedY);
                }
            }
            
            resetPosition() {
                this.knob.style.transform = 'translate(-20px, -20px)';
                if (this.onUpdate) {
                    this.onUpdate(0, 0);
                }
            }
        }

        // 初始化虚拟摇杆
        const leftJoystick = new VirtualJoystick('leftJoystick', 'leftKnob', (x, y) => {
            joystickData.leftX = x;
            joystickData.leftY = y;
            updateDisplay();
            sendJoystickData();
        });

        const rightJoystick = new VirtualJoystick('rightJoystick', 'rightKnob', (x, y) => {
            joystickData.rightX = x;
            joystickData.rightY = y;
            updateDisplay();
            sendJoystickData();
        });

        // 更新显示
        function updateDisplay() {
            const leftXInt = Math.round(joystickData.leftX * 60 * 100); // 应用yaw_rate=0.6
            const leftYInt = Math.round(joystickData.leftY * 100);
            const rightXInt = Math.round(joystickData.rightX * 100);
            const rightYInt = Math.round(joystickData.rightY * 100);
            
            document.getElementById('leftXValue').textContent = leftXInt;
            document.getElementById('leftYValue').textContent = leftYInt;
            document.getElementById('rightXValue').textContent = rightXInt;
            document.getElementById('rightYValue').textContent = rightYInt;
            
            document.getElementById('leftValues').textContent = `X: ${leftXInt}, Y: ${leftYInt}`;
            document.getElementById('rightValues').textContent = `X: ${rightXInt}, Y: ${rightYInt}`;
        }

        // 发送摇杆数据
        function sendJoystickData() {
            const yawRate = 0.6;
            
            const leftXInt = Math.max(-100, Math.min(100, Math.round(joystickData.leftX * yawRate * 100)));
            const leftYInt = Math.max(-100, Math.min(100, Math.round(joystickData.leftY * 100)));
            const rightXInt = Math.max(-100, Math.min(100, Math.round(joystickData.rightX * 100)));
            const rightYInt = Math.max(-100, Math.min(100, Math.round(joystickData.rightY * 100)));
            
            const message = {
                type: 'joystick_control',
                data: {
                    lX: leftXInt,
                    lY: leftYInt,
                    rX: rightXInt,
                    rY: rightYInt
                },
                timestamp: Date.now() / 1000
            };
            
            // 模拟发送到OrangePiZero3
            if (Math.abs(leftXInt) > 5 || Math.abs(leftYInt) > 5 || Math.abs(rightXInt) > 5 || Math.abs(rightYInt) > 5) {
                addLog(`发送摇杆数据: L(${leftXInt:+4},${leftYInt:+4}) R(${rightXInt:+4},${rightYInt:+4})`, 'success');
                sendCount++;
                updateStats();
            }
        }

        // 更新统计信息
        function updateStats() {
            const now = Date.now();
            const timeDiff = (now - lastSendTime) / 1000;
            if (timeDiff >= 1) {
                const rate = Math.round(sendCount / timeDiff);
                document.getElementById('sendRate').textContent = rate;
                lastSendTime = now;
                sendCount = 0;
            }
            
            document.getElementById('totalSent').textContent = parseInt(document.getElementById('totalSent').textContent) + 1;
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条数
            while (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // 自动测试
        function startAutoTest() {
            if (autoTestInterval) return;
            
            addLog('开始自动测试模式', 'info');
            let angle = 0;
            
            autoTestInterval = setInterval(() => {
                angle += 0.1;
                joystickData.leftX = Math.sin(angle) * 0.8;
                joystickData.leftY = Math.cos(angle) * 0.8;
                joystickData.rightX = Math.sin(angle * 1.5) * 0.6;
                joystickData.rightY = Math.cos(angle * 1.5) * 0.6;
                
                updateDisplay();
                sendJoystickData();
                
                // 更新摇杆视觉位置
                const leftDeltaX = joystickData.leftX * 80;
                const leftDeltaY = -joystickData.leftY * 80;
                const rightDeltaX = joystickData.rightX * 80;
                const rightDeltaY = -joystickData.rightY * 80;
                
                document.getElementById('leftKnob').style.transform = `translate(${leftDeltaX - 20}px, ${leftDeltaY - 20}px)`;
                document.getElementById('rightKnob').style.transform = `translate(${rightDeltaX - 20}px, ${rightDeltaY - 20}px)`;
            }, 50);
        }

        function stopAutoTest() {
            if (autoTestInterval) {
                clearInterval(autoTestInterval);
                autoTestInterval = null;
                addLog('停止自动测试模式', 'warning');
                resetJoysticks();
            }
        }

        function resetJoysticks() {
            joystickData = { leftX: 0, leftY: 0, rightX: 0, rightY: 0 };
            leftJoystick.resetPosition();
            rightJoystick.resetPosition();
            updateDisplay();
            addLog('摇杆已重置', 'info');
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 模拟连接状态
        function simulateConnectionStatus() {
            const statusIndicator = document.getElementById('connectionStatus');
            const connectionText = document.getElementById('connectionText');
            
            let connected = false;
            setInterval(() => {
                connected = !connected;
                if (connected) {
                    statusIndicator.className = 'status-indicator status-connected';
                    connectionText.textContent = '已连接 (模拟)';
                } else {
                    statusIndicator.className = 'status-indicator status-disconnected';
                    connectionText.textContent = '连接中... (模拟)';
                }
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateDisplay();
            simulateConnectionStatus();
            addLog('摇杆WebSocket发送测试页面已初始化', 'success');
            addLog('拖拽摇杆来模拟手柄输入', 'info');
        });
    </script>
</body>
</html>
