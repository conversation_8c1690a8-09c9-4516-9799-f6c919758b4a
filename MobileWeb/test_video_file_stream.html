<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频文件流功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-button {
            margin: 5px;
            padding: 12px 24px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .demo-button.video-mode {
            background: #2196F3;
        }
        
        .demo-button.video-mode:hover {
            background: #1976D2;
        }
        
        .demo-button.camera-mode {
            background: #FF9800;
        }
        
        .demo-button.camera-mode:hover {
            background: #F57C00;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.active {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }
        
        .status-indicator.inactive {
            background: #666;
        }
        
        .video-preview {
            width: 100%;
            max-width: 640px;
            height: 360px;
            background: #000;
            border-radius: 8px;
            margin: 20px auto;
            display: block;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-label {
            color: #4CAF50;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #ffffff;
            font-family: 'Courier New', monospace;
        }
        
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .log-entry {
            margin: 2px 0;
            word-wrap: break-word;
        }
        
        .log-info { color: #0ff; }
        .log-success { color: #0f0; }
        .log-warning { color: #ff0; }
        .log-error { color: #f00; }
        
        .file-input-group {
            margin: 15px 0;
        }
        
        .file-input {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            color: white;
            font-family: 'Courier New', monospace;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">视频文件流功能测试</h1>
            
            <!-- 功能说明 -->
            <div class="demo-section">
                <h3 style="color: white;">视频文件流功能说明</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>功能特点:</strong></p>
                    <ul class="feature-list">
                        <li>使用本地MP4视频文件作为MJPEG流源</li>
                        <li>支持视频文件循环播放</li>
                        <li>自动调整视频分辨率到摄像头设置</li>
                        <li>应用图像处理效果（对比度、饱和度、锐化）</li>
                        <li>添加视频信息覆盖层显示</li>
                        <li>支持实时切换摄像头和视频文件模式</li>
                        <li>错误处理和自动回退机制</li>
                    </ul>
                    
                    <p><strong>应用场景:</strong></p>
                    <ul class="feature-list">
                        <li>ROV系统演示和测试</li>
                        <li>离线视频回放和分析</li>
                        <li>系统功能展示</li>
                        <li>培训和教学用途</li>
                    </ul>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="demo-section">
                <h3 style="color: white;">模式控制</h3>
                <div class="control-panel">
                    <div class="control-group">
                        <h4 style="color: white; margin-bottom: 15px;">当前状态</h4>
                        <div style="margin: 10px 0;">
                            <span class="status-indicator active" id="modeIndicator"></span>
                            <span style="color: white;" id="modeStatus">摄像头模式</span>
                        </div>
                        <div style="color: #b0b0b0; font-size: 0.9rem;" id="modeDetails">
                            使用实时摄像头作为视频源
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <h4 style="color: white; margin-bottom: 15px;">视频文件设置</h4>
                        <div class="file-input-group">
                            <label style="color: white; display: block; margin-bottom: 5px;">视频文件路径:</label>
                            <input type="text" class="file-input" id="videoPath" 
                                   value="rovRemote/video/test.mp4" 
                                   placeholder="输入视频文件路径">
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 20px 0;">
                    <button class="demo-button video-mode" onclick="switchToVideoMode()">
                        🎬 切换到视频文件模式
                    </button>
                    <button class="demo-button camera-mode" onclick="switchToCameraMode()">
                        📹 切换到摄像头模式
                    </button>
                    <button class="demo-button" onclick="getVideoModeStatus()">
                        📊 获取状态
                    </button>
                    <button class="demo-button" onclick="clearLog()">
                        🗑️ 清空日志
                    </button>
                </div>
            </div>
            
            <!-- 视频预览 -->
            <div class="demo-section">
                <h3 style="color: white;">视频流预览</h3>
                <img src="/video_feed" class="video-preview" alt="ROV视频流" id="videoPreview">
                <div style="text-align: center; color: #b0b0b0; margin-top: 10px;">
                    实时视频流 - 支持摄像头和视频文件模式
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="demo-section">
                <h3 style="color: white;">系统信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">视频模式</div>
                        <div class="info-value" id="infoVideoMode">摄像头模式</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">视频文件</div>
                        <div class="info-value" id="infoVideoFile">无</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">分辨率</div>
                        <div class="info-value" id="infoResolution">1280x720</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">帧率</div>
                        <div class="info-value" id="infoFrameRate">30 FPS</div>
                    </div>
                </div>
            </div>
            
            <!-- 技术实现 -->
            <div class="demo-section">
                <h3 style="color: white;">技术实现详解</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <h4 style="color: #4CAF50;">核心函数:</h4>
                    <pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; color: #0ff; overflow-x: auto;">
def generate_video_file_frames(self, video_path=None):
    """使用本地视频文件生成MJPEG流"""
    # 1. 检查视频文件是否存在
    # 2. 打开视频文件 cv2.VideoCapture(video_path)
    # 3. 循环读取视频帧
    # 4. 调整分辨率和应用图像处理
    # 5. 添加信息覆盖层
    # 6. 编码为JPEG并yield
                    </pre>
                    
                    <h4 style="color: #4CAF50;">模式切换:</h4>
                    <pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; color: #0ff; overflow-x: auto;">
def generate_frames(self):
    """生成视频流 - 支持摄像头和视频文件模式"""
    if self.video_mode:
        yield from self.generate_video_file_frames(self.video_file_path)
    else:
        # 使用摄像头模式...
                    </pre>
                    
                    <h4 style="color: #4CAF50;">WebSocket命令:</h4>
                    <ul class="feature-list">
                        <li><code>switch_to_video_file</code>: 切换到视频文件模式</li>
                        <li><code>switch_to_camera</code>: 切换回摄像头模式</li>
                        <li><code>get_video_mode_status</code>: 获取当前模式状态</li>
                    </ul>
                </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="demo-section">
                <h3 style="color: white;">操作日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[INFO] 视频文件流功能测试页面已加载</div>
                    <div class="log-entry log-success">[SUCCESS] 当前模式: 摄像头模式</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentMode = 'camera';
        let websocket = null;

        // 初始化WebSocket连接
        function initWebSocket() {
            try {
                websocket = new WebSocket('ws://localhost:8765');
                
                websocket.onopen = function(event) {
                    addLog('WebSocket连接已建立', 'success');
                    getVideoModeStatus();
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (e) {
                        addLog(`WebSocket消息解析错误: ${e}`, 'error');
                    }
                };
                
                websocket.onclose = function(event) {
                    addLog('WebSocket连接已关闭', 'warning');
                };
                
                websocket.onerror = function(error) {
                    addLog(`WebSocket错误: ${error}`, 'error');
                };
                
            } catch (e) {
                addLog(`WebSocket初始化失败: ${e}`, 'error');
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            if (data.type === 'video_mode_status') {
                updateModeStatus(data.video_mode, data.video_file_path);
            } else if (data.type === 'response') {
                addLog(`命令响应: ${data.command} - ${data.message}`, 
                       data.success ? 'success' : 'error');
                if (data.success) {
                    getVideoModeStatus(); // 更新状态
                }
            }
        }

        // 发送WebSocket命令
        function sendCommand(command, data = {}) {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'command',
                    command: command,
                    ...data,
                    timestamp: Date.now()
                };
                websocket.send(JSON.stringify(message));
                addLog(`发送命令: ${command}`, 'info');
            } else {
                addLog('WebSocket未连接', 'error');
            }
        }

        // 切换到视频文件模式
        function switchToVideoMode() {
            const videoPath = document.getElementById('videoPath').value;
            const data = videoPath ? { video_path: videoPath } : {};
            sendCommand('switch_to_video_file', data);
        }

        // 切换到摄像头模式
        function switchToCameraMode() {
            sendCommand('switch_to_camera');
        }

        // 获取视频模式状态
        function getVideoModeStatus() {
            sendCommand('get_video_mode_status');
        }

        // 更新模式状态显示
        function updateModeStatus(videoMode, videoFilePath) {
            const indicator = document.getElementById('modeIndicator');
            const status = document.getElementById('modeStatus');
            const details = document.getElementById('modeDetails');
            
            const infoVideoMode = document.getElementById('infoVideoMode');
            const infoVideoFile = document.getElementById('infoVideoFile');
            
            if (videoMode) {
                currentMode = 'video';
                indicator.className = 'status-indicator active';
                status.textContent = '视频文件模式';
                details.textContent = `播放视频文件: ${videoFilePath || '默认文件'}`;
                infoVideoMode.textContent = '视频文件模式';
                infoVideoFile.textContent = videoFilePath || 'rovRemote/video/test.mp4';
            } else {
                currentMode = 'camera';
                indicator.className = 'status-indicator active';
                status.textContent = '摄像头模式';
                details.textContent = '使用实时摄像头作为视频源';
                infoVideoMode.textContent = '摄像头模式';
                infoVideoFile.textContent = '无';
            }
            
            addLog(`模式状态更新: ${videoMode ? '视频文件' : '摄像头'}模式`, 'success');
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条数
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('视频文件流功能测试页面已初始化', 'success');
            initWebSocket();
            
            // 定期检查视频流状态
            setInterval(() => {
                const videoPreview = document.getElementById('videoPreview');
                if (videoPreview.complete && videoPreview.naturalWidth > 0) {
                    // 视频流正常
                } else {
                    // 可以添加视频流状态检查
                }
            }, 5000);
        });
    </script>
</body>
</html>
