#!/usr/bin/env python3
"""
ROV通信协议测试脚本
用于测试与ESP32下位机的通信
"""

import serial
import struct
import time
import sys

class ROVProtocol:
    def __init__(self, port, baudrate=115200):
        """初始化ROV通信协议"""
        try:
            self.ser = serial.Serial(port, baudrate, timeout=1)
            print(f"✅ 串口 {port} 连接成功，波特率 {baudrate}")
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            sys.exit(1)
    
    def calculate_crc8(self, data):
        """计算CRC8校验码"""
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc
    
    def send_command(self, cmd, data=b''):
        """发送命令到ESP32"""
        try:
            # 构建命令帧
            frame = bytearray([0xAA, cmd, len(data)])
            frame.extend(data)
            crc = self.calculate_crc8(frame[1:])
            frame.append(crc)
            
            print(f"📤 发送命令: {frame.hex().upper()}")
            self.ser.write(frame)
            
            return self.read_response()
        except Exception as e:
            print(f"❌ 发送命令失败: {e}")
            return None
    
    def read_response(self):
        """读取ESP32响应"""
        try:
            # 读取帧头
            header = self.ser.read(1)
            if not header or header[0] != 0x55:
                print("❌ 无效的响应帧头")
                return None
                
            # 读取状态码和数据长度
            status_len = self.ser.read(2)
            if len(status_len) != 2:
                print("❌ 响应数据不完整")
                return None
                
            status = status_len[0]
            data_len = status_len[1]
            
            # 读取数据和校验码
            remaining = self.ser.read(data_len + 1)
            if len(remaining) != data_len + 1:
                print("❌ 响应数据长度不匹配")
                return None
                
            data = remaining[:-1]
            received_crc = remaining[-1]
            
            # 验证CRC
            frame_data = bytes([status, data_len]) + data
            calculated_crc = self.calculate_crc8(frame_data)
            
            if received_crc != calculated_crc:
                print(f"❌ CRC校验失败: 期望 {calculated_crc:02X}, 收到 {received_crc:02X}")
                return None
            
            response_hex = (header + status_len + remaining).hex().upper()
            print(f"📥 收到响应: {response_hex}")
            
            return {'status': status, 'data': data}
            
        except Exception as e:
            print(f"❌ 读取响应失败: {e}")
            return None
    
    def get_pressure_data(self):
        """获取气压计数据"""
        print("\n🌊 请求气压计数据...")
        response = self.send_command(0x02)
        
        if response and response['status'] == 0x00:
            data = response['data']
            if len(data) == 12:
                temp = struct.unpack('<f', data[0:4])[0]
                pressure = struct.unpack('<f', data[4:8])[0]
                depth = struct.unpack('<f', data[8:12])[0]
                
                print(f"✅ 气压计数据:")
                print(f"   温度: {temp:.2f}°C")
                print(f"   压力: {pressure:.2f} hPa")
                print(f"   深度: {depth:.2f} m")
                
                return {'temperature': temp, 'pressure': pressure, 'depth': depth}
            else:
                print(f"❌ 数据长度错误: 期望12字节, 收到{len(data)}字节")
        else:
            status = response['status'] if response else 'None'
            print(f"❌ 命令执行失败，状态码: {status}")
        
        return None
    
    def get_all_sensor_data(self):
        """获取所有传感器数据"""
        print("\n📊 请求所有传感器数据...")
        response = self.send_command(0x01)
        
        if response and response['status'] == 0x00:
            data = response['data']
            if len(data) == 24:
                temp = struct.unpack('<f', data[0:4])[0]
                pressure = struct.unpack('<f', data[4:8])[0]
                depth = struct.unpack('<f', data[8:12])[0]
                angle_x = struct.unpack('<f', data[12:16])[0]
                angle_y = struct.unpack('<f', data[16:20])[0]
                angle_z = struct.unpack('<f', data[20:24])[0]
                
                print(f"✅ 传感器数据:")
                print(f"   温度: {temp:.2f}°C")
                print(f"   压力: {pressure:.2f} hPa")
                print(f"   深度: {depth:.2f} m")
                print(f"   姿态角X: {angle_x:.2f}°")
                print(f"   姿态角Y: {angle_y:.2f}°")
                print(f"   姿态角Z: {angle_z:.2f}°")
                
                return {
                    'temperature': temp, 'pressure': pressure, 'depth': depth,
                    'angle_x': angle_x, 'angle_y': angle_y, 'angle_z': angle_z
                }
            else:
                print(f"❌ 数据长度错误: 期望24字节, 收到{len(data)}字节")
        else:
            status = response['status'] if response else 'None'
            print(f"❌ 命令执行失败，状态码: {status}")
        
        return None
    
    def set_led(self, r, g, b, brightness):
        """设置LED颜色和亮度"""
        print(f"\n💡 设置LED: R={r}, G={g}, B={b}, 亮度={brightness}")
        led_data = bytes([r, g, b, brightness])
        response = self.send_command(0x11, led_data)
        
        if response and response['status'] == 0x00:
            print("✅ LED设置成功")
            return True
        else:
            status = response['status'] if response else 'None'
            print(f"❌ LED设置失败，状态码: {status}")
            return False
    
    def set_pwm(self, channel, value):
        """设置PWM输出"""
        print(f"\n⚡ 设置PWM: 通道{channel}, 值{value}")
        pwm_data = bytes([channel, value])
        response = self.send_command(0x10, pwm_data)
        
        if response and response['status'] == 0x00:
            print("✅ PWM设置成功")
            return True
        else:
            status = response['status'] if response else 'None'
            print(f"❌ PWM设置失败，状态码: {status}")
            return False
    
    def ping(self):
        """发送心跳包"""
        print("\n💓 发送心跳包...")
        response = self.send_command(0xFF)
        
        if response and response['status'] == 0x00:
            data = response['data']
            if len(data) == 4:
                timestamp = struct.unpack('<I', data)[0]
                print(f"✅ 心跳响应: 系统运行时间 {timestamp} ms")
                return timestamp
            else:
                print(f"❌ 心跳数据长度错误: 期望4字节, 收到{len(data)}字节")
        else:
            status = response['status'] if response else 'None'
            print(f"❌ 心跳失败，状态码: {status}")
        
        return None
    
    def close(self):
        """关闭串口连接"""
        if self.ser:
            self.ser.close()
            print("🔌 串口连接已关闭")

def main():
    """主测试函数"""
    print("🤖 ROV通信协议测试")
    print("=" * 50)
    
    # 串口配置
    port = '/dev/ttyACM0'  # 根据实际情况修改
    if len(sys.argv) > 1:
        port = sys.argv[1]
    
    print(f"🔌 连接串口: {port}")
    
    try:
        # 创建协议实例
        rov = ROVProtocol(port)
        
        # 测试序列
        tests = [
            ("心跳测试", lambda: rov.ping()),
            ("气压计数据", lambda: rov.get_pressure_data()),
            ("所有传感器数据", lambda: rov.get_all_sensor_data()),
            ("LED测试(红色)", lambda: rov.set_led(255, 0, 0, 128)),
            ("LED测试(绿色)", lambda: rov.set_led(0, 255, 0, 128)),
            ("LED测试(蓝色)", lambda: rov.set_led(0, 0, 255, 128)),
            ("LED测试(白色)", lambda: rov.set_led(255, 255, 255, 100)),
            ("PWM测试(通道0)", lambda: rov.set_pwm(0, 90)),
            ("PWM测试(通道1)", lambda: rov.set_pwm(1, 45)),
        ]
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                result = test_func()
                if result is not None:
                    print(f"✅ {test_name} 完成")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
            
            time.sleep(1)  # 测试间隔
        
        print(f"\n{'='*50}")
        print("🎉 测试完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        try:
            rov.close()
        except:
            pass

if __name__ == "__main__":
    main()
