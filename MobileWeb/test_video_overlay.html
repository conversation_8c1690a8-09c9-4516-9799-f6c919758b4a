<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频传感器覆盖层测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-video {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            position: relative;
        }
        
        .demo-controls {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #45a049;
        }
        
        .demo-info {
            color: #ffffff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: white; text-align: center; margin: 20px 0;">视频传感器覆盖层测试</h1>
        
        <div class="demo-controls">
            <h3 style="color: white;">模拟传感器数据</h3>
            <div class="demo-info">
                <p>点击下面的按钮来模拟不同的传感器数据状态：</p>
            </div>
            <button class="demo-button" onclick="simulateNormalData()">正常数据</button>
            <button class="demo-button" onclick="simulateUnderwaterData()">入水状态</button>
            <button class="demo-button" onclick="simulateLowBatteryData()">低电量警告</button>
            <button class="demo-button" onclick="simulateHighLatencyData()">高延迟状态</button>
            <button class="demo-button" onclick="simulateRandomData()">随机数据</button>
        </div>
        
        <!-- 视频容器 -->
        <section class="video-section">
            <div class="video-container">
                <div class="demo-video">
                    🎥 模拟视频流
                    <br>
                    <small style="font-size: 1rem; opacity: 0.7;">传感器数据将覆盖在此视频上</small>
                </div>
                
                <!-- 传感器数据覆盖层 -->
                <div class="sensor-overlay" id="sensorOverlay">
                    <div class="sensor-overlay-grid">
                        <!-- 左上角 - 深度和温度 -->
                        <div class="sensor-group top-left">
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">🌊</span>
                                <span class="sensor-label">深度</span>
                                <span class="sensor-value-overlay" id="overlayDepth">0.0m</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">🌡️</span>
                                <span class="sensor-label">水温</span>
                                <span class="sensor-value-overlay" id="overlayTemperature">--°C</span>
                            </div>
                        </div>
                        
                        <!-- 右上角 - 压力和入水状态 -->
                        <div class="sensor-group top-right">
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">📊</span>
                                <span class="sensor-label">压力</span>
                                <span class="sensor-value-overlay" id="overlayPressure">--hPa</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">💧</span>
                                <span class="sensor-label">状态</span>
                                <span class="sensor-value-overlay" id="overlayUnderwaterStatus">水面</span>
                            </div>
                        </div>
                        
                        <!-- 左下角 - 电池和罗盘 -->
                        <div class="sensor-group bottom-left">
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">🔋</span>
                                <span class="sensor-label">电池</span>
                                <span class="sensor-value-overlay" id="overlayBattery">--V</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">🧭</span>
                                <span class="sensor-label">航向</span>
                                <span class="sensor-value-overlay" id="overlayHeading">--°</span>
                            </div>
                        </div>
                        
                        <!-- 右下角 - 姿态信息 -->
                        <div class="sensor-group bottom-right">
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">📐</span>
                                <span class="sensor-label">姿态</span>
                                <span class="sensor-value-overlay" id="overlayAttitude">--°</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">📡</span>
                                <span class="sensor-label">延迟</span>
                                <span class="sensor-value-overlay" id="overlayLatency">--ms</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 覆盖层控制按钮 -->
                    <div class="overlay-controls">
                        <button id="toggleOverlay" class="overlay-toggle-btn" title="显示/隐藏传感器数据">📊</button>
                        <button id="toggleOverlayOpacity" class="overlay-toggle-btn" title="调整透明度">👁️</button>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="demo-controls">
            <h3 style="color: white;">覆盖层功能说明</h3>
            <div class="demo-info">
                <p><strong>📊 显示/隐藏按钮:</strong> 点击可以显示或隐藏整个传感器覆盖层</p>
                <p><strong>👁️ 透明度按钮:</strong> 点击可以在三种透明度之间切换（正常、半透明、很透明）</p>
                <p><strong>响应式设计:</strong> 覆盖层会根据屏幕大小自动调整布局和字体大小</p>
                <p><strong>数据更新:</strong> 传感器数据会实时更新，颜色会根据数值状态变化</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟传感器数据
        function updateOverlayData(data) {
            if (data.depth !== undefined) {
                document.getElementById('overlayDepth').textContent = `${data.depth.toFixed(1)}m`;
            }
            if (data.temperature !== undefined) {
                document.getElementById('overlayTemperature').textContent = `${data.temperature.toFixed(1)}°C`;
            }
            if (data.pressure !== undefined) {
                document.getElementById('overlayPressure').textContent = `${data.pressure.toFixed(1)}hPa`;
            }
            if (data.isUnderwater !== undefined) {
                const element = document.getElementById('overlayUnderwaterStatus');
                element.textContent = data.isUnderwater ? '入水' : '水面';
                element.style.color = data.isUnderwater ? '#2196F3' : '#4CAF50';
            }
            if (data.battery !== undefined) {
                const element = document.getElementById('overlayBattery');
                element.textContent = `${data.battery.toFixed(1)}V`;
                if (data.battery < 7.0) {
                    element.style.color = '#ff6b6b';
                } else if (data.battery < 7.4) {
                    element.style.color = '#ffa726';
                } else {
                    element.style.color = '#4CAF50';
                }
            }
            if (data.heading !== undefined) {
                document.getElementById('overlayHeading').textContent = `${data.heading}°`;
            }
            if (data.attitude !== undefined) {
                document.getElementById('overlayAttitude').textContent = `${data.attitude.toFixed(1)}°`;
            }
            if (data.latency !== undefined) {
                const element = document.getElementById('overlayLatency');
                element.textContent = `${data.latency}ms`;
                if (data.latency < 20) {
                    element.style.color = '#4CAF50';
                } else if (data.latency < 100) {
                    element.style.color = '#ffa726';
                } else {
                    element.style.color = '#ff6b6b';
                }
            }
        }

        // 模拟不同状态的数据
        function simulateNormalData() {
            updateOverlayData({
                depth: 2.5,
                temperature: 24.3,
                pressure: 1025.6,
                isUnderwater: true,
                battery: 8.2,
                heading: 135,
                attitude: -2.1,
                latency: 15
            });
        }

        function simulateUnderwaterData() {
            updateOverlayData({
                depth: 15.8,
                temperature: 18.7,
                pressure: 2580.3,
                isUnderwater: true,
                battery: 7.8,
                heading: 270,
                attitude: 5.3,
                latency: 25
            });
        }

        function simulateLowBatteryData() {
            updateOverlayData({
                depth: 5.2,
                temperature: 22.1,
                pressure: 1520.4,
                isUnderwater: true,
                battery: 6.8,
                heading: 45,
                attitude: -1.2,
                latency: 35
            });
        }

        function simulateHighLatencyData() {
            updateOverlayData({
                depth: 8.1,
                temperature: 20.5,
                pressure: 1810.7,
                isUnderwater: true,
                battery: 7.5,
                heading: 180,
                attitude: 3.7,
                latency: 150
            });
        }

        function simulateRandomData() {
            updateOverlayData({
                depth: Math.random() * 20,
                temperature: Math.random() * 15 + 15,
                pressure: Math.random() * 1000 + 1000,
                isUnderwater: Math.random() > 0.3,
                battery: Math.random() * 3 + 6,
                heading: Math.floor(Math.random() * 360),
                attitude: (Math.random() - 0.5) * 20,
                latency: Math.floor(Math.random() * 200)
            });
        }

        // 初始化覆盖层控制
        function initOverlayControls() {
            const toggleOverlayBtn = document.getElementById('toggleOverlay');
            const toggleOpacityBtn = document.getElementById('toggleOverlayOpacity');
            const sensorOverlay = document.getElementById('sensorOverlay');

            if (toggleOverlayBtn && sensorOverlay) {
                toggleOverlayBtn.addEventListener('click', () => {
                    sensorOverlay.classList.toggle('hidden');
                    const isHidden = sensorOverlay.classList.contains('hidden');
                    toggleOverlayBtn.textContent = isHidden ? '📊' : '🚫';
                });
            }

            if (toggleOpacityBtn && sensorOverlay) {
                let opacityLevel = 0;
                toggleOpacityBtn.addEventListener('click', () => {
                    opacityLevel = (opacityLevel + 1) % 3;
                    sensorOverlay.classList.remove('semi-transparent');
                    sensorOverlay.style.opacity = '';
                    
                    switch (opacityLevel) {
                        case 0:
                            sensorOverlay.style.opacity = '0.9';
                            toggleOpacityBtn.textContent = '👁️';
                            break;
                        case 1:
                            sensorOverlay.classList.add('semi-transparent');
                            toggleOpacityBtn.textContent = '👁️‍🗨️';
                            break;
                        case 2:
                            sensorOverlay.style.opacity = '0.3';
                            toggleOpacityBtn.textContent = '👻';
                            break;
                    }
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initOverlayControls();
            simulateNormalData();
            console.log('视频传感器覆盖层测试页面已加载');
        });
    </script>
</body>
</html>
