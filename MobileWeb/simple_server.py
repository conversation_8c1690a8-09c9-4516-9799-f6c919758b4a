#!/usr/bin/env python3
"""
简单的HTTP服务器，用于测试传感器覆盖层功能
"""

from flask import Flask, send_from_directory
import os

app = Flask(__name__)

@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def serve_file(filename):
    return send_from_directory('.', filename)

if __name__ == '__main__':
    print("启动简单HTTP服务器在端口8089")
    print("访问地址: http://localhost:8089")
    print("测试覆盖层: http://localhost:8089/test_video_overlay.html")
    app.run(host='0.0.0.0', port=8089, debug=False)
