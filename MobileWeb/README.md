# ROV Web控制系统

这是一个基于Web的ROV（水下机器人）远程控制系统，支持实时视频流显示、传感器数据监控和远程控制功能。

## 🌟 主要功能

### 📺 视频流显示
- **摄像头直连**: 直接从USB/CSI摄像头获取实时视频流
- **MJPEG流**: 使用OpenCV进行高效的MJPEG编码
- **多源适配**: 自动尝试多个视频源地址
- **全屏播放**: 支持全屏观看视频
- **录制功能**: 支持开始/停止视频录制
- **质量调节**: 支持低/中/高三档画质设置
- **帧率控制**: 可调节15/30/60FPS帧率

### 📊 状态信息显示
- **深度监测**: 实时显示ROV当前深度（米）
- **ROV航向**: 显示ROV当前航向角度（0-360°）
- **手机航向**: 显示控制设备的航向角度
- **系统状态**: 连接状态、延迟等信息

### 🎮 远程控制
- **灯光控制**: 开关灯光、调节亮度
- **相机控制**: 上下左右移动、居中复位
- **系统设置**: 深度校准、航向校准等
- **视频设置**: 质量调节、帧率设置

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- 现代浏览器（支持WebSocket和设备方向API）
- ROV硬件系统（ESP32 + 传感器）
- USB摄像头或CSI摄像头（可选）

### 2. 安装依赖
```bash
pip3 install websockets flask pyserial requests opencv-python numpy
```

### 3. 测试摄像头
```bash
# 测试摄像头和OpenCV是否正常工作
python3 test_camera.py
```

### 4. 启动系统
```bash
# 方法1: 使用启动脚本
./start.sh

# 方法2: 直接运行
python3 server.py
```

### 5. 访问界面
打开浏览器访问: `http://localhost:8088`

## 📱 移动端使用

### 设备方向权限
在iOS设备上首次使用时，需要授权设备方向访问权限以获取手机航向数据。

### 触摸控制
- 所有控制按钮都针对触摸操作进行了优化
- 支持手势操作和触摸反馈
- 响应式设计，适配各种屏幕尺寸

## 🔧 配置说明

### 视频流配置
在 `script.js` 中修改视频源地址：
```javascript
const videoSources = [
    'http://localhost:5000/video_feed',    // 本地视频流
    '/video_feed',                         // 相对路径
    'http://*************:5000/video_feed' // ROV设备IP
];
```

### 串口配置
在 `server.py` 中修改串口设置：
```python
self.serial_port = SerialPort(port='/dev/ttyACM0', baudrate=115200)
```

### WebSocket端口
默认WebSocket端口为8765，Web服务端口为8088，可在代码中修改。

## 📡 通信协议

### WebSocket消息格式
```json
{
    "type": "command",
    "command": "light_toggle",
    "data": {},
    "timestamp": 1234567890
}
```

### 串口命令映射
- `01`: 灯光开关
- `02XX`: 灯光亮度设置（XX为十六进制亮度值）
- `03-07`: 相机控制（上下左右居中）
- `08`: 航向校准

### 传感器数据格式
```json
{
    "type": "sensor_data",
    "data": {
        "depth": 2.5,
        "rovHeading": 180,
        "phoneHeading": 90,
        "temperature": 25.0,
        "pressure": 1013.25
    },
    "timestamp": 1234567890
}
```

## 🛠️ 开发说明

### 文件结构
```
MobileWeb/
├── index.html      # 主页面
├── style.css       # 样式文件
├── script.js       # 前端逻辑
├── server.py       # 后端服务器（集成摄像头功能）
├── start.sh        # 启动脚本
├── test.html       # 功能测试页面
├── test_camera.py  # 摄像头测试脚本
└── README.md       # 说明文档
```

### 自定义开发
1. **添加新控制**: 在 `script.js` 中添加事件监听器，在 `server.py` 中添加命令处理
2. **修改UI**: 编辑 `index.html` 和 `style.css`
3. **扩展传感器**: 在 `server.py` 中修改数据解析逻辑

## 🔍 故障排除

### 常见问题

**1. 视频流无法显示**
- 检查视频流服务是否启动
- 确认视频源地址是否正确
- 查看浏览器控制台错误信息

**2. WebSocket连接失败**
- 确认服务器已启动
- 检查防火墙设置
- 验证端口是否被占用

**3. 串口连接失败**
- 检查设备连接
- 确认串口权限
- 验证波特率设置

**4. 手机航向不工作**
- 确认设备支持方向传感器
- 检查浏览器权限设置
- 在HTTPS环境下使用

**5. 摄像头无法工作**
- 运行 `python3 test_camera.py` 检测摄像头
- 确认摄像头设备连接（通常是/dev/video0）
- 检查摄像头权限：`sudo usermod -a -G video $USER`
- 确认OpenCV安装：`pip3 install opencv-python`
- 尝试不同的摄像头设备索引（0, 1, 2...）

### 调试模式
启动时添加调试参数：
```bash
python3 server.py --debug
```

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如有问题，请在GitHub上创建Issue或联系开发者。
