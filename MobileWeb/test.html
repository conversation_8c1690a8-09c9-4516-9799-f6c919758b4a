<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROV系统测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 10px 0;
            border-radius: 10px;
        }
        .test-button {
            background: #00d4ff;
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0099cc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.info { background: #2196F3; }
        #log {
            background: #000;
            color: #0f0;
            padding: 10px;
            height: 200px;
            overflow-y: scroll;
            font-family: monospace;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 ROV系统测试页面</h1>
    
    <div class="test-section">
        <h2>WebSocket连接测试</h2>
        <button class="test-button" onclick="testWebSocket()">测试WebSocket连接</button>
        <button class="test-button" onclick="sendTestCommand()">发送测试命令</button>
        <div id="wsStatus" class="status info">未连接</div>
    </div>

    <div class="test-section">
        <h2>设备方向测试</h2>
        <button class="test-button" onclick="testDeviceOrientation()">测试设备方向</button>
        <div id="orientationData">
            <p>Alpha (航向): <span id="alpha">--</span>°</p>
            <p>Beta (俯仰): <span id="beta">--</span>°</p>
            <p>Gamma (翻滚): <span id="gamma">--</span>°</p>
        </div>
    </div>

    <div class="test-section">
        <h2>视频流测试</h2>
        <button class="test-button" onclick="testVideoStream()">测试视频流</button>
        <div id="videoStatus" class="status info">未测试</div>
        <video id="testVideo" width="320" height="240" controls style="display:none;">
            <source src="http://localhost:8088/video_feed" type="video/mp4">
        </video>
    </div>

    <div class="test-section">
        <h2>模拟传感器数据</h2>
        <button class="test-button" onclick="startSimulation()">开始模拟</button>
        <button class="test-button" onclick="stopSimulation()">停止模拟</button>
        <div id="sensorData">
            <p>深度: <span id="simDepth">0.0</span> m</p>
            <p>航向: <span id="simHeading">0</span>°</p>
            <p>温度: <span id="simTemp">25.0</span>°C</p>
        </div>
    </div>

    <div class="test-section">
        <h2>系统日志</h2>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        let ws = null;
        let simulationInterval = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testWebSocket() {
            const wsUrl = `ws://${window.location.hostname}:8765`;
            log(`尝试连接WebSocket: ${wsUrl}`);
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    log('✅ WebSocket连接成功');
                    document.getElementById('wsStatus').textContent = '已连接';
                    document.getElementById('wsStatus').className = 'status success';
                };
                
                ws.onmessage = function(event) {
                    log(`📨 收到消息: ${event.data}`);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'sensor_data') {
                            updateSensorDisplay(data.data);
                        }
                    } catch (e) {
                        log(`❌ 解析消息失败: ${e.message}`);
                    }
                };
                
                ws.onclose = function() {
                    log('❌ WebSocket连接已关闭');
                    document.getElementById('wsStatus').textContent = '已断开';
                    document.getElementById('wsStatus').className = 'status error';
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket错误: ${error}`);
                    document.getElementById('wsStatus').textContent = '连接错误';
                    document.getElementById('wsStatus').className = 'status error';
                };
                
            } catch (error) {
                log(`❌ WebSocket初始化失败: ${error.message}`);
            }
        }

        function sendTestCommand() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testCommand = {
                    type: 'command',
                    command: 'ping',
                    data: {},
                    timestamp: Date.now()
                };
                ws.send(JSON.stringify(testCommand));
                log('📤 发送测试命令: ping');
            } else {
                log('❌ WebSocket未连接，无法发送命令');
            }
        }

        function testDeviceOrientation() {
            if ('DeviceOrientationEvent' in window) {
                log('🧭 开始测试设备方向...');
                
                // 请求权限（iOS 13+）
                if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                    DeviceOrientationEvent.requestPermission()
                        .then(response => {
                            if (response === 'granted') {
                                enableOrientationListener();
                            } else {
                                log('❌ 设备方向权限被拒绝');
                            }
                        })
                        .catch(err => log(`❌ 权限请求失败: ${err}`));
                } else {
                    enableOrientationListener();
                }
            } else {
                log('❌ 设备不支持方向传感器');
            }
        }

        function enableOrientationListener() {
            window.addEventListener('deviceorientation', function(event) {
                document.getElementById('alpha').textContent = event.alpha ? event.alpha.toFixed(1) : '--';
                document.getElementById('beta').textContent = event.beta ? event.beta.toFixed(1) : '--';
                document.getElementById('gamma').textContent = event.gamma ? event.gamma.toFixed(1) : '--';
            });
            log('✅ 设备方向监听已启用');
        }

        function testVideoStream() {
            const video = document.getElementById('testVideo');
            const statusDiv = document.getElementById('videoStatus');
            
            log('📹 测试视频流...');
            video.style.display = 'block';
            
            video.onloadstart = function() {
                log('📹 开始加载视频流');
                statusDiv.textContent = '加载中...';
                statusDiv.className = 'status info';
            };
            
            video.onloadeddata = function() {
                log('✅ 视频流加载成功');
                statusDiv.textContent = '视频流正常';
                statusDiv.className = 'status success';
            };
            
            video.onerror = function() {
                log('❌ 视频流加载失败');
                statusDiv.textContent = '视频流失败';
                statusDiv.className = 'status error';
            };
            
            // 尝试播放
            video.play().catch(e => {
                log(`❌ 视频播放失败: ${e.message}`);
            });
        }

        function startSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
            }
            
            log('🎲 开始模拟传感器数据');
            let startTime = Date.now();
            
            simulationInterval = setInterval(() => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                // 模拟数据
                const depth = 2.0 + Math.sin(elapsed * 0.1) * 0.5;
                const heading = Math.floor((elapsed * 10) % 360);
                const temp = 25.0 + Math.sin(elapsed * 0.05) * 2.0;
                
                // 更新显示
                document.getElementById('simDepth').textContent = depth.toFixed(1);
                document.getElementById('simHeading').textContent = heading;
                document.getElementById('simTemp').textContent = temp.toFixed(1);
                
                // 发送到WebSocket（如果连接）
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const sensorData = {
                        type: 'sensor_data',
                        data: {
                            depth: depth,
                            rovHeading: heading,
                            temperature: temp
                        },
                        timestamp: Date.now()
                    };
                    // 这里不发送，只是模拟显示
                }
            }, 1000);
        }

        function stopSimulation() {
            if (simulationInterval) {
                clearInterval(simulationInterval);
                simulationInterval = null;
                log('⏹️ 停止模拟传感器数据');
            }
        }

        function updateSensorDisplay(data) {
            if (data.depth !== undefined) {
                document.getElementById('simDepth').textContent = data.depth.toFixed(1);
            }
            if (data.rovHeading !== undefined) {
                document.getElementById('simHeading').textContent = data.rovHeading;
            }
            if (data.temperature !== undefined) {
                document.getElementById('simTemp').textContent = data.temperature.toFixed(1);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 ROV测试页面已加载');
            log('点击上方按钮开始测试各项功能');
        });
    </script>
</body>
</html>
