#!/usr/bin/env python3
"""
摄像头功能测试脚本
用于验证OpenCV和摄像头是否正常工作
"""

import sys
import time

def test_opencv():
    """测试OpenCV是否可用"""
    try:
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        return True
    except ImportError:
        print("❌ OpenCV未安装")
        print("请运行: pip3 install opencv-python")
        return False

def test_camera():
    """测试摄像头是否可用"""
    try:
        import cv2
        
        print("🔍 检测可用摄像头...")
        
        # 尝试打开摄像头
        for i in range(5):  # 尝试前5个设备
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                print(f"✅ 找到摄像头设备: /dev/video{i}")
                
                # 获取摄像头信息
                width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                fps = cap.get(cv2.CAP_PROP_FPS)
                
                print(f"   分辨率: {width}x{height}")
                print(f"   帧率: {fps}")
                
                # 尝试读取一帧
                ret, frame = cap.read()
                if ret:
                    print(f"   帧大小: {frame.shape}")
                    print("✅ 摄像头读取测试成功")
                else:
                    print("❌ 无法读取摄像头数据")
                
                cap.release()
                return True
            else:
                cap.release()
        
        print("❌ 未找到可用的摄像头设备")
        return False
        
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
        return False

def test_video_encoding():
    """测试视频编码功能"""
    try:
        import cv2
        import numpy as np
        
        print("🎬 测试视频编码...")
        
        # 创建测试图像
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:] = (100, 150, 200)  # 填充颜色
        
        # 添加文字
        cv2.putText(frame, 'Test Frame', (50, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 编码为JPEG
        ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
        
        if ret:
            print(f"✅ JPEG编码成功，大小: {len(buffer)} 字节")
            return True
        else:
            print("❌ JPEG编码失败")
            return False
            
    except Exception as e:
        print(f"❌ 视频编码测试失败: {e}")
        return False

def test_numpy():
    """测试NumPy是否可用"""
    try:
        import numpy as np
        print(f"✅ NumPy版本: {np.__version__}")
        return True
    except ImportError:
        print("❌ NumPy未安装")
        print("请运行: pip3 install numpy")
        return False

def main():
    """主测试函数"""
    print("🧪 ROV摄像头系统测试")
    print("=" * 40)
    
    tests = [
        ("OpenCV", test_opencv),
        ("NumPy", test_numpy),
        ("摄像头设备", test_camera),
        ("视频编码", test_video_encoding)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 20)
        result = test_func()
        results.append((test_name, result))
        time.sleep(0.5)
    
    print("\n" + "=" * 40)
    print("📊 测试结果汇总:")
    print("=" * 40)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 所有测试通过！摄像头系统可以正常使用")
        print("💡 提示: 现在可以启动ROV Web服务器")
        print("   运行: ./start.sh")
    else:
        print("⚠️  部分测试失败，请检查以下问题:")
        print("   1. 确保摄像头已连接")
        print("   2. 检查摄像头权限")
        print("   3. 安装缺失的依赖包")
        print("   4. 尝试运行: sudo usermod -a -G video $USER")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
