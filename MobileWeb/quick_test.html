<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速视频流测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .video-container {
            max-width: 640px;
            margin: 20px auto;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        img {
            width: 100%;
            height: auto;
            display: block;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .info { background: #2196F3; }
        button {
            background: #00d4ff;
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0099cc;
        }
    </style>
</head>
<body>
    <h1>🚀 快速视频流测试</h1>
    <p>这个页面用于快速验证MJPEG视频流是否正常工作</p>
    
    <div class="video-container">
        <img id="videoStream" alt="MJPEG视频流">
    </div>
    
    <div class="status info" id="status">准备测试...</div>
    
    <div>
        <button onclick="startTest()">开始测试</button>
        <button onclick="reloadStream()">重新加载</button>
        <button onclick="checkStatus()">检查状态</button>
        <button onclick="goToMain()">返回主页</button>
    </div>
    
    <div style="margin-top: 30px; text-align: left; max-width: 600px; margin-left: auto; margin-right: auto;">
        <h3>📋 测试说明:</h3>
        <ul>
            <li>✅ 如果看到视频画面，说明MJPEG流工作正常</li>
            <li>🔄 如果没有画面，点击"重新加载"按钮</li>
            <li>🔍 点击"检查状态"查看详细信息</li>
            <li>🏠 测试完成后点击"返回主页"</li>
        </ul>
        
        <h3>🔧 故障排除:</h3>
        <ul>
            <li>确保服务器正在运行 (python3 server.py)</li>
            <li>检查摄像头是否连接</li>
            <li>尝试直接访问: <a href="/video_feed" target="_blank" style="color: #00d4ff;">/video_feed</a></li>
        </ul>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function startTest() {
            const img = document.getElementById('videoStream');
            updateStatus('正在连接视频流...', 'info');
            
            img.onload = function() {
                updateStatus('✅ 视频流连接成功！画面正常显示', 'success');
                console.log('视频流加载成功');
            };
            
            img.onerror = function() {
                updateStatus('❌ 视频流连接失败，请检查服务器状态', 'error');
                console.log('视频流加载失败');
            };
            
            // 设置视频源
            img.src = '/video_feed?t=' + Date.now();
        }

        function reloadStream() {
            const img = document.getElementById('videoStream');
            updateStatus('重新加载视频流...', 'info');
            img.src = '/video_feed?t=' + Date.now();
        }

        function checkStatus() {
            const img = document.getElementById('videoStream');
            
            if (img.src) {
                if (img.complete && img.naturalWidth > 0) {
                    updateStatus(`✅ 视频流正常 - 尺寸: ${img.naturalWidth}x${img.naturalHeight}`, 'success');
                } else if (img.complete) {
                    updateStatus('❌ 视频流加载完成但无有效图像', 'error');
                } else {
                    updateStatus('🔄 视频流正在加载中...', 'info');
                }
            } else {
                updateStatus('❌ 未设置视频源', 'error');
            }
            
            console.log('视频流状态:', {
                src: img.src,
                complete: img.complete,
                naturalWidth: img.naturalWidth,
                naturalHeight: img.naturalHeight
            });
        }

        function goToMain() {
            window.location.href = '/';
        }

        // 页面加载完成后自动开始测试
        window.onload = function() {
            console.log('快速测试页面已加载');
            setTimeout(startTest, 1000); // 1秒后自动开始测试
        };

        // 每5秒检查一次状态
        setInterval(checkStatus, 5000);
    </script>
</body>
</html>
