#!/usr/bin/env python3
"""
简单的串口测试程序
用于测试与ESP32的基本串口通信
"""

import serial
import time
import sys

def test_serial_connection():
    """测试串口连接"""
    port = '/dev/tty.usbserial-0001'
    baudrate = 115200
    
    print(f"🔌 尝试连接串口: {port}")
    print(f"📊 波特率: {baudrate}")
    
    try:
        # 打开串口
        ser = serial.Serial(port, baudrate, timeout=1)
        print("✅ 串口连接成功")
        
        # 等待ESP32启动
        print("⏳ 等待ESP32启动...")
        time.sleep(2)
        
        # 清空缓冲区
        ser.flushInput()
        ser.flushOutput()
        
        print("\n📤 开始发送测试数据...")
        
        # 测试1: 发送简单字节
        print("\n🧪 测试1: 发送单个字节")
        for i in range(5):
            test_byte = bytes([0xAA])
            ser.write(test_byte)
            print(f"发送: {test_byte.hex().upper()}")
            time.sleep(0.5)
            
            # 检查是否有响应
            if ser.in_waiting > 0:
                response = ser.read(ser.in_waiting)
                print(f"收到响应: {response.hex().upper()}")
            else:
                print("无响应")
        
        # 测试2: 发送完整协议帧
        print("\n🧪 测试2: 发送心跳包协议帧")
        # 心跳包: AA FF 00 FF (帧头 + 命令 + 长度 + CRC)
        heartbeat_frame = bytes([0xAA, 0xFF, 0x00, 0xFF])
        
        for i in range(3):
            ser.write(heartbeat_frame)
            print(f"发送心跳包: {heartbeat_frame.hex().upper()}")
            time.sleep(1)
            
            # 等待响应
            start_time = time.time()
            while time.time() - start_time < 2:  # 等待2秒
                if ser.in_waiting > 0:
                    response = ser.read(ser.in_waiting)
                    print(f"收到响应: {response.hex().upper()}")
                    break
                time.sleep(0.1)
            else:
                print("心跳包无响应")
        
        # 测试3: 监听ESP32发送的调试信息
        print("\n🧪 测试3: 监听ESP32调试信息 (10秒)")
        start_time = time.time()
        while time.time() - start_time < 10:
            if ser.in_waiting > 0:
                try:
                    # 尝试读取文本数据
                    data = ser.read(ser.in_waiting)
                    try:
                        text = data.decode('utf-8', errors='ignore')
                        if text.strip():
                            print(f"📥 收到文本: {text.strip()}")
                    except:
                        print(f"📥 收到数据: {data.hex().upper()}")
                except Exception as e:
                    print(f"读取错误: {e}")
            time.sleep(0.1)
        
        # 测试4: 发送气压计数据请求
        print("\n🧪 测试4: 发送气压计数据请求")
        # 计算CRC8
        def calculate_crc8(data):
            crc = 0x00
            for byte in data:
                crc ^= byte
                for _ in range(8):
                    if crc & 0x80:
                        crc = (crc << 1) ^ 0x07
                    else:
                        crc <<= 1
                    crc &= 0xFF
            return crc
        
        # 气压计数据请求: AA 02 00 CRC
        cmd_data = bytes([0x02, 0x00])
        crc = calculate_crc8(cmd_data)
        pressure_request = bytes([0xAA]) + cmd_data + bytes([crc])
        
        for i in range(3):
            ser.write(pressure_request)
            print(f"发送气压计请求: {pressure_request.hex().upper()}")
            time.sleep(1)
            
            # 等待响应
            start_time = time.time()
            response_data = b''
            while time.time() - start_time < 3:  # 等待3秒
                if ser.in_waiting > 0:
                    new_data = ser.read(ser.in_waiting)
                    response_data += new_data
                    print(f"收到数据: {new_data.hex().upper()}")
                    
                    # 检查是否收到完整响应
                    if len(response_data) >= 4:
                        break
                time.sleep(0.1)
            
            if response_data:
                print(f"完整响应: {response_data.hex().upper()}")
            else:
                print("气压计请求无响应")
        
        print("\n✅ 串口测试完成")
        
    except serial.SerialException as e:
        print(f"❌ 串口错误: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        try:
            ser.close()
            print("🔌 串口已关闭")
        except:
            pass

def check_serial_ports():
    """检查可用的串口"""
    import serial.tools.list_ports
    
    print("🔍 扫描可用串口:")
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("❌ 未找到任何串口设备")
        return
    
    for port in ports:
        print(f"  📍 {port.device}")
        print(f"     描述: {port.description}")
        print(f"     硬件ID: {port.hwid}")
        print()

def main():
    """主函数"""
    print("🧪 ESP32串口通信测试")
    print("=" * 50)
    
    # 检查可用串口
    check_serial_ports()
    
    # 测试串口通信
    test_serial_connection()

if __name__ == "__main__":
    main()
