<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket客户端功能测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }
        
        .status-disconnected {
            background: #f44336;
            box-shadow: 0 0 10px #f44336;
        }
        
        .status-connecting {
            background: #ff9800;
            box-shadow: 0 0 10px #ff9800;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .demo-button {
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #45a049;
        }
        
        .demo-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .log-entry {
            margin: 2px 0;
            word-wrap: break-word;
        }
        
        .log-info { color: #0ff; }
        .log-success { color: #0f0; }
        .log-warning { color: #ff0; }
        .log-error { color: #f00; }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .data-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .data-label {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .data-value {
            color: #ffffff;
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <h1 style="color: white; text-align: center; margin: 20px 0;">WebSocket客户端功能测试</h1>
            
            <!-- 连接状态 -->
            <div class="demo-section">
                <h3 style="color: white;">
                    <span class="status-indicator status-disconnected" id="connectionStatus"></span>
                    OrangePi服务器连接状态
                </h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>服务器地址:</strong> <span id="serverUrl">orangepi3b.local:8766</span></p>
                    <p><strong>连接状态:</strong> <span id="connectionText">未连接</span></p>
                    <p><strong>最后连接时间:</strong> <span id="lastConnectTime">--</span></p>
                    <p><strong>数据发送次数:</strong> <span id="sendCount">0</span></p>
                    <p><strong>数据接收次数:</strong> <span id="receiveCount">0</span></p>
                </div>
            </div>
            
            <!-- 功能说明 -->
            <div class="demo-section">
                <h3 style="color: white;">WebSocket客户端功能</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p><strong>主要功能:</strong></p>
                    <ul>
                        <li>🔗 自动连接到OrangePi服务器 (orangepi3b.local:8766)</li>
                        <li>📡 定期发送ROV传感器数据到OrangePi</li>
                        <li>🎮 接收来自OrangePi的远程控制命令</li>
                        <li>🔄 自动重连机制，连接断开时自动重试</li>
                        <li>🚨 支持紧急停止等安全功能</li>
                    </ul>
                    
                    <p><strong>支持的消息类型:</strong></p>
                    <ul>
                        <li><code>sensor_data</code> - 传感器数据发送</li>
                        <li><code>command</code> - 接收控制命令</li>
                        <li><code>status</code> - 状态信息交换</li>
                        <li><code>control</code> - 运动/摄像头/灯光控制</li>
                    </ul>
                </div>
            </div>
            
            <!-- 模拟数据发送 -->
            <div class="demo-section">
                <h3 style="color: white;">模拟数据发送测试</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p>模拟向OrangePi服务器发送不同类型的数据：</p>
                </div>
                <button class="demo-button" onclick="sendSensorData()">📊 发送传感器数据</button>
                <button class="demo-button" onclick="sendStatusUpdate()">📋 发送状态更新</button>
                <button class="demo-button" onclick="sendConnectionInfo()">🔗 发送连接信息</button>
                <button class="demo-button" onclick="sendModeStatus()">🎮 发送模式状态</button>
            </div>
            
            <!-- 模拟接收命令 -->
            <div class="demo-section">
                <h3 style="color: white;">模拟接收命令测试</h3>
                <div style="color: #ffffff; margin: 10px 0;">
                    <p>模拟接收来自OrangePi服务器的各种命令：</p>
                </div>
                <button class="demo-button" onclick="simulateGetSensorCommand()">📡 模拟获取传感器数据命令</button>
                <button class="demo-button" onclick="simulateSetModeCommand()">🎮 模拟设置模式命令</button>
                <button class="demo-button" onclick="simulateEmergencyStop()">🚨 模拟紧急停止命令</button>
                <button class="demo-button" onclick="simulateMovementControl()">🎛️ 模拟运动控制命令</button>
            </div>
            
            <!-- 当前传感器数据 -->
            <div class="demo-section">
                <h3 style="color: white;">当前传感器数据</h3>
                <div class="data-grid">
                    <div class="data-item">
                        <div class="data-label">深度</div>
                        <div class="data-value" id="currentDepth">5.2 m</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">航向</div>
                        <div class="data-value" id="currentHeading">135°</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">水温</div>
                        <div class="data-value" id="currentTemperature">24.3°C</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">压力</div>
                        <div class="data-value" id="currentPressure">1520.4 hPa</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">电池电压</div>
                        <div class="data-value" id="currentBattery">8.2 V</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">入水状态</div>
                        <div class="data-value" id="currentUnderwaterStatus">入水</div>
                    </div>
                </div>
                <button class="demo-button" onclick="updateSensorData()">🔄 更新传感器数据</button>
            </div>
            
            <!-- 通信日志 -->
            <div class="demo-section">
                <h3 style="color: white;">通信日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[INFO] WebSocket客户端功能测试页面已加载</div>
                    <div class="log-entry log-warning">[WARN] 这是模拟测试页面，实际通信由server.py处理</div>
                </div>
                <button class="demo-button" onclick="clearLog()">🗑️ 清空日志</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        let sensorData = {
            depth: 5.2,
            rovHeading: 135,
            temperature: 24.3,
            pressure: 1520.4,
            adcVoltage: 8.2,
            isUnderwater: true
        };

        let sendCount = 0;
        let receiveCount = 0;

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新计数器
        function updateCounters() {
            document.getElementById('sendCount').textContent = sendCount;
            document.getElementById('receiveCount').textContent = receiveCount;
        }

        // 发送传感器数据
        function sendSensorData() {
            const data = {
                type: 'sensor_data',
                data: sensorData,
                timestamp: Date.now() / 1000
            };
            
            addLog(`发送传感器数据: ${JSON.stringify(data)}`, 'success');
            sendCount++;
            updateCounters();
        }

        // 发送状态更新
        function sendStatusUpdate() {
            const data = {
                type: 'status',
                status: {
                    system: 'online',
                    esp32_connected: true,
                    xbox_controller: false,
                    serial_latency: 25
                },
                timestamp: Date.now() / 1000
            };
            
            addLog(`发送状态更新: ${JSON.stringify(data)}`, 'success');
            sendCount++;
            updateCounters();
        }

        // 发送连接信息
        function sendConnectionInfo() {
            const data = {
                type: 'connection',
                client_id: 'rov_controller',
                version: '1.0.0',
                capabilities: ['sensor_data', 'remote_control', 'emergency_stop'],
                timestamp: Date.now() / 1000
            };
            
            addLog(`发送连接信息: ${JSON.stringify(data)}`, 'success');
            sendCount++;
            updateCounters();
        }

        // 发送模式状态
        function sendModeStatus() {
            const data = {
                type: 'mode_status',
                modes: {
                    depth_hold: { enabled: false, target: 0 },
                    heading_hold: { enabled: true, target: 135 }
                },
                timestamp: Date.now() / 1000
            };
            
            addLog(`发送模式状态: ${JSON.stringify(data)}`, 'success');
            sendCount++;
            updateCounters();
        }

        // 模拟接收获取传感器数据命令
        function simulateGetSensorCommand() {
            const command = {
                type: 'command',
                command: 'get_sensor_data',
                params: {
                    sensors: ['depth', 'heading', 'temperature']
                }
            };
            
            addLog(`接收命令: ${JSON.stringify(command)}`, 'info');
            addLog('处理获取传感器数据命令', 'success');
            receiveCount++;
            updateCounters();
        }

        // 模拟接收设置模式命令
        function simulateSetModeCommand() {
            const command = {
                type: 'command',
                command: 'set_mode',
                params: {
                    mode: 'depth_hold',
                    enabled: true,
                    target: 10.5
                }
            };
            
            addLog(`接收命令: ${JSON.stringify(command)}`, 'info');
            addLog('处理设置定深模式命令，目标深度: 10.5m', 'success');
            receiveCount++;
            updateCounters();
        }

        // 模拟紧急停止
        function simulateEmergencyStop() {
            const command = {
                type: 'command',
                command: 'emergency_stop',
                params: {}
            };
            
            addLog(`接收命令: ${JSON.stringify(command)}`, 'warning');
            addLog('🚨 执行紧急停止！停止所有运动，禁用所有模式', 'error');
            receiveCount++;
            updateCounters();
        }

        // 模拟运动控制
        function simulateMovementControl() {
            const command = {
                type: 'control',
                control_type: 'movement',
                data: {
                    forward: 0.5,
                    turn: -0.3,
                    vertical: 0.2
                }
            };
            
            addLog(`接收控制命令: ${JSON.stringify(command)}`, 'info');
            addLog('处理运动控制：前进50%，左转30%，上升20%', 'success');
            receiveCount++;
            updateCounters();
        }

        // 更新传感器数据
        function updateSensorData() {
            sensorData.depth = Math.random() * 20;
            sensorData.rovHeading = Math.floor(Math.random() * 360);
            sensorData.temperature = Math.random() * 15 + 15;
            sensorData.pressure = Math.random() * 1000 + 1000;
            sensorData.adcVoltage = Math.random() * 3 + 6;
            sensorData.isUnderwater = Math.random() > 0.3;
            
            // 更新显示
            document.getElementById('currentDepth').textContent = `${sensorData.depth.toFixed(1)} m`;
            document.getElementById('currentHeading').textContent = `${sensorData.rovHeading}°`;
            document.getElementById('currentTemperature').textContent = `${sensorData.temperature.toFixed(1)}°C`;
            document.getElementById('currentPressure').textContent = `${sensorData.pressure.toFixed(1)} hPa`;
            document.getElementById('currentBattery').textContent = `${sensorData.adcVoltage.toFixed(1)} V`;
            document.getElementById('currentUnderwaterStatus').textContent = sensorData.isUnderwater ? '入水' : '水面';
            
            addLog('传感器数据已更新', 'info');
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // 模拟连接状态变化
        function simulateConnectionStatus() {
            const statusIndicator = document.getElementById('connectionStatus');
            const connectionText = document.getElementById('connectionText');
            const lastConnectTime = document.getElementById('lastConnectTime');
            
            const states = [
                { class: 'status-connecting', text: '连接中...', time: null },
                { class: 'status-connected', text: '已连接', time: new Date().toLocaleTimeString() },
                { class: 'status-disconnected', text: '连接断开', time: null },
                { class: 'status-connecting', text: '重连中...', time: null }
            ];
            
            let currentState = 0;
            
            setInterval(() => {
                const state = states[currentState];
                statusIndicator.className = `status-indicator ${state.class}`;
                connectionText.textContent = state.text;
                if (state.time) {
                    lastConnectTime.textContent = state.time;
                }
                
                currentState = (currentState + 1) % states.length;
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateCounters();
            simulateConnectionStatus();
            addLog('WebSocket客户端功能测试页面已初始化', 'success');
            
            // 定期更新传感器数据
            setInterval(() => {
                if (Math.random() > 0.7) {
                    updateSensorData();
                }
            }, 5000);
        });
    </script>
</body>
</html>
