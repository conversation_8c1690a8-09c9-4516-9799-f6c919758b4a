import json
import paho.mqtt.client as mqtt
import time
from colorama import Fore,init,Style,Back
init(autoreset=True)
# ROV端


# 回调函数：连接成功时触发
def on_connect(client, userdata, flags, rc):
    print(f"Connected with result code {rc}")
    client.subscribe("rov/command")  # 订阅主题

# 回调函数：收到消息时触发
def on_message(client, userdata, msg):
    
    data = json.loads(msg.payload.decode())
    
    start_timestamp=data.get('timestamp')
    index=data.get('index')
    use_time=time.time()-start_timestamp
    
    # print(Fore.GREEN+f"[{msg.topic}]message({use_time:.3f}): {msg.payload.decode()}")
    
    message = f"{{\"index\":\"{index}\",\"timestamp\":{time.time()},\"start_timestamp\":{start_timestamp},\"use_time\":{(time.time()-start_timestamp):.3f}}}"
    client.publish("rov/status", message)

# 创建 MQTT 客户端
client = mqtt.Client()
client.on_connect = on_connect
client.on_message = on_message

# 连接到 MQTT 代理（默认端口 1883）
client.connect("192.168.1.1", 1883, 60)
# client.connect("orangepi3b.local", 1883, 60)

# 保持连接，监听消息
client.loop_forever()
