#!/usr/bin/env python3
"""
ROV Web控制服务器
提供WebSocket通信、视频流转发、传感器数据处理
"""

import asyncio
from datetime import datetime
import math
import websockets
import json
import time
import threading
import struct
from flask import Flask, Response, render_template, send_from_directory
import sys
import os
import pyudev
import numpy as np

from colorama import Fore,init,Style,Back
from adjust_img import enhance_underwater_video
from config_manager import ConfigManager

print(Fore.GREEN+"\n\n\n--------------------ROV REMOTE SERVER Starting------------------\n")

MAC_DEBUG=False
MAC_DEBUG=True

baudrate=115200

MENU_SERVO_PID=0

def floats_to_bytearray(float_list):
    """将多个浮点数转换为字节数组"""
    format_str = f'{len(float_list)}f'
    bytes_data = struct.pack(format_str, *float_list)
    return bytearray(bytes_data)

def find_camera_index(device_name):
    context = pyudev.Context()
    cameras = []
    index=None
    for device in context.list_devices(subsystem='video4linux'):
        # 获取设备节点（如 /dev/video0）
        dev_node = device.device_node
        
        # 获取摄像头名称（可能存储在 ID_MODEL 或 ID_V4L_PRODUCT）
        model = device.get('ID_MODEL') or device.get('ID_V4L_PRODUCT') or "Unknown Camera"
        
        cameras.append({
            "name": model,
            "path": dev_node,
            "index": int(dev_node[-1])  # 提取数字（如 video0 -> 0）
        })
        
        # print(f"\n\n")
        # print(Fore.YELLOW+f"*"*20+f"{model},{int(dev_node[-1])}")
        # print(f"\n\n")
        
        if device_name in model:
            dev_path = device.device_node
            print(Fore.CYAN+f"*"*30+f"Find camera {device_name} at {dev_path} index:{int(dev_path[-1])}")
            return int(dev_path[-1])  # 如 /dev/video0 -> 0
        
    # print(cameras)
    return None


if MAC_DEBUG:
    # 测试
    camera_index=1
    
    # 开发板的串口通过Zero3网络转换
    # serial_port_str="/dev/tty.wchusbserial599D0149021"
    serial_port_str ='/dev/tty.wchusbserial599C0020501'
    
    serial_port_str ='/dev/tty.wchusbserial140'
    
    
    #单线串口板
    # serial_port_str ='/dev/tty.wchusbserial5A680119801'
    # 开发板
    # serial_port_str="/dev/tty.wchusbserial539E0045951"
    # 转换板
    # serial_port_str'/dev/tty.usbserial-0001'
    
    ws_port=8765
    # ws_port=8799
else:
    # Zero3
    camera_index=find_camera_index("icspring_camera")
    # camera_index=find_camera_index("USB_camera")
    
    # camera_index=2

    serial_port_str="/dev/ttyACM0"
    
    ws_port=8765

try:
    import cv2
    CV2_AVAILABLE = True
    print("\n\n---------------------OpenCV已加载，摄像头功能可用------------------")
except ImportError:
    CV2_AVAILABLE = False
    print("警告: OpenCV未安装，摄像头功能不可用")

# 添加父目录到路径，以便导入App模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from Serial.serial_port import SerialPort
except ImportError:
    print(Fore.RED+"警告: 无法导入串口模块，将使用模拟数据")
    SerialPort = None


def map_value(value, in_min, in_max, out_min, out_max):
    """将输入值从一个范围映射到另一个范围"""
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

def altitude_at_pressure(pressure,P0=101325.0):
    """
    根据气压计算海拔高度(国际标准大气模型)
    参数:
        pressure: 气压(Pa)
    返回:
        高度/海拔(米)
    """
    # 常量定义
    # P0 = 101325.0     # 海平面标准大气压(Pa)
    T0 = 288.15       # 海平面标准温度(K)
    L = 0.0065        # 温度递减率(K/m)
    g = 9.80665       # 重力加速度(m/s²)
    M = 0.0289644     # 干空气摩尔质量(kg/mol)
    R = 8.31446       # 通用气体常数(J/(mol·K))
    
    if pressure > 22632:  # 对流层(0-11km)
        exponent = (R * L) / (g * M)
        height = (T0 / L) * (1 - (pressure / P0) ** exponent)
    else:  # 平流层(11-20km)，简化处理
        height = 11000 + 6341.62 * math.log(22632.0 / pressure)
    
    return height

def calculate_crc8(data):
    """计算CRC8校验码，遵循ROV协议标准"""
    crc = 0x00
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x07
            else:
                crc <<= 1
            crc &= 0xFF
    return crc
    
class ROVWebServer:
    def __init__(self):
        print("ROVWebServer init")

        # 初始化配置管理器
        self.config_manager = ConfigManager("data")
        
        self.camera_settings = {
        'width': 1280,
        'height': 720,
        'fps': 30,
        'quality': 85,
        'contrast': 1.0,    # 对比度 (0.5-3.0, 1.0为正常)
        'saturation': 1.0,  # 饱和度 (0.0-2.0, 1.0为正常)
        'sharpness': 0.0,   # 锐化强度 (0.0-3.0, 0.0为不锐化)
        'sharpen_method': 'standard'  # 锐化方法 ('standard', 'unsharp_mask', 'laplacian')
        }
        self.camera_settings = self.config_manager.get_camera_settings()

        self.clients = set()
        self.serial_port = None
        self.controller_thread = None
        self.controller_running = False
        self.sensor_data = {
            'depth': 0.0,
            'rovHeading': 0,
            'phoneHeading': 0,
            'temperature': 0.0,
            'pressure': 0.0,
            'adcVoltage': 0.00,
            'serialLatency': 150,  # 串口延迟(毫秒)
            'serialResponseStatus': 'disconnected',  # 串口响应状态
            'roll': 0.0,         # MPU6050横滚角度
            'pitch': 0.0,        # MPU6050俯仰角度
            'yaw': 0.0,          # MPU6050偏航角度
            'waterSealValue': 255,  # 水密封性能原始值
            'waterSealStatus': 0,  # 水密封性能状态 (0-3)
            'waterSealText': '密封良好',  # 水密封性能文本描述
            'waterDetectionValue': 255,  # 入水检测原始值
            'isUnderwater': False,  # 是否入水状态,
            
            'mode':0, #操作模式：0普通，1定深
            'joystickStatus': 0, #手柄连接状态：0未连接，1已连接
            'esp32CommunicationError':"",
            'esp32CommunicationResponseText':""
        }
        
        self.is_underwater=False
        
        self.esp32_init=False

        # 串口延迟计时
        self.serial_send_time = None
        self.serial_receive_time=0

        # 串口数据缓冲区，用于帧头对齐
        self.serial_buffer = bytearray()
        
        self.is_recording = False
        self.camera = None

        # 视频文件模式相关
        self.video_mode = False
        
        if MAC_DEBUG:
            # self.video_mode = True
            pass
        
        self.video_file_path = None
   
   
         # 舵机PID
        # [0] 0:P 1:I 2:D
        self.menu_item_index=[0,0]
        
        self.menu_items=[
            ["舵机PID",["P","I","D","目标电流"]],
            ["摄像头设置",["分辨率","帧率","对比度","饱和度","锐化"]],
            ["串口设置",["波特率","超时时间"]],
            ["系统设置",["自动保存","保存间隔"]],
            ["视频设置",["视频模式","视频文件路径","循环模式","自动缩放","显示叠加","应用效果"]],
        ]
             
        # 舵机
        self.servoCurrentKp=None
        self.servoCurrentKi=None
        self.servoCurrentKd=None
        self.servo_target_current=None
        
        self.adjust_image_switcher=False
        
        # 水面临界数值
        self.surface_pressure=0
        
        self.serial_response_dispaly_lasttime=0
        
        self.has_other_commond=False
        
        self.serial_commond_list=[]
        
        self.timeout_start_timestamp=0
        self.timeout_log_timestamp=0
        self.timeout_not_count=0
        self.timeout_count=0
        
        self.USB_Camera_size_index=0
        self.icspring_camera_size_index=0
        
        self.setup_camera()
        
        self.setup_serial()
        
        self.send_init_data()

    def get_menu_item_name(self,menu_index,sub_index):
        return self.menu_items[menu_index][0]+":"+self.menu_items[menu_index][1][sub_index]
        
    def setup_camera(self):
        global camera_index
        
        """初始化摄像头"""
        try:
            print(Fore.YELLOW+f"打开摄像头[{camera_index}]")
            
            if MAC_DEBUG:
                self.camera = cv2.VideoCapture(camera_index)
            else:
                self.camera = cv2.VideoCapture(camera_index,cv2.CAP_V4L2)

            if not self.camera.isOpened():
                print(Fore.RED+f"警告: 无法打开摄像头[{camera_index}]")
                self.camera = None
               
                return

            # 设置摄像头参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.camera_settings['width'])
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.camera_settings['height'])
            self.camera.set(cv2.CAP_PROP_FPS, self.camera_settings['fps'])
           

            # 验证设置
            actual_width = self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)
            actual_height = self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
            actual_fps = self.camera.get(cv2.CAP_PROP_FPS)

            print(Fore.CYAN+f"\n摄像头[{camera_index}]初始化成功 - 分辨率: {self.camera_settings['width']}/{actual_width}x{self.camera_settings['height']}/{actual_height}, 帧率:{self.camera_settings['fps']}/{actual_fps}\n")

        except Exception as e:
            print(Fore.RED+f"摄像头初始化失败: {e}")
            self.camera = None

    def switch_camera(self, new_camera_index,width=640,height=480,fps=30):
        """切换摄像头"""
        global camera_index

        try:
            # 检查是否需要切换
            # if camera_index == new_camera_index:
            #     print(Fore.YELLOW + f"摄像头已经是[{new_camera_index}]，无需切换")
            #     return

            # 更新摄像头索引
            # old_camera_index = camera_index
            camera_index = new_camera_index
            
            print(Fore.CYAN + f"🎥 切换摄像头: [{camera_index}] → [{new_camera_index}]")

            # 释放当前摄像头
            if self.camera:
                self.camera.release()
                self.camera = None
                print(Fore.YELLOW + f"已释放摄像头[{camera_index}]")

            # 初始化新摄像头
            if MAC_DEBUG:
                self.camera = cv2.VideoCapture(camera_index)
            else:
                self.camera = cv2.VideoCapture(camera_index, cv2.CAP_V4L2)

            if self.camera.isOpened():
                # 设置摄像头参数
                self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, width)
                self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
                self.camera.set(cv2.CAP_PROP_FPS, fps)

                # 验证设置
                actual_width = self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)
                actual_height = self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
                actual_fps = self.camera.get(cv2.CAP_PROP_FPS)

                print(Fore.GREEN + f"✅ 摄像头[{camera_index}]切换成功 - 分辨率: {actual_width}x{actual_height}, 帧率: {actual_fps}")
            else:
                print(Fore.RED + f"❌ 无法打开摄像头[{camera_index}]")

                # print(Fore.RED + f"❌ 无法打开摄像头[{camera_index}]，回退到摄像头[{old_camera_index}]")

                # # 回退到原摄像头
                # camera_index = old_camera_index
                # if MAC_DEBUG:
                #     self.camera = cv2.VideoCapture(camera_index)
                # else:
                #     self.camera = cv2.VideoCapture(camera_index, cv2.CAP_V4L2)

                # if not self.camera.isOpened():
                #     print(Fore.RED + f"❌ 回退失败，摄像头[{camera_index}]也无法打开")
                #     self.camera = None
                # else:
                #     print(Fore.YELLOW + f"⚠️ 已回退到摄像头[{camera_index}]")
                

        except Exception as e:
            print(Fore.RED + f"❌ 摄像头切换失败: {e}")
            # 尝试恢复到原摄像头
            # try:
            #     if MAC_DEBUG:
            #         self.camera = cv2.VideoCapture(camera_index)
            #     else:
            #         self.camera = cv2.VideoCapture(camera_index, cv2.CAP_V4L2)
            # except:
            #     self.camera = None
    
    def restart_camera(self):
        """重启摄像头以应用新设置"""
        if self.camera:
            self.camera.release()
        self.setup_camera()
        
    def adjust_image_properties(self, frame, contrast=None, saturation=None):
        """调整图像的对比度和饱和度

        Args:
            frame: 输入图像帧 (BGR格式)
            contrast: 对比度值 (0.5-3.0, 1.0为正常, None使用默认值)
            saturation: 饱和度值 (0.0-2.0, 1.0为正常, None使用默认值)

        Returns:
            调整后的图像帧
        """
        try:
            # 使用默认值如果参数为None
            if contrast is None:
                contrast = self.camera_settings.get('contrast', 1.0)
            if saturation is None:
                saturation = self.camera_settings.get('saturation', 1.0)

            # 限制参数范围
            # contrast = max(0.5, min(3.0, contrast))
            # saturation = max(0.0, min(2.0, saturation))

            # print("self.camera_settings",self.camera_settings)
            # 调整对比度
            if contrast != 1.0:
                # 对比度调整公式: new_pixel = (old_pixel - 128) * contrast + 128
                frame = cv2.convertScaleAbs(frame, alpha=contrast, beta=128 * (1 - contrast))

            # 调整饱和度
            if saturation != 1.0:
                # 转换到HSV色彩空间进行饱和度调整
                hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                hsv = hsv.astype(np.float32)

                # 调整饱和度通道 (S通道)
                hsv[:, :, 1] = hsv[:, :, 1] * saturation

                # 限制饱和度值在0-255范围内
                hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)

                # 转换回BGR色彩空间
                hsv = hsv.astype(np.uint8)
                frame = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

            return frame

        except Exception as e:
            print(Fore.RED + f"图像属性调整失败: {e}")
            return frame  # 返回原始帧

    def set_image_properties(self, contrast=None, saturation=None, sharpness=None, sharpen_method=None, save_config=True):
        """设置图像属性

        Args:
            contrast: 对比度值 (0.5-4.0, 1.0为正常)
            saturation: 饱和度值 (0.0-5.0, 1.0为正常)
            sharpness: 锐化强度 (0.0-3.0, 0.0为不锐化)
            sharpen_method: 锐化方法 ('standard', 'unsharp_mask', 'laplacian')
            save_config: 是否保存配置到文件
        """
        updated_settings = {}

        if contrast is not None:
            contrast = max(0.5, min(4.0, contrast))
            self.camera_settings['contrast'] = contrast
            updated_settings['contrast'] = contrast
            print(Fore.CYAN + f"设置对比度: {contrast:.2f}")

        if saturation is not None:
            saturation = max(0.0, min(5.0, saturation))
            self.camera_settings['saturation'] = saturation
            updated_settings['saturation'] = saturation
            print(Fore.CYAN + f"设置饱和度: {saturation:.2f}")

        if sharpness is not None:
            sharpness = max(0.0, min(5.0, sharpness))
            self.camera_settings['sharpness'] = sharpness
            updated_settings['sharpness'] = sharpness
            print(Fore.CYAN + f"设置锐化强度: {sharpness:.2f}")

        if sharpen_method is not None:
            valid_methods = ['standard', 'unsharp_mask', 'laplacian']
            if sharpen_method in valid_methods:
                self.camera_settings['sharpen_method'] = sharpen_method
                updated_settings['sharpen_method'] = sharpen_method
                print(Fore.CYAN + f"设置锐化方法: {sharpen_method}")
            else:
                print(Fore.YELLOW + f"无效的锐化方法: {sharpen_method}, 支持的方法: {valid_methods}")

        # 保存配置到文件
        if save_config and updated_settings:
            self.config_manager.set_camera_settings(updated_settings, auto_save=True)
            
    def adaptive_sharpen_frame(self, frame, strength=1.0, method='standard'):
        """自适应锐化图像帧

        Args:
            frame: 输入图像帧 (BGR格式)
            strength: 锐化强度 (0.0-3.0)
            method: 锐化方法 ('standard', 'unsharp_mask', 'laplacian')

        Returns:
            锐化后的图像帧
        """
        try:
            if strength == 0.0:
                return frame

            if method == 'unsharp_mask':
                return self.unsharp_mask_sharpen(frame, strength)
            elif method == 'laplacian':
                return self.laplacian_sharpen(frame, strength)
            else:  # standard
                return self.sharpen_frame(frame, strength)

        except Exception as e:
            print(Fore.RED + f"自适应锐化失败: {e}")
            return frame

    def sharpen_frame(self, frame, strength=1.0):
        """锐化图像帧

        Args:
            frame: 输入图像帧 (BGR格式)
            strength: 锐化强度 (0.0-3.0, 1.0为正常锐化, 0.0为不锐化)

        Returns:
            锐化后的图像帧
        """
        try:
            # 限制锐化强度范围
            strength = max(0.0, min(3.0, strength))

            # 如果强度为0，直接返回原图
            if strength == 0.0:
                return frame

            # 创建锐化卷积核
            # 标准锐化核
            kernel_base = np.array([
                [0, -1, 0],
                [-1, 5, -1],
                [0, -1, 0]
            ], dtype=np.float32)

            # 根据强度调整锐化核
            # 中心值 = 1 + 4 * strength
            # 周围值 = -strength
            kernel = np.array([
                [0, -strength, 0],
                [-strength, 1 + 4 * strength, -strength],
                [0, -strength, 0]
            ], dtype=np.float32)

            # 应用锐化滤波器
            sharpened = cv2.filter2D(frame, -1, kernel)

            # 确保像素值在有效范围内
            sharpened = np.clip(sharpened, 0, 255).astype(np.uint8)

            return sharpened

        except Exception as e:
            print(Fore.RED + f"图像锐化失败: {e}")
            return frame  # 返回原始帧

    def unsharp_mask_sharpen(self, frame, strength=1.0, radius=1.0, threshold=0):
        """使用Unsharp Mask算法进行锐化

        Args:
            frame: 输入图像帧 (BGR格式)
            strength: 锐化强度 (0.0-3.0)
            radius: 模糊半径 (0.5-3.0)
            threshold: 阈值 (0-255, 只对差异大于此值的像素进行锐化)

        Returns:
            锐化后的图像帧
        """
        try:
            # 限制参数范围
            strength = max(0.0, min(3.0, strength))
            radius = max(0.5, min(3.0, radius))
            threshold = max(0, min(255, threshold))

            if strength == 0.0:
                return frame

            # 创建高斯模糊版本
            blurred = cv2.GaussianBlur(frame, (0, 0), radius)

            # 计算差异
            mask = cv2.subtract(frame, blurred)

            # 应用阈值
            if threshold > 0:
                _, mask = cv2.threshold(mask, threshold, 255, cv2.THRESH_BINARY)

            # 应用锐化
            sharpened = cv2.addWeighted(frame, 1.0, mask, strength, 0)

            return sharpened

        except Exception as e:
            print(Fore.RED + f"Unsharp Mask锐化失败: {e}")
            return frame


    def laplacian_sharpen(self, frame, strength=1.0):
        """使用拉普拉斯算子进行锐化

        Args:
            frame: 输入图像帧 (BGR格式)
            strength: 锐化强度 (0.0-3.0)

        Returns:
            锐化后的图像帧
        """
        try:
            if strength == 0.0:
                return frame

            # 转换为灰度图进行边缘检测
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 应用拉普拉斯算子
            # laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            # laplacian = np.float32(laplacian)  # 转换为 32 位浮点型（CV_32F）
            # laplacian = np.absolute(laplacian)
            
            # 方法 默认
            # laplacian = np.uint8(np.clip(laplacian, 0, 255))
            
            # 方法1：线性对比度拉伸
            # laplacian = (laplacian - laplacian.min()) * (255/(laplacian.max()-laplacian.min()))
            # laplacian = np.uint8(np.clip(laplacian, 0, 255))

            # 方法2：使用阈值处理（更强烈）
            # _, laplacian = cv2.threshold(laplacian, 40, 255, cv2.THRESH_BINARY)
            
            # 4
            # gray_blur = cv2.GaussianBlur(gray, (5,5), 0)  # 可调整核大小
            # laplacian = cv2.Laplacian(gray_blur, cv2.CV_64F)
            # laplacian = np.float32(laplacian)  # 转换为 32 位浮点型（CV_32F）
            # laplacian = np.absolute(laplacian)
            
            #5
            # sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            # sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            # laplacian = np.sqrt(sobelx**2 + sobely**2)  # 更强烈的边缘
            # laplacian = np.absolute(laplacian)
            # laplacian = np.uint8(laplacian)  # 转换为 uint8 (0-255)
            
            #6
            gray_blur = cv2.GaussianBlur(gray, (3,3), 0)
            laplacian = cv2.Laplacian(gray_blur, cv2.CV_64F, ksize=3)  # 增大ksize
            laplacian = np.absolute(laplacian)
            laplacian = np.uint8(255 * (laplacian / laplacian.max()))  # 归一化增强


            # 将拉普拉斯结果转换为3通道
            laplacian_3ch = cv2.cvtColor(laplacian, cv2.COLOR_GRAY2BGR)
            
            # sharpened=laplacian_3ch
            
            # 与原图像混合
            sharpened = cv2.addWeighted(frame, 1.0, laplacian_3ch, strength * 0.3, 0)
            
            return sharpened

        except Exception as e:
            print(Fore.RED + f"拉普拉斯锐化失败: {e}")
            return frame

    def generate_frames(self):
        """生成视频帧流"""
        
        # if self.camera is None or not self.camera.isOpened():
        #     # 如果OpenCV不可用，生成测试图像
        #     yield from self.generate_test_frames()
        #     return
        
        # 检查是否为视频文件模式
        if self.video_mode:
            print(Fore.CYAN + "🎬 使用视频文件模式")
            yield from self.generate_video_file_frames(self.video_file_path)
            return

        fps_time=time.time()
        while True:
            try:
                # if self.camera.isOpened():
                #     print("self.camera.isOpened")
                    
                success, frame = self.camera.read()
                if not success:
                    print(Fore.RED+f"*"*10+"摄像头读取失败，等待摄像头恢复...")
                    # yield from self.generate_test_frames()
                    time.sleep(0.1)
                    # TODO 将状态编码到图像上
                else:
                    if self.adjust_image_switcher:
                        # 应用图像属性调整（对比度、饱和度）
                        # frame = self.adjust_image_properties(frame)

                        # 应用锐化处理
                        # sharpness = self.camera_settings.get('sharpness', 0.0)
                        # if sharpness > 0.0:
                        #     sharpen_method = self.camera_settings.get('sharpen_method', 'standard')
                        #     frame = self.adaptive_sharpen_frame(frame, sharpness, sharpen_method)

                        frame=enhance_underwater_video(frame)
                        
                    # ！！！TODO 2个摄像头分别判断翻转
                    # 垂直翻转图像
                    flipped_frame = cv2.flip(frame, -1)  # -1表示水平和垂直都翻转
    
                    one_frame_time=time.time()-fps_time
                    real_fps=1.0/one_frame_time
                    fps_time=time.time()
                    
                    # 添加视频文件信息覆盖层
                    self.add_video_info_overlay(flipped_frame,real_fps=format(real_fps,".1f"))
                    
                    # 编码为JPEG
                    ret, buffer = cv2.imencode('.jpg', flipped_frame,[cv2.IMWRITE_JPEG_QUALITY, self.camera_settings['quality']])
                    if ret:
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                            b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    else:
                        print("帧编码失败")
                        # break
                    
            except Exception as e:
                print(Fore.RED+f"*"*10+"视频流生成错误: {e}")
                time.sleep(1)
                # yield from self.generate_test_frames()
                
    def generate_video_file_frames(self, video_path=None):
        """使用本地视频文件生成MJPEG流

        Args:
            video_path: 视频文件路径，默认为 'RovRemote/video/250812Dev.mp4'
        """
        if video_path is None:
            # 默认视频文件路径
            video_path = os.path.join(os.path.dirname(__file__), 'video', '250812Dev.mp4')

        print(Fore.CYAN + f"🎬 开始播放视频文件: {video_path}")

        # 检查视频文件是否存在
        if not os.path.exists(video_path):
            print(Fore.RED + f"❌ 视频文件不存在: {video_path}")
            # 如果文件不存在，回退到测试帧生成
            yield from self.generate_test_frames()
            return

        try:
            # 打开视频文件
            video_capture = cv2.VideoCapture(video_path)

            if not video_capture.isOpened():
                print(Fore.RED + f"❌ 无法打开视频文件: {video_path}")
                yield from self.generate_test_frames()
                return

            # 获取视频信息
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = video_capture.get(cv2.CAP_PROP_FPS)
            width = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))

            print(Fore.GREEN + f"✅ 视频文件信息: {width}x{height}, {fps:.1f}fps, {total_frames}帧")

            frame_count = 0
            loop_count = 0

            fps_time=time.time()
            while True:
                if frame_count<90:
                    ret, frame = video_capture.read()

                if not ret:
                    # 视频播放完毕，重新开始
                    loop_count += 1
                    print(Fore.YELLOW + f"🔄 视频循环播放 - 第{loop_count}次")
                    video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到开始
                    frame_count = 0
                    continue

                try:
                    # 调整视频帧大小到摄像头设置的分辨率
                    # target_width = self.camera_settings['width']
                    # target_height = self.camera_settings['height']

                    # if width != target_width or height != target_height:
                        # frame = cv2.resize(frame, (target_width, target_height))

                    if self.adjust_image_switcher and frame_count<90:
                        # 应用图像处理（对比度、饱和度、锐化）
                        frame = self.adjust_image_properties(frame)

                        # # # 应用锐化处理
                        sharpness = self.camera_settings.get('sharpness', 0.0)
                        if sharpness > 0.0:
                            sharpen_method = self.camera_settings.get('sharpen_method', 'standard')
                            frame = self.adaptive_sharpen_frame(frame, sharpness, sharpen_method)
                    
                        # frame=enhance_underwater_video(frame)
                        pass

                    one_frame_time=time.time()-fps_time
                    real_fps=1.0/one_frame_time
                    fps_time=time.time()
                    
                    if frame_count<90:
                        # 添加视频文件信息覆盖层
                        self.add_video_info_overlay(frame, frame_count, format(real_fps,".1f"), video_path)

                    # 编码为JPEG
                    ret_encode, buffer = cv2.imencode('.jpg', frame,
                                                    [cv2.IMWRITE_JPEG_QUALITY, 100])

                    if ret_encode:
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    else:
                        print(Fore.RED + "❌ 视频帧编码失败")
                        continue

                    frame_count += 1

                    # 控制播放帧率
                    target_fps = self.camera_settings.get('fps', 30)
                    # time.sleep(1.0 / target_fps)

                except Exception as e:
                    print(Fore.RED + f"❌ 视频帧处理错误: {e}")
                    continue

        except Exception as e:
            print(Fore.RED + f"❌ 视频文件播放错误: {e}")
            yield from self.generate_test_frames()
        finally:
            if 'video_capture' in locals():
                video_capture.release()
                print(Fore.YELLOW + "📹 视频文件已关闭")
    
    def generate_test_frames(self):
        """生成测试视频帧（当没有摄像头时）"""
        import numpy as np
        frame_count = 0
        while True:
            try:
                # 创建测试图像
                height, width = self.camera_settings['height'], self.camera_settings['width']
                frame = np.zeros((height, width, 3), dtype=np.uint8)

                # 添加一些动态内容
                color = (0, 100 + int(50 * np.sin(frame_count * 0.1)), 200)
                cv2.rectangle(frame, (50, 50), (width-50, height-50), color, 2)

                # 添加文字
                text = f"ROV Camera Test - Frame {frame_count}"
                cv2.putText(frame, text, (60, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                text2 = "No camera detected"
                cv2.putText(frame, text2, (60, 140), cv2.FONT_HERSHEY_SIMPLEX, 1, (100, 100, 255), 2)

                # 编码为JPEG
                ret, buffer = cv2.imencode('.jpg', frame)
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

                frame_count += 1
                time.sleep(1.0 / self.camera_settings['fps'])  # 控制帧率

            except Exception as e:
                print(f"测试帧生成错误: {e}")
                break

    def setup_serial(self):
        """初始化串口连接"""
        if SerialPort:
            try:
                self.serial_port = SerialPort(port=serial_port_str, baudrate=baudrate)

                reboot=True
                # if MAC_DEBUG:
                #     reboot=False
                    
                if self.serial_port.open(receive_callback=self.handle_serial_data,reboot=reboot):
                    print(Fore.CYAN+f"串口连接成功,串口接收回调已设置")
                else:
                    self.serial_port = None
                    print(Fore.RED+"串口连接失败")
                    
                    if not MAC_DEBUG:
                        while(1):pass
                    
            except Exception as e:
                print(f"串口连接失败: {e}")
                self.serial_port = None
                while(1):pass
                
        else:
            print("使用模拟数据模式")
            
    def send_init_data(self):
        self.servoCurrentKp=self.config_manager.get_setting("servo_settings","P")
        self.servoCurrentKi=self.config_manager.get_setting("servo_settings","I")
        self.servoCurrentKd=self.config_manager.get_setting("servo_settings","D")
        self.servo_target_current=self.config_manager.get_setting("servo_settings","servo_target_current")
         
        floats = [self.servoCurrentKp, self.servoCurrentKi,self.servoCurrentKd,self.servo_target_current]
        byte_arr = floats_to_bytearray(floats)
        self.serial_commond_list.append((0x19, byte_arr))

    def send_command(self, cmd, data=b''):
        #[ ] """发送命令到ESP32"""
        
        if not self.serial_port:
            return None

        try:
            # 构建命令帧
            frame = bytearray([0xAA, cmd, len(data)])
            frame.extend(data)
            crc = calculate_crc8(frame[1:])
            frame.append(crc)

            # 转换为bytes类型
            frame_bytes = bytes(frame)

            # 记录发送时间
            self.serial_send_time = time.time() * 1000  # 转换为毫秒

            # 发送命令
            success = self.serial_port.send_hex(frame_bytes)
            if success:
                # print(f"\n发送命令: {frame_bytes.hex().upper()}")
                pass
            else:
                print(Fore.RED+f"\n发送命令失败: {frame_bytes.hex().upper()}")
                self.serial_send_time = None  # 发送失败时清除计时
                self.sensor_data['serialResponseStatus'] = 'timeout'
                self.sensor_data['serialLatency'] = 150
                
                time.sleep(0.1)  # 等待10ms，减少延迟
                
                # TODO 增加发送失败重发机制，如果是串口断开，重新等待连接，直到连接成功

            # TODO 等待响应（简化版，实际应该有超时处理）
            

            return True
        except Exception as e:
            print(f"发送命令失败: {e}")
            return False

    def parse_response(self, data):
        """解析ESP32响应数据"""
        try:
            if len(data) < 4:
                return None

            # 检查帧头
            if data[0] != 0x55:
                return None

            status = data[1]
            data_len = data[2]

            if len(data) < 4 + data_len:
                return None

            payload = data[3:3+data_len]
            received_crc = data[3+data_len]

            # 验证CRC
            frame_data = data[1:3+data_len]
            calculated_crc = calculate_crc8(frame_data)

            if received_crc != calculated_crc:
                print("CRC校验失败")
                return None
            
            # self.has_other_commond=False

            return {'status': status, 'data': payload}

        except Exception as e:
            print(f"解析响应失败: {e}")
            return None
        
        

    def handle_serial_data(self, data: bytes):
        # [ ]! 1 接收ESP32数据，包含帧头对齐功能
        try:
            # [ ]TEST------------------------
            # print(Fore.GREEN+f"\n接收HEX: {data.hex().upper()}")
            # print(Fore.GREEN+f"\tTEXT: {str(data)}\n")
            
            if str(data).find("searching")!=-1:
                # print(Fore.RED+f"CRC错误:{data}")
                self.serial_response_dispaly_lasttime=time.time()
                self.sensor_data["esp32CommunicationError"] = 'ESP32_CRC_ERROR'   
            
            # if str(data).find("serialTimeOut")!=-1:
            #     self.has_other_commond=False
            
            if time.time()-self.serial_response_dispaly_lasttime>5:
                self.sensor_data["esp32CommunicationError"] = ''
                self.sensor_data["esp32CommunicationResponseText"]=''
                
            # 将新数据添加到缓冲区
            self.serial_buffer.extend(data)

            # 处理缓冲区中的所有完整帧
            while len(self.serial_buffer) > 0:
                # 查找帧头0x55
                frame_start = self._find_frame_header()
                if frame_start == -1:
                    # 没有找到帧头，清空缓冲区
                    # print(f"未找到帧头0x55，清空缓冲区: {self.serial_buffer.hex().upper()}")
                    # [ ]用于提示非协议的信息，TODO 封装成标准格式
                    # print(Fore.RED+f"无帧头:{data}")
                    
                    print(Fore.RED+f"{data}")
                    
                    self.serial_response_dispaly_lasttime=time.time()
                    
                    data=data.replace(b"\r",b"<br>").replace(b"\n",b"")
                    resposne_text=str(data)
                    resposne_text=resposne_text.replace("b'","")
                    
                    notin_response=False
                    
                    if str(resposne_text).find("LOG:")!=-1:
                        # if str(resposne_text).find("serialTimeOut")!=-1:
                        #     self.sensor_data["esp32CommunicationError"] = "手柄未连接/超时"
                        # else:
                        
                        resposne_text=resposne_text.replace("<br>",'')
                        # resposne_text=resposne_text.replace("LOG:",'')
                        resposne_text=resposne_text[resposne_text.find("LOG:"):].replace("LOG:",'')
                        
                        end_index=resposne_text.find("\n")
                        if end_index!=-1:
                            resposne_text=resposne_text[:end_index]
                        end_index=resposne_text.find("U")
                        if end_index!=-1:
                            resposne_text=resposne_text[:end_index]
                        
                        self.sensor_data["esp32CommunicationError"] =resposne_text
                            
                        notin_response=True
                    
                    if(len(self.sensor_data["esp32CommunicationResponseText"])>500):
                        self.sensor_data["esp32CommunicationResponseText"]=""
                    
                    if not notin_response:
                        self.sensor_data["esp32CommunicationResponseText"]="["+datetime.now().strftime("%m-%d %H:%M:%S")+"]"+resposne_text+self.sensor_data["esp32CommunicationResponseText"]
                    
                    self.serial_buffer.clear()
                    break

                # 如果帧头不在开始位置，移除之前的无效数据
                if frame_start > 0:
                    discarded_data = self.serial_buffer[:frame_start]
                    # print(Fore.YELLOW+f"丢弃无效数据: {discarded_data.hex().upper()}")
                    self.serial_buffer = self.serial_buffer[frame_start:]

                # 尝试解析完整帧
                frame_data = self._extract_complete_frame()
                if frame_data is None:
                    # 数据不完整，等待更多数据
                    break

                self.serial_receive_time=time.time()*1000
                
                # 处理完整的帧数据
                self._process_frame(frame_data)

        except Exception as e:
            print(f"处理串口数据失败: {e}")
            # 出错时清空缓冲区
            self.serial_buffer.clear()

    def _find_frame_header(self):
        """查找帧头0x55的位置"""
        try:
            return self.serial_buffer.index(0x55)
        except ValueError:
            return -1

    def _extract_complete_frame(self):
        """从缓冲区提取完整的帧数据"""
        try:
            if len(self.serial_buffer) < 4:
                return None  # 数据不足，无法确定帧长度

            # 检查帧头
            if self.serial_buffer[0] != 0x55:
                return None

            # 获取数据长度
            data_len = self.serial_buffer[2]
            frame_len = 4 + data_len  # 帧头(1) + 状态(1) + 长度(1) + 数据(data_len) + CRC(1)

            if len(self.serial_buffer) < frame_len:
                return None  # 数据不完整

            # 提取完整帧
            frame_data = bytes(self.serial_buffer[:frame_len])
            self.serial_buffer = self.serial_buffer[frame_len:]  # 移除已处理的数据

            return frame_data

        except Exception as e:
            print(f"提取帧数据失败: {e}")
            return None

    def _process_frame(self, frame_data: bytes):
        # [ ]!!! """处理单个完整的帧数据"""
        try:
            # print(Fore.GREEN+f"\t处理帧数据[Other:{len(self.serial_commond_list)}]: {frame_data.hex().upper()}")
            # print(f"\t--------: {frame_data}")
            
            # if len(frame_data)>8:
                # print(Fore.GREEN+f"\t处理帧数据[Other:{len(self.serial_commond_list)}]: {frame_data.hex().upper()}")
                

            # 计算串口延迟和响应状态
            if self.serial_send_time:
                current_time = time.time() * 1000  # 转换为毫秒
                latency = max(0, int(current_time - self.serial_send_time))  # 确保延迟不为负数
                self.sensor_data['serialLatency'] = latency

                # 根据延迟设置响应状态
                if latency < 200:
                    self.sensor_data['serialResponseStatus'] = 'success'
                elif latency < 250:
                    self.sensor_data['serialResponseStatus'] = 'timeout'
                else:
                    self.sensor_data['serialResponseStatus'] = 'error'

                # TODO !!!!!!增加平均串口时间，丢包率
                # print(Fore.YELLOW+f"串口延迟: {latency}ms, 状态: {self.sensor_data['serialResponseStatus']}")
                self.serial_send_time = None  # 重置计时
            # else:
            #     # 没有发送时间记录，标记为未连接状态
            #     self.sensor_data['serialResponseStatus'] = 'disconnected'

            # 解析协议响应
            response = self.parse_response(frame_data)
            if response and response['status'] == 0x00:
                payload = response['data']

                # 根据数据长度判断响应类型
                if len(payload) == 2:  # 初始化
                    if payload[0:2] == b'\x01\xff':
                        self.esp32_init=True
                        
                        led_data = bytes([255, 255, 255, 128])  # R, G, B, 亮度
                        self.serial_commond_list.append((0x11, led_data))
                        # time.sleep(0.2)
                        # self.serial_commond_list.append((0x11, led_data))
                        # time.sleep(0.2)
                        # self.serial_commond_list.append((0x11, led_data))
                        # time.sleep(0.2)
                        # self.serial_commond_list.append((0x11, led_data))
                        
                        print(Fore.CYAN+"\n\n------------------------ESP32 ready--------------------------\n\n")

                elif len(payload) == 35+6+1+4:  
                    # print(Fore.YELLOW+str(payload.hex().upper()))
                    
                    #[ ]!!! 所有传感器数据（温度+压力+深度+MPU6050姿态+罗盘航向+ADC电压+水密封性能+入水状态+操作模式:普通0，定深1）
                    # 解析MS5837传感器数据（温度、压力、深度）
                    temperature = struct.unpack('<f', payload[0:4])[0]
                    pressure = struct.unpack('<f', payload[4:8])[0]
                    # kPa转为hPa 千帕转百帕
                    pressure=pressure*10
                    depth = struct.unpack('<f', payload[8:12])[0]
                    
                    # 解析MPU6050姿态数据（完整的三轴姿态）
                    mpu_roll = struct.unpack('<f', payload[12:16])[0]   # MPU6050 Roll（横滚）
                    mpu_pitch = struct.unpack('<f', payload[16:20])[0]  # MPU6050 Pitch（俯仰）
                    mpu_yaw = struct.unpack('<f', payload[20:24])[0]    # MPU6050 Yaw（偏航）

                    # 解析HMC5883罗盘航向数据（独立的航向角）
                    compass_heading = struct.unpack('<f', payload[24:28])[0]  # HMC5883罗盘航向
                    
                    if math.isnan(temperature):
                        temperature=0
                    if math.isnan(pressure) or pressure==0:
                        pressure = 1013.250
                    if math.isnan(depth):
                        depth=0
                    if math.isnan(mpu_roll):
                        mpu_roll=0
                    if math.isnan(mpu_pitch):
                        mpu_pitch=0
                    if math.isnan(compass_heading):
                        compass_heading=0
                        
                    # 确保航向在0-360度范围内
                    if compass_heading < 0:
                        compass_heading += 360
                        
                    # 解析ADC电压数据
                    adc_voltage = struct.unpack('<f', payload[28:32])[0]  # ADC电压值 (float)

                    # 解析水密封性能数据
                    water_seal_value = payload[32]  # 水密封性能状态 (1字节uint8_t)
                    if(water_seal_value<10):
                        water_seal_status=2
                    elif water_seal_value<30:
                        water_seal_status=1
                    else:
                        water_seal_status=0

                    # 将水密封状态转换为可读文本
                    water_seal_text = {
                        0: "密封良好",
                        1: "轻微漏水",
                        2: "严重漏水"
                    }.get(water_seal_status, "未知状态")
                    
                    # 解析入水状态数据
                    water_detection_value = payload[33]  # 入水状态 (1字节uint8_t)
                    # 入水状态判断：当触摸值小于10时，认为入水，深度归零
                    
                    # 水上47，52
                    if water_detection_value <= 20:
                        self.is_underwater = True
                    else:
                        self.is_underwater = False
                    
                    depth=0
                   
                    # 如果未入水，深度归零并开始计算入水深度
                    if self.is_underwater==False:
                         # 压力校准
                        offset=-19
                        # print(Fore.RED+"P:"+str(self.is_underwater))
                        
                        # 记录水面压力作为基准
                        self.surface_pressure = pressure
                        
                        # 标准大气
                        atmospheric_pressure = 101325.0
                        
                        # 未入水时显示海拔高度
                        depth=altitude_at_pressure((pressure+offset)*100,atmospheric_pressure)
                        # print(Fore.RED+f"气压:{pressure},{offset},{depth}")
                        
                    else:
                        # print(Fore.YELLOW+"P:"+str(self.is_underwater))
                        
                        # 已入水，使用基准压力计算深度
                        # 计算方式1 使用水面压力作为基准计算深度
                        # depth = (pressure - self.surface_pressure) * 0.01  # 1hPa ≈ 1cm水深
                        
                        # 计算方式2
                        # 标准大气
                        # atmospheric_pressure = 101325.0;
                        rho = 1000.0
                        # 重力加速度(m/s²)
                        g = 9.80665 
                        # 减atmospheric_pressure标准气压作为基准压力，用于计算海平面以下
                        # depth= (pressure*100  - atmospheric_pressure) / (rho * g)
                        # print(pressure,self.surface_pressure,depth)
                        
                        # 计算方式3
                        #self.surface_pressure入水时气压作为基准（标准）压力，用于计算入当前水深度，不考虑海平面的标准气压
                        depth= (pressure*100-self.surface_pressure*100) / (rho * g)
                        # print(pressure,self.surface_pressure,depth)
                     

                    # 解析ADC电压数据
                    depth_control_enabled = payload[34]
                    
                    # print(f"\t📊ADC:{adc_voltage:.3f}V 气压:{pressure:.3f}hpa 深度:{depth:.3f}m 温度:{temperature:.2f}°C 罗盘:{int(compass_heading)}° " \
                    #        f"水密:{water_seal_value} 入水:{self.is_underwater}({water_detection_value}) Roll={mpu_roll:.1f}° Pitch:{mpu_pitch:.1f}° Yaw:{mpu_yaw:.1f}°")
                    
                    
                    motor_currents=[payload[35],payload[36],payload[37],payload[38],payload[39],payload[40]]
                    # print(Fore.YELLOW+"motor_currents:"+str(motor_currents))
                    
                    water_seal_bottom_value=payload[41]
                    
                    servo_current= struct.unpack('<f', payload[42:46])[0]   #舵机电流
                    
                    # if self.servo_target_current is not None:
                    #     if servo_current>self.servo_target_current:
                    #         print(f"servoCurrent:{servo_current:.2f}")
                        
                    # 更新所有传感器数据
                    self.sensor_data.update({
                        'temperature': temperature,
                        'pressure': pressure,
                        'depth': depth,
                        'roll': mpu_roll,                        # MPU6050横滚角度
                        'pitch': mpu_pitch,                      # MPU6050俯仰角度
                        'yaw': mpu_yaw,                          # MPU6050偏航角度
                        'rovHeading': int(compass_heading),      # HMC5883罗盘航向（0-360度）
                        'compassHeading': compass_heading,       # 原始罗盘航向数据
                        'adcVoltage': adc_voltage,               # ADC电压值 (V)
                        'waterSealValue': water_seal_value,    # 水密封性原始值
                        'waterSealBottomValue': water_seal_bottom_value,    # 水密封性原始值
                        
                        'waterSealStatus': water_seal_status,    # 水密封性能状态 (0-3)
                        'waterSealText': water_seal_text,        # 水密封性能文本描述
                        'waterDetectionValue': water_detection_value,  # 入水检测原始值
                        'isUnderwater': self.is_underwater,           # 是否入水状态
                        'mode':depth_control_enabled,
                        'motorCurrents': motor_currents,
                        # 兼容性字段
                        'angleX': mpu_roll,
                        'angleY': mpu_pitch,
                        'angleZ': mpu_yaw,
                        'magnetometerX': mpu_roll,
                        'magnetometerY': mpu_pitch,
                        'magnetometerZ': compass_heading,
                    })
                    
                    # print(Fore.CYAN+"\n"+str(self.sensor_data))
                    
                     # 立即广播更新的数据到所有客户端
                    # self.immediate_broadcast()
                elif len(payload) == 17:  # 舵机 0x1A
                    # print(Fore.GREEN+f"\t处理帧数据: {frame_data.hex().upper()}")
                    
                    serveAngle= payload[0]
                    self.servo_target_current = round(struct.unpack('<f', payload[1:5])[0],2)
                    self.servoCurrentKp = round(struct.unpack('<f', payload[5:9])[0],2)
                    self.servoCurrentKi = round(struct.unpack('<f', payload[9:13])[0],2)
                    self.servoCurrentKd = round(struct.unpack('<f', payload[13:17])[0],2)
                    
                    menu_name=self.get_menu_item_name(MENU_SERVO_PID,self.menu_item_index[1])
                    
                    print(Fore.CYAN+f"{menu_name} angle={serveAngle:.2f},P:{self.servoCurrentKp},I:{self.servoCurrentKi},D:{self.servoCurrentKd},TC:{self.servo_target_current:.2f}")
                    
                elif len(payload) == 16:  # 磁力计数据
                    mag_x = struct.unpack('<f', payload[0:4])[0]
                    mag_y = struct.unpack('<f', payload[4:8])[0]
                    mag_z = struct.unpack('<f', payload[8:12])[0]
                    heading = struct.unpack('<f', payload[12:16])[0]

                    # 确保航向在0-360度范围内
                    if heading < 0:
                        heading += 360

                    self.sensor_data.update({
                        'magnetometerX': mag_x,
                        'magnetometerY': mag_y,
                        'magnetometerZ': mag_z,
                        'rovHeading': int(heading)
                    })

                    print(f"🧭 磁力计数据 - X: {mag_x:.2f}, Y: {mag_y:.2f}, Z: {mag_z:.2f}, 航向: {heading:.1f}°")

               
            else:
                print(f"协议解析失败或状态错误: {response}")

        except Exception as e:
            print(f"处理帧数据失败: {e}")
            
    def request_all_sensor_data(self):
        """请求所有传感器数据 - 统一协议，包含温度、压力、深度、磁力计等所有数据"""
        self.send_command(0x01)  # 统一使用CMD_GET_SENSOR_DATA获取所有传感器数据
        
    def immediate_broadcast(self):
        """立即广播传感器数据（强制立即执行）"""
        # print(self.clients)
        if self.clients:
            try:
                # 使用线程池确保立即执行广播
                import threading
                def force_broadcast():
                    try:
                        # 创建新的事件循环来立即执行广播
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        # 立即执行广播
                        loop.run_until_complete(self.broadcast_sensor_data())
                        loop.close()

                        # print(f"\t✅ 立即广播完成，客户端数量: {len(self.clients)}")
                    except Exception as e:
                        print(f"❌ 立即广播失败: {e}")

                # 在新线程中立即执行广播
                thread = threading.Thread(target=force_broadcast, daemon=True)
                thread.start()

                print(f"\t🚀 启动立即广播线程，客户端数量: {len(self.clients)}")
            except Exception as e:
                print(f"❌ 启动立即广播失败: {e}")



    async def register_client(self, websocket):
        """注册新的WebSocket客户端"""
        self.clients.add(websocket)
        print(Fore.CYAN+f"客户端已连接，当前连接数: {len(self.clients)}"+"-" * 50)
        
        # print(Fore.CYAN+f"总连接数: {len(self.clients)}")
        for i, client in enumerate(self.clients, 1):
            print(f"\n客户端 #{i}:")
            print(f"ID: {client.id}")
            print(f"远程地址: {client.remote_address}，本地地址: {client.local_address}")
            print(f"协议: {client.subprotocol}")
            # print(f"打开状态: {client.open}")
            # print(f"关闭状态: {client.closed}")
        print("-" * 50)
        
        # 发送当前传感器数据
        # await self.send_sensor_data(websocket)

    async def unregister_client(self, websocket):
        """注销WebSocket客户端"""
        self.clients.discard(websocket)
        print(Fore.YELLOW+f"客户端已断开，当前连接数: {len(self.clients)}")

    async def send_sensor_data(self, websocket,message_start_timestamp=0):
        # print("sensor_data",self.sensor_data)
        """发送传感器数据到指定客户端"""
        message = {
            'type': 'sensor_data',
            'data': self.sensor_data,
            'startTimestamp':message_start_timestamp,
            'timestamp': time.time()
        }
        await self.safe_send(websocket,message)
        
    async def broadcast_sensor_data(self):
        # [ ]"""广播传感器数据到所有客户端"""
        if self.clients:
            try:
                # 确保sensor_data中的所有值都可以JSON序列化
                clean_sensor_data = {}
                for key, value in self.sensor_data.items():
                    try:
                        if value is None:
                            clean_sensor_data[key] = None
                        elif isinstance(value, bool):
                            clean_sensor_data[key] = value
                        elif isinstance(value, int):
                            # 检查是否为有效整数
                            if -2147483648 <= value <= 2147483647:
                                clean_sensor_data[key] = value
                            else:
                                clean_sensor_data[key] = str(value)
                        elif isinstance(value, float):
                            # 检查是否为有效浮点数
                            import math
                            if math.isfinite(value):
                                clean_sensor_data[key] = round(value, 6)  # 限制精度
                            else:
                                clean_sensor_data[key] = 0.0
                        elif isinstance(value, str):
                            # 确保字符串不包含控制字符
                            clean_value = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
                            clean_sensor_data[key] = clean_value
                        else:
                            # 尝试转换为基本类型
                            try:
                                if hasattr(value, '__float__'):
                                    float_val = float(value)
                                    if math.isfinite(float_val):
                                        clean_sensor_data[key] = round(float_val, 6)
                                    else:
                                        clean_sensor_data[key] = 0.0
                                elif hasattr(value, '__int__'):
                                    clean_sensor_data[key] = int(value)
                                else:
                                    clean_sensor_data[key] = str(value)
                            except:
                                clean_sensor_data[key] = str(value)
                    except Exception as e:
                        print(f"清理数据键 '{key}' 失败: {e}, 值: {value}")
                        clean_sensor_data[key] = None

                message = {
                    'type': 'sensor_data',
                    'data': clean_sensor_data,
                    'timestamp': time.time()
                }

                # 调试：打印消息内容（限制长度）
                if len(message) > 200:
                    print(f"发送消息（前200字符）: {message[:200]}...")
                else:
                    # print(f"\t发送消息: {message}")
                    pass
                
                print(f"📡 广播传感器数据到 {len(self.clients)} 个客户端", str(message))
                # print(f"\t深度: {clean_sensor_data.get('depth', 'N/A')}m, 温度: {clean_sensor_data.get('temperature', 'N/A')}°C")
                # print(f"\t发送消息: {str(message)}")
                
                # 测试JSON序列化
                try:
                    json_str = json.dumps(message, ensure_ascii=False, separators=(',', ':'))
                    # 验证JSON字符串的有效性
                    json.loads(json_str)
                except (TypeError, ValueError) as e:
                    print(f"JSON序列化失败: {e}")
                    print(f"问题数据: {message}")
                    # 使用备用的安全数据
                    safe_message = {
                        'type': 'sensor_data',
                        'data': {
                            'depth': 0.0,
                            'temperature': 25.0,
                            'rovHeading': 0,
                            'error': 'data_serialization_failed'
                        },
                        'timestamp': time.time()
                    }
                    json_str = json.dumps(safe_message)

                # 创建发送任务列表
                tasks = []
                for client in self.clients.copy():
                    tasks.append(self.safe_send(client, json_str))
            except Exception as e:
                print(f"广播数据序列化错误: {e}")
                print(f"sensor_data内容: {self.sensor_data}")
                return
            
            # 并发发送
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

    async def safe_send(self, websocket, message):
        """安全发送消息，处理连接异常"""
        try:
            # 确保消息是字符串格式
            if isinstance(message, dict):
                try:
                    message = json.dumps(message, ensure_ascii=False, separators=(',', ':'))
                    # 验证生成的JSON
                    json.loads(message)
                except (TypeError, ValueError) as e:
                    print(f"safe_send JSON序列化失败: {e}")
                    message = json.dumps({'type': 'error', 'message': 'serialization_failed'})
            elif not isinstance(message, str):
                message = str(message)

            # 验证消息长度
            if len(message) > 10000:  # 限制消息大小
                print(f"消息过大，截断: {len(message)} 字符")
                message = message[:10000]

            # 确保WebSocket连接状态正常
            try:
                if hasattr(websocket, 'closed') and websocket.closed:
                    print("WebSocket连接已关闭，跳过发送")
                    self.clients.discard(websocket)
                    return
            except Exception as e:
                print(f"检查WebSocket状态失败: {e}")
                self.clients.discard(websocket)
                return
            
            # print(f"\t发送消息safe_send：",message)

            # 发送完整消息
            await websocket.send(message)

        except websockets.exceptions.ConnectionClosed:
            print("WebSocket连接在发送时关闭")
            self.clients.discard(websocket)
        except Exception as e:
            print(f"发送消息失败: {e}")
            print(f"消息长度: {len(message) if isinstance(message, str) else 'unknown'}")
            print(f"消息内容: {message[:100] if len(str(message)) > 100 else message}")
            self.clients.discard(websocket)

    # [ ] SORCKET收到命令
    async def handle_sorcket_command(self, websocket, command_data):
        # [ ]"""处理来自web客户端的命令"""
        command = command_data.get('command')
        data = command_data.get('data', {})
        time_now=time.time()
        timestamp=command_data.get('timestamp')
        # print(f"\n---SORCKET收到命令: {command}, 数据: {data}")
        
        try:
            if command == 'ping':
                # 心跳响应
                await websocket.send(json.dumps({
                    'type': 'pong',
                    'timestamp': time.time(),
                    'start_timestamp': timestamp
                }))
            
            elif command == 'joystick_control':
                # 手柄数据
                # print(Fore.YELLOW+f"收到手柄数据({(time_now-timestamp):.3f}): {data}")
                
                #  message = {
                # 'type': 'sensor_data',
                # 'data': self.sensor_data,
                # 'timestamp': time.time()
                # }
                # self.safe_send(websocket,message)
                
                if time_now-self.timeout_log_timestamp>1:
                    self.timeout_not_count+=1
                    self.timeout_log_timestamp=time_now
                    # print(Fore.WHITE+f"手柄数据:[{self.timeout_not_count}秒]\t延迟:{(time_now-timestamp):.3f}\t超时次数:{self.timeout_count}")
                
                if time_now-timestamp>0.5:
                    # self.timeout_not_count=0
                    self.timeout_count+=1
                    
                    if self.timeout_start_timestamp==0:
                        self.timeout_start_timestamp=timestamp
                    print(Fore.RED+f"手柄数据超时: {(time_now-timestamp):.3f}")
                        
                    # TODO 需要对齐receive_time，第一次接收数据时
                    return
                else:
                    if self.timeout_start_timestamp!=0:
                        print(Fore.YELLOW+f"手柄数据恢复: {(time_now-self.timeout_start_timestamp):.3f}")
                        
                        self.timeout_start_timestamp=0
                    
                if data.get("XBOX")==2:
                    if self.serial_port:
                        led_data = bytes([255, 255, 255, 128])  # R, G, B, 亮度
                        self.serial_commond_list.append((0x11, led_data))

                if data.get("UP") == 1:
                    print(f"UP DOWN"+"-"*30)
                    # if self.serial_port:
                        # self.serial_commond_list.append((0x33, [0x01]))
                    
                    # 调整饱和度
                    # saturation=self.camera_settings['saturation']
                    # saturation+=0.1
                    # self.set_image_properties(saturation=saturation,save_config=True)
                    # saturation=self.camera_settings['saturation']
                    # self.serial_response_dispaly_lasttime=time.time()
                    # self.sensor_data["esp32CommunicationError"] ="LOG:saturation:"+format(saturation,".1f")
                    
                    if self.menu_item_index[0]==MENU_SERVO_PID:
                        if self.menu_item_index[1]>0:
                            self.menu_item_index[1]-=1
                    
                    menu_name=self.get_menu_item_name(MENU_SERVO_PID,self.menu_item_index[1])
                    print(Fore.CYAN+f"{menu_name}")
                    
                if data.get("DOWN") == 1:
                    print(f"DOWN DOWN"+"-"*30)
                    
                    if self.menu_item_index[0]==MENU_SERVO_PID:
                        if self.menu_item_index[1]<len(self.menu_items[MENU_SERVO_PID][1])-1:
                            self.menu_item_index[1]+=1
                        
                    menu_name=self.get_menu_item_name(MENU_SERVO_PID,self.menu_item_index[1])
                    print(Fore.CYAN+f"{menu_name}")
                     
                    # if self.serial_port:
                        # self.serial_commond_list.append((0x32, [0x01]))
                    
                    # 调整饱和度
                    # saturation=self.camera_settings['saturation']
                    # saturation-=0.1
                    # self.set_image_properties(saturation=saturation,save_config=True)
                    # saturation=self.camera_settings['saturation']
                    # self.serial_response_dispaly_lasttime=time.time()
                    # self.sensor_data["esp32CommunicationError"] ="LOG:saturation:"+format(saturation,".1f")
                    
                if data.get("LEFT") == 1:
                    print(f"LEFT")
                    
                    # contrast=self.camera_settings['contrast']
                    # contrast-=0.1
                    # self.set_image_properties(contrast=contrast,save_config=True)
                    # contrast=self.camera_settings['contrast']
                    # self.serial_response_dispaly_lasttime=time.time()
                    # self.sensor_data["esp32CommunicationError"] ="LOG:contrast:"+format(contrast,".1f")
                    
                    # sharpness=self.camera_settings.get('sharpness', 0.0)
                    # sharpness-=0.1
                    # self.set_image_properties(sharpness=sharpness,save_config=True)
                    # sharpness=self.camera_settings.get('sharpness', 0.0)
                    # self.sensor_data["esp32CommunicationError"] ="LOG:sharpness:"+format(sharpness,".1f")   
                    
                    if self.serial_port:
                    # 舵机PID
                        if self.menu_item_index[0]==MENU_SERVO_PID:
                            
                            if self.servoCurrentKp is None:
                                self.serial_commond_list.append((0x1A, [0x00]))
                            else:
                                if self.menu_item_index[1]==0:
                                    self.servoCurrentKp-=1
                                if self.menu_item_index[1]==1:
                                    self.servoCurrentKi-=0.1
                                if self.menu_item_index[1]==2:
                                    self.servoCurrentKd-=0.1
                                if self.menu_item_index[1]==3:
                                    self.servo_target_current-=0.1
                                
                                floats = [round(self.servoCurrentKp,2), round(self.servoCurrentKi,2),round(self.servoCurrentKd,2),round(self.servo_target_current,2)]
                                byte_arr = floats_to_bytearray(floats)
                                self.serial_commond_list.append((0x19, byte_arr))
                                
                                self.config_manager.set_servo_settings(self.servoCurrentKp, self.servoCurrentKi,self.servoCurrentKd,self.servo_target_current)

                if data.get("RIGHT") == 1:
                    print(f"RIGHT")
                    
                    # contrast=self.camera_settings['contrast']
                    # contrast+=0.1
                    # self.set_image_properties(contrast=contrast,save_config=True)
                    # contrast=self.camera_settings['contrast']
                    # self.serial_response_dispaly_lasttime=time.time()
                    # self.sensor_data["esp32CommunicationError"] ="LOG:contrast:"+format(contrast,".1f")
                    
                    # sharpness=self.camera_settings.get('sharpness', 0.0)
                    # sharpness+=0.1
                    # self.set_image_properties(sharpness=sharpness,save_config=True)
                    # sharpness=self.camera_settings.get('sharpness', 0.0)
                    # self.sensor_data["esp32CommunicationError"] ="LOG:sharpness:"+format(sharpness,".1f")   
                    
                    if self.serial_port:
                    # 舵机PID
                        if self.menu_item_index[0]==MENU_SERVO_PID:
                            if self.servoCurrentKp is None:
                                self.serial_commond_list.append((0x1A, [0x00]))
                            else:
                                if self.menu_item_index[1]==0:
                                    self.servoCurrentKp+=1
                                if self.menu_item_index[1]==1:
                                    self.servoCurrentKi+=0.1
                                if self.menu_item_index[1]==2:
                                    self.servoCurrentKd+=0.1
                                if self.menu_item_index[1]==3:
                                    self.servo_target_current+=0.1
                                    
                                floats = [round(self.servoCurrentKp,2), round(self.servoCurrentKi,2),round(self.servoCurrentKd,2),round(self.servo_target_current,2)]
                                byte_arr = floats_to_bytearray(floats)
                                
                                self.serial_commond_list.append((0x19, byte_arr))
                                
                                self.config_manager.set_servo_settings(self.servoCurrentKp, self.servoCurrentKi,self.servoCurrentKd,self.servo_target_current)
                
                # A键 - 定深控制
                if data.get("A") == 1:
                    print(f"🌊 A键UP - 定深控制")
                    if self.serial_port:
                        self.serial_commond_list.append((0x14, [0x01]))
                        
                # X键 - 锁定航向控制
                if data.get("X")==1:
                    print(f"X键按下 - 锁定航向控制")
                    if self.serial_port:
                        self.serial_commond_list.append((0x15, [0x01]))
                
                #  前进时保持平衡开启
                if data.get("B")==1:
                    # print(f"🧭 B键按下")
                    
                    # 前进平衡
                    # if self.serial_port:
                    #     self.serial_commond_list.append((0x18, [0x01]))
                    
                    # 夹子
                    if self.serial_port:
                        self.serial_commond_list.append((0x34, [0x01]))
                
                if data.get("Y")==2:
                    print(f"Y键UP")
                    self.adjust_image_switcher=not self.adjust_image_switcher
                        
                # if data.get("Y")==1: 
                #     print(f"Y键DOWN")
                #     if self.serial_port:
                #         self.serial_commond_list.append((0x31, [0x00]))
                        
                # Y键 - 切换摄像头（保留原功能）
                # if data.get("Y")==2:
                #     print(f"Y键UP")
                #     if self.serial_port:
                        # self.send_command(0x12, [1])
                        # self.serial_commond_list.append((0x12, [0x01]))
                
                # print(data.get("LB"))
                # LB键 - 切换到摄像头0
                if data.get("LB") == 1:
                    c_index=find_camera_index("USB_Camera")
                    print(f"LB键按下 - 切换到 前:{c_index}")
                    
                    # self.switch_camera(find_camera_index("icspring_camera"),1280,720)
                    
                    if self.icspring_camera_size_index==1:
                        self.icspring_camera_size_index=0
                        self.switch_camera(find_camera_index("icspring_camera"),1280,720)
                    else:
                        self.icspring_camera_size_index=1
                        self.switch_camera(find_camera_index("icspring_camera"),640,480,30)
                        
                # # RB键 - 切换到摄像头2
                if data.get("RB") == 1:
                    # self.switch_camera(find_camera_index("USB_Camera"),160,480)
                    # self.switch_camera(find_camera_index("HD_Camera"),1280,720)
                    
                    c_index=find_camera_index("USB_Camera")
                    c_index=find_camera_index("HD_Camera")
                    
                    print(f"RB键按下 - 切换到 下:{c_index}")
                    
                    # self.switch_camera(c_index,640,480)
                    
                    if  self.USB_Camera_size_index==0:
                        self.USB_Camera_size_index=1
                        self.switch_camera(c_index,640,480)
                    elif self.USB_Camera_size_index==1:
                        self.USB_Camera_size_index=0
                        self.switch_camera(c_index,1280,720)
                        
                # 重置水面气压
                if data.get("VIEW")==1:
                    self.surface_pressure=self.sensor_data["pressure"]
                    self.sensor_data["esp32CommunicationError"] ="重置海平面"
                    
                    
                # 发送摇杆数据
                # 限制范围
                left_x_int = max(-100, min(100, data.get("lX", 0)))
                left_y_int = max(-100, min(100, data.get("lY", 0)))
                right_x_int = max(-100, min(100, data.get("rX", 0)))
                right_y_int = max(-100, min(100, data.get("rY", 0)))
                
                def int_to_signed_byte(value):
                    """将-100到+100的整数转换为有符号字节"""
                    if value >= 0:
                        return value & 0xFF
                    else:
                        return (256 + value) & 0xFF
                
                frame = bytearray([])
                frame.append(int_to_signed_byte(left_x_int))
                frame.append(int_to_signed_byte(left_y_int))
                frame.append(int_to_signed_byte(right_x_int))
                frame.append(int_to_signed_byte(right_y_int))
                
                # 发送手柄数据给下位机
                if self.serial_port:
                    if len(self.serial_commond_list)==0:
                        # 记录发送时间
                        #  TODO 增加发送超时判断，并显示在WEB
                            
                        self.send_command(0x30, frame)
                    else:
                        # if len(self.serial_commond_list[0][1])==1:
                        #     print(Fore.YELLOW+f"有其他命令，跳过发送摇杆数据:0x{format(self.serial_commond_list[0][0],'02x')} {list(self.serial_commond_list[0][1])}")
                        # else:
                        #     print(Fore.YELLOW+f"有其他命令，跳过发送摇杆数据:0x{format(self.serial_commond_list[0][0],'02x')} {self.serial_commond_list[0][1].hex(' ')}")
                            
                        self.send_command(self.serial_commond_list[0][0], self.serial_commond_list[0][1])
                        
                        self.serial_commond_list.pop(0)
                        
                
            elif command == 'get_sensor_data':
                # 移除主动请求ESP32数据的功能，现在server会自动推送数据
                # 只发送当前缓存的传感器数据
                await self.send_sensor_data(websocket,timestamp)
                
            elif command == 'light_toggle':
                # 灯光开关控制 - 使用新协议 ``
                if self.serial_port:
                    # 切换LED状态 (白色，中等亮度)
                    led_data = bytes([255, 255, 255, 128])  # R, G, B, 亮度
                    self.send_command(0x11, led_data)

            elif command == 'light_brightness':
                # 灯光亮度控制 - 使用新协议
                brightness = int(data.get('value', 50))
                if self.serial_port:
                    # 设置LED亮度 (保持白色)
                    brightness_scaled = int(brightness * 255 / 100)
                    led_data = bytes([255, 255, 255, brightness_scaled])
                    self.send_command(0x11, led_data)
                    
            elif command == 'camera_control':
                # 相机控制
                direction = data.get('direction', 'center')
                direction_map = {
                    'up': '03',
                    'down': '04', 
                    'left': '05',
                    'right': '06',
                    'center': '07'
                }
                if direction in direction_map and self.serial_port:
                    self.serial_port.send_hex(direction_map[direction])
                    
            elif command == 'calibrate_heading':
                # 航向校准
                if self.serial_port:
                    self.serial_port.send_hex("08")  # 校准命令

            elif command == 'calibrate_adc':
                # ADC校准
                reference_voltage = float(data.get('voltage', 0))
                if reference_voltage > 0 and self.serial_port:
                    # 发送ADC校准命令 (0x21)
                    voltage_bytes = struct.pack('<f', reference_voltage)
                    self.send_command(0x21, voltage_bytes)
                    print(f"发送ADC校准命令，参考电压: {reference_voltage:.3f}V")

            elif command == 'depth_offset':
                # 深度偏移设置
                offset = float(data.get('value', 0))
                # 这里可以保存偏移值或发送到ESP32
                print(f"设置深度偏移: {offset}m")
                
            elif command == 'video_quality':
                # 视频质量设置
                quality = data.get('quality', 'medium')
                quality_map = {
                    'low': {'width': 480, 'height': 360, 'quality': 60},
                    'medium': {'width': 640, 'height': 480, 'quality': 85},
                    'high': {'width': 1280, 'height': 720, 'quality': 95}
                }
                if quality in quality_map:
                    self.camera_settings.update(quality_map[quality])
                    self.restart_camera()
                print(f"设置视频质量: {quality}")

            elif command == 'frame_rate':
                # 帧率设置
                rate = int(data.get('rate', 30))
                self.camera_settings['fps'] = rate
                if self.camera and self.camera.isOpened():
                    self.camera.set(cv2.CAP_PROP_FPS, rate)
                print(f"设置帧率: {rate}fps")
                
            elif command == 'start_recording':
                # 开始录制
                self.is_recording = True
                print("开始录制视频")
                
            elif command == 'stop_recording':
                # 停止录制
                self.is_recording = False
                print("停止录制视频")

            elif command == 'switch_to_video_file':
                # 切换到视频文件模式
                video_path = data.get('video_path')  # 可选的自定义视频路径
                self.switch_to_video_mode(video_path)
                await websocket.send(json.dumps({
                    'type': 'response',
                    'command': 'switch_to_video_file',
                    'success': True,
                    'message': f'已切换到视频文件模式: {self.video_file_path}',
                    'timestamp': timestamp
                }))

            elif command == 'switch_to_camera':
                # 切换回摄像头模式
                self.switch_to_camera_mode()
                await websocket.send(json.dumps({
                    'type': 'response',
                    'command': 'switch_to_camera',
                    'success': True,
                    'message': '已切换回摄像头模式',
                    'timestamp': timestamp
                }))

            elif command == 'get_video_mode_status':
                # 获取当前视频模式状态
                await websocket.send(json.dumps({
                    'type': 'video_mode_status',
                    'video_mode': self.video_mode,
                    'video_file_path': self.video_file_path,
                    'timestamp': timestamp
                }))

            elif command == 'save_config':
                # 保存当前配置
                success = self.config_manager.save_config()
                await websocket.send(json.dumps({
                    'type': 'response',
                    'command': 'save_config',
                    'success': success,
                    'message': '配置保存成功' if success else '配置保存失败',
                    'timestamp': timestamp
                }))

            elif command == 'load_config':
                # 重新加载配置
                try:
                    self.config_manager.load_config()
                    self.camera_settings = self.config_manager.get_camera_settings()
                    await websocket.send(json.dumps({
                        'type': 'response',
                        'command': 'load_config',
                        'success': True,
                        'message': '配置重新加载成功',
                        'camera_settings': self.camera_settings,
                        'timestamp': timestamp
                    }))
                except Exception as e:
                    await websocket.send(json.dumps({
                        'type': 'response',
                        'command': 'load_config',
                        'success': False,
                        'message': f'配置加载失败: {e}',
                        'timestamp': timestamp
                    }))

            elif command == 'reset_config':
                # 重置配置到默认值
                section = data.get('section')  # 可选，指定要重置的配置节
                try:
                    self.config_manager.reset_to_default(section)
                    self.camera_settings = self.config_manager.get_camera_settings()
                    await websocket.send(json.dumps({
                        'type': 'response',
                        'command': 'reset_config',
                        'success': True,
                        'message': f'配置重置成功: {section or "全部"}',
                        'camera_settings': self.camera_settings,
                        'timestamp': timestamp
                    }))
                except Exception as e:
                    await websocket.send(json.dumps({
                        'type': 'response',
                        'command': 'reset_config',
                        'success': False,
                        'message': f'配置重置失败: {e}',
                        'timestamp': timestamp
                    }))

            elif command == 'get_config_info':
                # 获取配置文件信息
                config_info = self.config_manager.get_config_info()
                validation = self.config_manager.validate_config()
                await websocket.send(json.dumps({
                    'type': 'config_info',
                    'config_info': config_info,
                    'validation': validation,
                    'camera_settings': self.camera_settings,
                    'timestamp': timestamp
                }))

            elif command == 'update_camera_settings':
                # 更新摄像头设置
                try:
                    settings = data.get('settings', {})
                    self.set_image_properties(
                        contrast=settings.get('contrast'),
                        saturation=settings.get('saturation'),
                        sharpness=settings.get('sharpness'),
                        sharpen_method=settings.get('sharpen_method'),
                        save_config=True
                    )

                    # 更新其他摄像头设置
                    camera_updates = {}
                    for key in ['width', 'height', 'fps', 'quality']:
                        if key in settings:
                            self.camera_settings[key] = settings[key]
                            camera_updates[key] = settings[key]

                    if camera_updates:
                        self.config_manager.set_camera_settings(camera_updates)

                    await websocket.send(json.dumps({
                        'type': 'response',
                        'command': 'update_camera_settings',
                        'success': True,
                        'message': '摄像头设置更新成功',
                        'camera_settings': self.camera_settings,
                        'timestamp': timestamp
                    }))
                except Exception as e:
                    await websocket.send(json.dumps({
                        'type': 'response',
                        'command': 'update_camera_settings',
                        'success': False,
                        'message': f'摄像头设置更新失败: {e}',
                        'timestamp': timestamp
                    }))
                
           
                   
        except Exception as e:
            print(f"处理命令失败: {e}")
            await websocket.send(json.dumps({
                'type': 'error',
                'message': f'命令执行失败: {str(e)}'
            }))

  
    def add_video_info_overlay(self, frame, frame_count=0, real_fps=0, video_path=""):
        """在视频帧上添加信息覆盖层

        Args:
            frame: 视频帧
            frame_count: 当前帧数
            loop_count: 循环次数
            video_path: 视频文件路径
        """
        try:
            height, width = frame.shape[:2]

            # 添加半透明背景
            # overlay = frame.copy()

            # 顶部信息栏
            # cv2.rectangle(overlay, (0, 0), (width, 60), (0, 0, 0), -1)
            # cv2.addWeighted(overlay, 0.9, frame, 0.3, 0, frame)

            # 添加文字信息
            # video_name = os.path.basename(video_path)

            # 顶部文字
            # cv2.putText(frame, f"ROV Video Stream - {video_name}",
            #            (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(frame, f"Frame: {frame_count} | Fps: {real_fps}",
                       (0, 25), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
            
            cv2.putText(frame, f"FPS: {real_fps}",
                       (width-230, 25), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

            # 底部信息栏
            # cv2.rectangle(overlay, (0, height-40), (width, height), (0, 0, 0), -1)
            # cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
            # # 底部文字
            # timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            # cv2.putText(frame, f"Video File Mode | {timestamp}",
            #            (10, height-15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            # # 右下角状态
            # status_text = "PLAYING"
            # text_size = cv2.getTextSize(status_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            # cv2.putText(frame, status_text,
            #            (width - text_size[0] - 10, height - 15),
            #            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        except Exception as e:
            print(Fore.RED + f"❌ 添加视频信息覆盖层失败: {e}")

    def switch_to_video_mode(self, video_path=None):
        """切换到视频文件模式

        Args:
            video_path: 视频文件路径，默认为 'RovRemote/video/test.mp4'
        """
        try:
            print(Fore.CYAN + "🎬 切换到视频文件模式")

            # 释放当前摄像头
            if hasattr(self, 'camera') and self.camera:
                self.camera.release()
                self.camera = None
                print(Fore.YELLOW + "📹 已释放摄像头资源")

            # 设置视频文件路径
            if video_path is None:
                video_path = os.path.join(os.path.dirname(__file__), 'video', 'test.mp4')

            self.video_file_path = video_path
            self.video_mode = True

            print(Fore.GREEN + f"✅ 已切换到视频文件模式: {video_path}")

        except Exception as e:
            print(Fore.RED + f"❌ 切换到视频文件模式失败: {e}")

    def switch_to_camera_mode(self):
        """切换回摄像头模式"""
        try:
            print(Fore.CYAN + "📹 切换回摄像头模式")

            self.video_mode = False
            self.video_file_path = None

            # 重新初始化摄像头
            self.setup_camera()

            print(Fore.GREEN + "✅ 已切换回摄像头模式")

        except Exception as e:
            print(Fore.RED + f"❌ 切换回摄像头模式失败: {e}")

    async def websocket_handler(self, websocket, path=None):
        """WebSocket连接处理器"""
        # path参数在不同版本的websockets库中可能为可选参数
        await self.register_client(websocket)
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    if data.get('type') == 'command':
                        await self.handle_sorcket_command(websocket, data)
                    elif data.get('type') == 'mode_control':
                        await self.handle_mode_control(websocket, data)
                except json.JSONDecodeError:
                    print(f"无效的JSON数据: {message}")
                except Exception as e:
                    print(f"处理消息失败: {e}")
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            await self.unregister_client(websocket)

    async def handle_mode_control(self, websocket, data):
        """处理模式控制命令"""
        try:
            mode = data.get('mode')
            mode_data = data.get('data', {})

            print(f"🎮 收到模式控制命令: {mode}, 数据: {mode_data}")

            if mode == 'depth_hold':
                # 定深控制
                if mode_data.get('enable'):
                    target_depth = mode_data.get('target', 0.0)
                    print(f"🌊 启用定深模式，目标深度: {target_depth:.2f}m")

                    # 发送定深控制命令到ESP32
                    if self.serial_port:
                        # depth_data = struct.pack('<f', float(target_depth))
                        # self.send_command(0x13, depth_data)  # handleSetDepthControl
                        self.serial_commond_list.append((0x14, [0x01]))

                        # 广播模式状态更新
                        await self.broadcast_mode_status('depth_hold', True, target_depth)
                else:
                    print("🌊 禁用定深模式")

                    # 发送禁用定深命令
                    if self.serial_port:
                        # 发送一个特殊值表示禁用（比如负数）
                        # depth_data = struct.pack('<f', -1.0)
                        # self.send_command(0x13, depth_data)
                        self.serial_commond_list.append((0x14, [0x01]))
                        
                        # 广播模式状态更新
                        await self.broadcast_mode_status('depth_hold', False, 0)

            elif mode == 'heading_hold':
                # 锁定航向控制
                if mode_data.get('enable'):
                    target_heading = mode_data.get('target', 0)
                    print(f"🧭 启用锁定航向，目标航向: {target_heading}°")

                    # 发送锁定航向命令到ESP32
                    if self.serial_port:
                        # heading_data = struct.pack('<f', float(target_heading))
                        # self.send_command(0x14, heading_data)  # handleSetHeadingControl
                        self.serial_commond_list.append((0x15, [0x01]))

                        # 广播模式状态更新
                        await self.broadcast_mode_status('heading_hold', True, target_heading)
                else:
                    print("🧭 禁用锁定航向")

                    # 发送禁用锁定航向命令
                    if self.serial_port:
                        # 发送一个特殊值表示禁用（比如负数）
                        # heading_data = struct.pack('<f', -1.0)
                        # self.send_command(0x14, heading_data)
                        self.serial_commond_list.append((0x15, [0x01]))

                        # 广播模式状态更新
                        await self.broadcast_mode_status('heading_hold', False, 0)

        except Exception as e:
            print(f"处理模式控制命令失败: {e}")

    async def broadcast_mode_status(self, mode, enabled, target_value):
        """广播模式状态更新到所有客户端"""
        try:
            message = {
                'type': 'mode_status',
                'mode': mode,
                'enabled': enabled,
                'target': target_value,
                'timestamp': time.time()
            }

            # 发送给所有连接的客户端
            if self.clients:
                tasks = []
                for client in self.clients.copy():
                    tasks.append(self.safe_send(client, message))

                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            print(f"广播模式状态失败: {e}")

    def create_websocket_handler(self):
        """创建兼容不同websockets版本的处理器"""
        async def handler(websocket, *args):
            # 兼容不同版本的websockets库，忽略额外参数
            _ = args  # 忽略未使用的参数
            await self.websocket_handler(websocket)
        return handler

    def start_data_collection(self): 
        # [ ]!!! """启动数据采集"""
        def collect_data():
            request_count = 0
            #  
            while True:
                if self.serial_port and self.esp32_init:
                    # 高频请求传感器数据
                    # request_count += 1

                    # 优化请求策略：统一使用"获取所有传感器数据"协议，降低通讯延迟
                    # if request_count % 100 == 0:
                    #     print(f"发送心跳包 #{request_count//100}")
                    #     self.send_command(0xFF)  # 每10秒发送一次心跳包
                    # else:
                    #     if request_count % 100 == 1:  # 每10秒打印一次状态
                    #         print(f"高频统一传感器数据请求中... (10Hz)")
                    #     self.request_all_sensor_data()
                    
                    # TODO 测试时注释 启动数据采集 
                    # self.request_all_sensor_data()
                    time.sleep(0.02)  # 每20ms请求一次，实现50Hz更新频率

        # 启动数据采集线程
        collection_thread = threading.Thread(target=collect_data, daemon=True)
        collection_thread.start()

        if self.serial_port:
            print(Fore.GREEN+"ESP32数据采集已启动")
        else:
            print(Fore.YELLOW+"\n\n------------数据模拟已启动------------\n")
            # 模拟数据模式下设置响应状态
            self.sensor_data['serialResponseStatus'] = 'disconnected'
            self.sensor_data['serialLatency'] = 150

# Flask应用用于提供静态文件和视频流
app = Flask(__name__, static_folder='.', template_folder='.')

# 全局ROV服务器实例
rov_server = None

@app.route('/')
def index():
    return send_from_directory('./MobileWebRemote', 'rov_index.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('./MobileWebRemote', filename)

@app.route('/video_feed')
def video_feed():
    """视频流 - 直接从摄像头获取"""
    if rov_server:
        return Response(rov_server.generate_frames(),
                       mimetype='multipart/x-mixed-replace; boundary=frame')
    else:
        return Response("视频流不可用", mimetype='text/plain')

def run_flask():
    """运行Flask服务器"""
    app.run(host='0.0.0.0', port=8088, debug=False, threaded=True)

        
async def main():
    """主函数"""
    global rov_server
    # 创建ROV服务器实例
    rov_server = ROVWebServer()
    
    # 启动数据采集（如果需要）
    # rov_server.start_data_collection()

    # 启动Flask服务器线程
    flask_thread = threading.Thread(target=run_flask, daemon=True)
    flask_thread.start()
    print(Fore.CYAN+"Flask服务器已启动在端口8088")
    
        # 启动WebSocket服务器
    print(Fore.CYAN+"启动WebSocket服务器")
    async with websockets.serve(rov_server.create_websocket_handler(), "0.0.0.0", ws_port):
        print(Fore.CYAN+f"WebSocket在端口{ws_port}")
        await asyncio.Future()  # 保持运行

if __name__ == "__main__":
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        
        if rov_server:
            rov_server.controller_running = False
     
        print("服务器已停止")
