/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #00ff88;
    box-shadow: 0 0 10px #00ff88;
}

.status-dot.offline {
    background: #ff4444;
    box-shadow: 0 0 10px #ff4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 视频区域样式 */
.video-section {
    margin-bottom: 20px;
}

.video-container {
    position: relative;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);

    display: flex; justify-content: center; align-items: center;
}

#videoPlayer {
    width: 100%;
    height: auto;
    min-height: 250px;
    display: block;
}

.video-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* 传感器数据覆盖层 */
.currentOneWrapper{

}
.currentOne{
    padding: 2px;
    padding-right: 4px;
}

.sensor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 15;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.sensor-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.sensor-overlay.semi-transparent {
    opacity: 0.6;
}

.sensor-overlay-grid {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 15px;
    box-sizing: border-box;
}

.sensor-group {
    position: absolute;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 120px;
}

.sensor-group.top-left {
    top: 5px;
    left: 5px;
}

.sensor-group.top-right {
    top: 5px;
    right: 5px;
}

.sensor-group.bottom-left {
    bottom: 50px;
    left: 5px;
}

.sensor-group.bottom-right {
    bottom: 50px;
    right: 5px;
}

.sensor-item-overlay {
    display: flex;
    align-items: center;
    gap: 0px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 1px 1px;
    font-size: 0.85rem;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.sensor-icon {
    font-size: 1rem;
    min-width: 16px;
    text-align: center;
}

.sensor-label {
    font-size: 0.75rem;
    color: #b0b0b0;
    min-width: 30px;
}

.sensor-value-overlay {
    font-weight: 600;
    color: #ffffff;
    font-size: 0.85rem;
    min-width: 40px;
    text-align: right;
}

/* 覆盖层控制按钮 */
.overlay-controls {
    position: absolute;
    top: 15px;
    right: 50%;
    transform: translateX(50%);
    display: flex;
    gap: 8px;
    pointer-events: auto;
}

.overlay-toggle-btn {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #ffffff;
    padding: 6px 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.overlay-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
}

.overlay-toggle-btn:active {
    transform: scale(0.95);
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .sensor-overlay-grid {
        padding: 10px;
    }

    .sensor-group {
        min-width: 100px;
    }

    .sensor-group.top-left,
    .sensor-group.top-right {
        top: 10px;
    }

    .sensor-group.top-left {
        left: 10px;
    }

    .sensor-group.top-right {
        right: 10px;
    }

    .sensor-group.bottom-left,
    .sensor-group.bottom-right {
        bottom: 40px;
    }

    .sensor-group.bottom-left {
        left: 10px;
    }

    .sensor-group.bottom-right {
        right: 10px;
    }

    .sensor-item-overlay {
        padding: 4px 8px;
        font-size: 0.75rem;
        gap: 4px;
    }

    .sensor-icon {
        font-size: 0.9rem;
        min-width: 14px;
    }

    .sensor-label {
        font-size: 0.7rem;
        min-width: 25px;
    }

    .sensor-value-overlay {
        font-size: 0.75rem;
        min-width: 35px;
    }

    .overlay-controls {
        top: 10px;
        gap: 6px;
    }

    .overlay-toggle-btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .sensor-group {
        min-width: 80px;
    }

    .sensor-item-overlay {
        padding: 3px 6px;
        font-size: 0.7rem;
    }

    .sensor-label {
        display: none; /* 在很小的屏幕上隐藏标签，只显示图标和数值 */
    }

    .sensor-value-overlay {
        min-width: 30px;
        font-size: 0.7rem;
    }
}

.video-status {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
}

.video-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* 状态面板样式 */
.status-panel {
    margin-bottom: 20px;
}

.status-grid {
    display: flex;
    flex-wrap: wrap;
    width:100%;
    /* grid-template-columns: 3fr; */
    gap: 15px;
}

.status-card {
    width:45%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* 特定状态卡片的颜色主题 */
.environment-card {
    border-left: 4px solid #00d4ff;
}

.environment-card .status-icon {
    color: #00d4ff;
}

/* 环境传感器网格布局 */
.environment-grid {
    /* display: grid; */
    grid-template-columns: 3fr;
    /* gap: 12px; */
    margin-top: 10px;
}

.env-item {
    display: flex;
    /* flex-direction: column; */
    align-items: center;
    padding: 1px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.env-label {
    font-size: 0.8rem;
    color: #b0b0b0;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.env-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 2px;
}

.env-trend {
    font-size: 0.7rem;
    color: #4ade80;
    opacity: 0.8;
}

/* 响应式布局 - 在较大屏幕上显示为三列 */
@media (min-width: 768px) {
    .environment-grid {
        grid-template-columns: 3fr;
        gap: 8px;
    }

    .env-item {
        padding: 6px;
    }

    .env-value {
        font-size: 1rem;
    }
}

.heading-card {
    border-left: 4px solid #45b7d1;
}

.phone-heading-card {
    border-left: 4px solid #96ceb4;
}

.system-card {
    border-left: 4px solid #feca57;
}

.status-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.status-info h3 {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.status-value {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.status-trend {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* 指南针样式 */
.compass-display {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    position: relative;
    margin-top: 10px;
}

.compass-needle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 15px;
    background: #00d4ff;
    transform-origin: bottom center;
    transform: translate(-50%, -100%);
    transition: transform 0.5s ease;
}

.compass-needle::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -2px;
    width: 6px;
    height: 6px;
    background: #00d4ff;
    border-radius: 50%;
}

/* 罗盘权限按钮样式 */
.compass-permission-btn {
    background: linear-gradient(135deg, #45b7d1, #96c93d);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(69, 183, 209, 0.3);
}

.compass-permission-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(69, 183, 209, 0.4);
}

.compass-permission-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(69, 183, 209, 0.3);
}

/* 传感器来源标签样式 */
.compass-source {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    margin-top: 4px;
    font-style: italic;
}

/* 串口响应指示器样式 */
.response-indicator {
    display: inline-block;
    font-size: 0.8rem;
    color: #00ff00;
    animation: pulse 1s infinite;
}

.response-indicator.success {
    color: #00ff00;  /* 绿色：<20ms */
    animation: pulse 0.5s infinite;
}

.response-indicator.timeout {
    color: #ffeb3b;  /* 黄色：20-100ms */
    animation: pulse 0.8s infinite;
}

.response-indicator.error {
    color: #f44336;  /* 红色：>100ms */
    animation: pulse 1.5s infinite;
}

.response-indicator.disconnected {
    color: #000000;  /* 黑色：未连接/无响应 */
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* 系统状态网格样式 */
.system-status-grid {
    display: grid;
    grid-template-columns: 3fr;
    gap: 1px;
    margin-top: 1px;
}

.system-status-grid .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.system-status-grid .status-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 2px;
    text-align: center;
}

.system-status-grid .status-value {
    font-size: 0.8rem;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
}

/* 系统状态卡片样式 */
.system-status-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.system-status-card .status-icon {
    font-size: 2rem;
}

/* 姿态显示样式 */
.attitude-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.attitude-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border-left: 3px solid #00d4ff;
}

.attitude-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.attitude-value {
    font-size: 0.9rem;
    color: #00d4ff;
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

.attitude-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.attitude-card .status-icon {
    font-size: 2rem;
}



/* 控制面板样式 */
.control-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.control-tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.2);
}

.tab-btn {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

.control-group {
    margin-bottom: 25px;
}

.control-group h4 {
    margin-bottom: 15px;
    color: #00d4ff;
    font-size: 1.1rem;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.control-button {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 80px;
}

.control-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

.control-button.small {
    padding: 8px 15px;
    font-size: 0.8rem;
    min-width: auto;
}

/* 设置模式按钮样式 */
.mode-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 8px;
    padding: 10px 15px;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mode-button:hover {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
}

.mode-button.active {
    background: linear-gradient(to right, rgba(0, 150, 136, 0.3), rgba(0, 150, 136, 0.5));
    border-color: rgba(0, 150, 136, 0.5);
}

.mode-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}

.mode-text {
    flex-grow: 1;
    text-align: left;
    font-weight: 500;
}

.mode-status {
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 10px;
    background: rgba(255, 0, 0, 0.2);
    color: #ff5252;
}

.mode-button.active .mode-status {
    background: rgba(0, 255, 0, 0.2);
    color: #4caf50;
}

.mode-info {
    margin-top: 10px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    font-size: 0.85rem;
}

.mode-info p {
    margin: 5px 0;
}

.current-targets {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.current-targets span {
    font-size: 0.8rem;
    color: #b0b0b0;
}

#targetDepth, #targetHeading {
    font-weight: bold;
    color: #ffffff;
}

/* 滑块样式 */
.slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #00d4ff;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.5);
}

.slider-value {
    min-width: 40px;
    text-align: center;
    font-size: 0.9rem;
}

/* 设置项样式 */
.setting-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.setting-item label {
    min-width: 120px;
    font-size: 0.9rem;
}

.setting-item input,
.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-size: 0.9rem;
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

/* 加载和错误提示 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ff4444;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    display: none;
    align-items: center;
    gap: 10px;
    z-index: 1001;
    box-shadow: 0 5px 15px rgba(255, 68, 68, 0.3);
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 5px;
    }
    
    .header {
        padding: 10px 15px;
    }
    
    .header h1 {
        font-size: 1.2rem;
    }
    
    .status-grid {
        grid-template-columns: 3fr;
    }
    
    .button-group {
        justify-content: center;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .setting-item label {
        min-width: auto;
    }
}

/* ADC校准控件样式 */
.calibration-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.calibration-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input-group label {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.9);
}

.input-group input {
    padding: 8px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1em;
}

.input-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.calibrate-btn {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calibrate-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.calibrate-btn:active {
    transform: translateY(0);
}

.calibration-result {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.9);
    min-height: 20px;
    padding: 5px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    margin-top: 5px;
}
