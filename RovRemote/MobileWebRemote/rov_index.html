<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROV远程控制系统</title>
    <link rel="stylesheet" href="rov_style.css">
</head>

<body style="background-color: #1a1a2e;" id="body">
    <div class="container">
        <!-- 视频流区域 -->
        <section class="video-section">
            <div class="video-container">
                <img id="videoStream" alt="ROV视频流" style="width: auto; height: 380px; display: block;">
                <video id="videoPlayer" controls autoplay muted playsinline style="display: none;">
                    您的浏览器不支持视频播放
                </video>
                <div class="video-overlay" style="display: none;">
                    <div class="video-controls">
                        <button id="fullscreenBtn" class="control-btn">📺</button>
                        <button id="recordBtn" class="control-btn">🔴</button>
                        <button id="switchMode" class="control-btn">🔄</button>
                    </div>
                </div>

                <!-- 传感器数据覆盖层 -->
                <div class="sensor-overlay" id="sensorOverlay">
                    <div class="sensor-overlay-grid">
                        <!-- 左上角 -->
                        <div class="sensor-group top-left">
                            <div class="sensor-item-overlay">
                                <span class="env-label">模式</span>
                                <span class="sensor-value-overlay" id="mode" style="font-size: 30px;">普通</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">💧</span>
                                <span class="env-label">水密</span>
                                <span class="env-trend" id="waterSealTrend">正常</span>
                                <span class="env-value" id="waterSealValue">()</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-label">电池</span>
                                <span class="sensor-value-overlay" id="overlayBattery">--V</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-label">电流</span>
                                <span class="sensor-value-overlay" id="motorAllCurrent">--A</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-label">深度</span>
                                <span class="sensor-value-overlay" id="overlayDepth">0.0m</span>
                                <span class="env-trend" id="depthTrend">稳定</span>
                            </div>
                        </div>

                        <!-- 左下角 -->
                        <div class="sensor-group bottom-left">
                            <div class="sensor-item-overlay">
                                <span class="sensor-value-overlay" id="esp32CommunicationResponseText" style="font-size: 10px;color: #ffd000;text-align: left;font-weight:100;
                                    max-height: 40px;max-width: 450px;word-break: break-all;"></span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-value-overlay" id="esp32CommunicationError"
                                    style="font-size: 20px;color: #ff6b6b;max-width: 450px;word-break: break-all;"></span>
                            </div>
                        </div>

                        <!-- 右上角 -->
                        <div class="sensor-group top-right">


                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">💧</span>
                                <span class="sensor-label">状态</span>
                                <span class="sensor-value-overlay" id="overlayUnderwaterStatus">水面</span>
                                <span class="env-trend" id="overlayUnderwaterTrend">()</span>
                            </div>

                            <div class="sensor-item-overlay">
                                <span class="sensor-label">串口</span>
                                <span class="sensor-value-overlay" id="overlayLatency">--ms</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-label">网络</span>
                                <span class="sensor-value-overlay" id="latency">--</span>ms
                            </div>
                        </div>


                        <!-- 右下角 -->
                        <div class="sensor-group bottom-right">
                            <div class="sensor-item-overlay" style="display: none;">
                                <span class="sensor-label">电机</span>
                                <div>
                                    <div style="display: flex;" class="currentOneWrapper">
                                        <div id="motorCurrent2" class="currentOne" style="display: flex;width: 50%;"></div>
                                        <div id="motorCurrent1" class="currentOne" style="display: flex;"></div>
                                    </div>
                                    <div style="display: flex;" class="currentOneWrapper">
                                        <div id="motorCurrent6" class="currentOne" style="display: flex;width: 50%;"></div>
                                        <div id="motorCurrent5" class="currentOne" style="display: flex;"></div>
                                    </div>
                                    <div style="display: flex;" class="currentOneWrapper">
                                        <div id="motorCurrent4" class="currentOne" style="display: flex;width: 50%;"></div>
                                        <div id="motorCurrent3" class="currentOne" style="display: flex;"></div>
                                    </div>
                                </div>

                            </div>

                            <div class="sensor-item-overlay">
                                <span class="sensor-icon">🧭</span>
                                <span class="sensor-label">航向</span>
                                <span class="sensor-value-overlay" id="overlayHeading">--°</span>
                                <!-- <span class="attitude-label">YAW</span>
                                <span class="attitude-value" id="yawAngle">0°</span> -->
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="attitude-label">横滚</span>
                                <span class="attitude-value" id="rollAngle">0°</span>
                                <span class="attitude-label">俯仰</span>
                                <span class="attitude-value" id="pitchAngle">0°</span>
                            </div>
                            <div class="sensor-item-overlay">
                                <span class="sensor-label">水温</span>
                                <span class="sensor-value-overlay" id="overlayTemperature">--°C</span>
                            </div>
                            <!-- <div class="sensor-item-overlay">
                                <span class="sensor-icon">📊</span>
                                <span class="sensor-label">压力</span>
                                <span class="sensor-value-overlay" id="overlayPressure">--hPa</span>
                                <span class="env-trend" id="pressureTrend">正常</span>
                            </div> -->
                        </div>
                    </div>

                    <!-- 覆盖层控制按钮 -->
                    <div class="overlay-controls">
                        <button id="toggleOverlay" class="overlay-toggle-btn" title="显示/隐藏传感器数据">📊</button>
                        <button id="toggleOverlayOpacity" class="overlay-toggle-btn" title="调整透明度">👁️</button>
                    </div>
                </div>

                <div class="video-status" id="videoStatus">
                    <span>MJPEG流</span>
                </div>
            </div>
        </section>

        <!-- 状态信息面板 -->
        <section class="status-panel">
            <div class="status-grid">

                <div class="status-card heading-card" style="display: flex;">
                    <!-- ROV航向 -->
                    <div class="status-info" style="width: 50%;">
                        <h3>ROV航向</h3>
                        <div class="status-value" id="rovHeading">0°</div>
                        <div class="compass-display">
                            <div class="compass-needle" id="rovCompass"></div>
                        </div>
                    </div>

                    <!-- 手机航向 -->
                    <div class="status-info">
                        <h3>手机航向</h3>
                        <div class="status-value" id="phoneHeading">初始化中...</div>
                        <div class="compass-display">
                            <div class="compass-needle" id="phoneCompass"></div>
                        </div>
                        <button id="compassPermissionBtn" class="compass-permission-btn">
                            启用罗盘
                        </button>
                    </div>
                </div>

                <!-- ADC校准控制 -->
                <div class="status-card calibration-card">
                    <div class="status-icon">⚡</div>
                    <div class="status-info">
                        <h3>ADC校准</h3>
                        <div class="calibration-controls">
                            <div class="input-group">
                                <label for="referenceVoltage">参考电压(V):</label>
                                <input type="number" id="referenceVoltage" step="0.001" min="0" max="10"
                                    placeholder="4.640">
                            </div>
                            <button id="calibrateAdcBtn" class="calibrate-btn">校准ADC</button>
                            <div id="calibrationResult" class="calibration-result"></div>
                        </div>
                    </div>
                </div>

            </div>
        </section>

        <!-- 控制面板 -->
        <section class="control-panel">
            <div class="control-tabs">
                <button class="tab-btn active" data-tab="basic">基础控制</button>
                <button class="tab-btn" data-tab="advanced">高级设置</button>
            </div>

            <!-- 基础控制 -->
            <div class="tab-content active" id="basic">
                <div class="control-group">
                    <h4>灯光控制</h4>
                    <div class="button-group">
                        <button class="control-button" id="lightToggle">💡 灯光开关</button>
                        <input type="range" id="lightBrightness" min="0" max="100" value="50" class="slider">
                        <span class="slider-value">50%</span>
                    </div>
                </div>

                <div class="control-group">
                    <h4>设置模式</h4>
                    <div class="button-group">
                        <button class="control-button mode-button" id="depthHoldBtn" data-mode="depth">
                            <span class="mode-icon">🌊</span>
                            <span class="mode-text">定深模式</span>
                            <span class="mode-status" id="depthHoldStatus">关闭</span>
                        </button>
                        <button class="control-button mode-button" id="headingHoldBtn" data-mode="heading">
                            <span class="mode-icon">🧭</span>
                            <span class="mode-text">锁定航向</span>
                            <span class="mode-status" id="headingHoldStatus">关闭</span>
                        </button>
                    </div>
                    <div class="mode-info">
                        <p><strong>手柄快捷键:</strong></p>
                        <p>🎮 A键 - 定深模式 | 🎮 X键 - 锁定航向</p>
                        <div class="current-targets">
                            <span>目标深度: <span id="targetDepth">--</span>m</span>
                            <span>目标航向: <span id="targetHeading">--</span>°</span>
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <h4>相机控制</h4>
                    <div class="button-group">
                        <button class="control-button" id="cameraUp">⬆️</button>
                        <button class="control-button" id="cameraDown">⬇️</button>
                        <button class="control-button" id="cameraLeft">⬅️</button>
                        <button class="control-button" id="cameraRight">➡️</button>
                        <button class="control-button" id="cameraCenter">🎯 居中</button>
                    </div>
                </div>
            </div>

            <!-- 高级设置 -->
            <div class="tab-content" id="advanced">
                <div class="control-group">
                    <h4>传感器设置</h4>
                    <div class="setting-item">
                        <label>深度校准偏移:</label>
                        <input type="number" id="depthOffset" value="0" step="0.1">
                        <span>m</span>
                    </div>
                    <div class="setting-item">
                        <label>航向校准:</label>
                        <button class="control-button small" id="calibrateHeading">校准航向</button>
                    </div>
                </div>

                <div class="control-group">
                    <h4>视频设置</h4>
                    <div class="setting-item">
                        <label>视频质量:</label>
                        <select id="videoQuality">
                            <option value="low">低 (480p)</option>
                            <option value="medium" selected>中 (720p)</option>
                            <option value="high">高 (1080p)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>帧率:</label>
                        <select id="frameRate">
                            <option value="15">15 FPS</option>
                            <option value="30" selected>30 FPS</option>
                            <option value="60">60 FPS</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>正在连接ROV系统...</p>
    </div>

    <!-- 错误提示 -->
    <div class="error-toast" id="errorToast">
        <span class="error-message"></span>
        <button class="close-btn">&times;</button>
    </div>

    <script src="rov_script.js?t=1"></script>
</body>

</html>
