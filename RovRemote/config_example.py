#!/usr/bin/env python3
"""
ROV配置管理器使用示例
演示如何使用ConfigManager类管理ROV系统配置
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from config_manager import ConfigManager
from colorama import Fore, init

init(autoreset=True)

def main():
    """配置管理器使用示例"""
    
    print(Fore.CYAN + "=" * 60)
    print(Fore.CYAN + "ROV配置管理器使用示例")
    print(Fore.CYAN + "=" * 60)
    
    # 1. 创建配置管理器实例
    print(Fore.YELLOW + "\n1. 初始化配置管理器")
    config_manager = ConfigManager()
    
    # 2. 获取当前配置信息
    print(Fore.YELLOW + "\n2. 获取配置文件信息")
    config_info = config_manager.get_config_info()
    print(f"配置文件路径: {config_info['config_path']}")
    print(f"配置文件存在: {config_info['config_exists']}")
    print(f"配置文件大小: {config_info['config_size']} 字节")
    print(f"配置节: {config_info['sections']}")
    
    # 3. 获取摄像头设置
    print(Fore.YELLOW + "\n3. 获取当前摄像头设置")
    camera_settings = config_manager.get_camera_settings()
    print("当前摄像头设置:")
    for key, value in camera_settings.items():
        print(f"  {key}: {value}")
    
    # 4. 更新摄像头设置
    print(Fore.YELLOW + "\n4. 更新摄像头设置")
    new_settings = {
        'width': 1920,
        'height': 1080,
        'fps': 60,
        'contrast': 1.2,
        'saturation': 1.1,
        'sharpness': 0.5,
        'sharpen_method': 'unsharp_mask'
    }
    
    print("更新设置:")
    for key, value in new_settings.items():
        print(f"  {key}: {value}")
    
    config_manager.set_camera_settings(new_settings)
    
    # 5. 验证更新后的设置
    print(Fore.YELLOW + "\n5. 验证更新后的设置")
    updated_settings = config_manager.get_camera_settings()
    print("更新后的摄像头设置:")
    for key, value in updated_settings.items():
        print(f"  {key}: {value}")
    
    # 6. 配置验证
    print(Fore.YELLOW + "\n6. 配置验证")
    validation = config_manager.validate_config()
    print(f"配置有效性: {validation['valid']}")
    
    if validation['errors']:
        print("配置错误:")
        for error in validation['errors']:
            print(f"  - {error}")
    
    if validation['warnings']:
        print("配置警告:")
        for warning in validation['warnings']:
            print(f"  - {warning}")
    
    # 7. 获取特定设置
    print(Fore.YELLOW + "\n7. 获取特定设置")
    width = config_manager.get_setting('camera_settings', 'width', 640)
    fps = config_manager.get_setting('camera_settings', 'fps', 30)
    debug_mode = config_manager.get_setting('system_settings', 'debug_mode', False)
    
    print(f"摄像头宽度: {width}")
    print(f"摄像头帧率: {fps}")
    print(f"调试模式: {debug_mode}")
    
    # 8. 设置特定配置
    print(Fore.YELLOW + "\n8. 设置特定配置")
    config_manager.set_setting('system_settings', 'debug_mode', True)
    config_manager.set_setting('system_settings', 'log_level', 'DEBUG')
    
    # 9. 导出配置
    print(Fore.YELLOW + "\n9. 导出配置")
    export_path = os.path.join('data', 'exported_config.json')
    success = config_manager.export_config(export_path)
    if success:
        print(f"配置已导出到: {export_path}")
    
    # 10. 创建备份并重置配置
    print(Fore.YELLOW + "\n10. 重置摄像头配置到默认值")
    if input("是否重置摄像头配置到默认值? (y/N): ").lower() == 'y':
        config_manager.reset_to_default('camera_settings')
        
        # 验证重置结果
        reset_settings = config_manager.get_camera_settings()
        print("重置后的摄像头设置:")
        for key, value in reset_settings.items():
            print(f"  {key}: {value}")
    
    # 11. 显示所有配置
    print(Fore.YELLOW + "\n11. 显示所有配置")
    all_settings = config_manager.get_all_settings()
    print("所有配置:")
    for section, settings in all_settings.items():
        print(f"\n[{section}]")
        if isinstance(settings, dict):
            for key, value in settings.items():
                print(f"  {key}: {value}")
        else:
            print(f"  {settings}")
    
    print(Fore.GREEN + "\n" + "=" * 60)
    print(Fore.GREEN + "配置管理器示例演示完成")
    print(Fore.GREEN + "=" * 60)

def test_config_operations():
    """测试配置操作"""
    
    print(Fore.CYAN + "\n" + "=" * 60)
    print(Fore.CYAN + "配置操作测试")
    print(Fore.CYAN + "=" * 60)
    
    config_manager = ConfigManager()
    
    # 测试不同的摄像头设置组合
    test_cases = [
        {
            'name': '高清设置',
            'settings': {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'quality': 95,
                'contrast': 1.1,
                'saturation': 1.0,
                'sharpness': 0.3
            }
        },
        {
            'name': '性能优化设置',
            'settings': {
                'width': 1280,
                'height': 720,
                'fps': 60,
                'quality': 75,
                'contrast': 1.0,
                'saturation': 1.0,
                'sharpness': 0.0
            }
        },
        {
            'name': '低带宽设置',
            'settings': {
                'width': 640,
                'height': 480,
                'fps': 15,
                'quality': 60,
                'contrast': 1.2,
                'saturation': 1.1,
                'sharpness': 0.5
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(Fore.YELLOW + f"\n测试用例 {i}: {test_case['name']}")
        
        # 应用设置
        config_manager.set_camera_settings(test_case['settings'])
        
        # 验证设置
        current_settings = config_manager.get_camera_settings()
        
        print("应用的设置:")
        for key, value in test_case['settings'].items():
            current_value = current_settings.get(key)
            status = "✅" if current_value == value else "❌"
            print(f"  {key}: {value} → {current_value} {status}")
        
        # 验证配置
        validation = config_manager.validate_config()
        if validation['valid']:
            print(Fore.GREEN + "  配置验证: 通过")
        else:
            print(Fore.RED + "  配置验证: 失败")
            for error in validation['errors']:
                print(Fore.RED + f"    错误: {error}")
        
        if validation['warnings']:
            for warning in validation['warnings']:
                print(Fore.YELLOW + f"    警告: {warning}")

if __name__ == "__main__":
    try:
        # 运行主示例
        main()
        
        # 运行测试
        if input("\n是否运行配置操作测试? (y/N): ").lower() == 'y':
            test_config_operations()
            
    except KeyboardInterrupt:
        print(Fore.YELLOW + "\n用户中断操作")
    except Exception as e:
        print(Fore.RED + f"\n示例运行出错: {e}")
        import traceback
        traceback.print_exc()
