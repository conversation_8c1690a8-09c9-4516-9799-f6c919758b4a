import cv2
import numpy as np

def white_balance(img):
    # 分离 BGR 通道
    b, g, r = cv2.split(img)
    
    # 计算各通道均值
    avg_b = np.mean(b)
    avg_g = np.mean(g)
    avg_r = np.mean(r)
    
    # 计算调整系数（增强红色）
    scale_r = (avg_b + avg_g + avg_r) / (3 * avg_r)
    scale_g = (avg_b + avg_g + avg_r) / (3 * avg_g)
    
    # 调整通道
    r = cv2.multiply(r, scale_r)
    g = cv2.multiply(g, scale_g)
    
    # 合并通道
    balanced = cv2.merge([b, g, r])
    balanced = np.clip(balanced, 0, 255).astype(np.uint8)
    return balanced

def enhance_color_clahe(img):
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # 增强亮度通道
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
    l = clahe.apply(l)
    
    # 合并通道
    enhanced_lab = cv2.merge([l, a, b])
    enhanced_bgr = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
    return enhanced_bgr

def dehaze(img, w=0.7, t0=0.1):
    # 计算暗通道
    dark = cv2.min(cv2.min(img[:,:,0], img[:,:,1]), img[:,:,2])
    dark = cv2.erode(dark, np.ones((15,15), np.uint8))  # 最小值滤波
    
    # 估计大气光
    atm_light = np.percentile(dark, 99)
    
    # 计算透射率
    transmission = 1 - w * (dark / atm_light)
    transmission = np.clip(transmission, t0, 1)
    
    # 恢复清晰图像
    dehazed = np.empty_like(img, dtype=np.float32)
    for i in range(3):
        dehazed[:,:,i] = (img[:,:,i].astype(np.float32) - atm_light) / transmission + atm_light
    
    dehazed = np.clip(dehazed, 0, 255).astype(np.uint8)
    return dehazed

def sharpen_laplacian(img):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    laplacian = cv2.Laplacian(gray, cv2.CV_64F)
    laplacian = cv2.convertScaleAbs(laplacian)
    laplacian_3ch = cv2.cvtColor(laplacian, cv2.COLOR_GRAY2BGR)
    sharpened = cv2.addWeighted(img, 1.5, laplacian_3ch, -0.5, 0)
    return sharpened

def unsharp_mask(img, sigma=1.0, strength=1.5):
    blurred = cv2.GaussianBlur(img, (0, 0), sigma)
    sharpened = cv2.addWeighted(img, 1.0 + strength, blurred, -strength, 0)
    return sharpened

def enhance_underwater_video(frame):
    # 1. 白平衡（恢复颜色）
    # frame = white_balance(frame)
    
    # 2. 去雾（增强清晰度）
    frame = dehaze(frame)
    
    # 3. 锐化（增强边缘）
    # frame = sharpen_laplacian(frame)
    
    # 4. CLAHE（增强对比度）
    # frame = enhance_color_clahe(frame)
    
    return frame

# 读取视频并处理
# cap = cv2.VideoCapture("underwater.mp4")
# while cap.isOpened():
#     ret, frame = cap.read()
#     if not ret:
#         break
    
#     enhanced = enhance_underwater_video(frame)
#     cv2.imshow("Enhanced", enhanced)
    
#     if cv2.waitKey(1) & 0xFF == ord('q'):
#         break

# cap.release()
# cv2.destroyAllWindows()
