#!/usr/bin/env python3
"""
ROV配置管理器
用于读取、保存和管理ROV系统的配置信息
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional
from colorama import Fore, init

init(autoreset=True)

class ConfigManager:
    """ROV配置管理类"""
    
    def __init__(self, config_dir: str = "data", config_file: str = "rov_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
            config_file: 配置文件名
        """
        self.config_dir = config_dir
        self.config_file = config_file
        self.config_path = os.path.join(config_dir, config_file)
        
        # 确保配置目录存在
        self._ensure_config_dir()
        
        # 默认配置
        self.default_config = {
            "camera_settings": {
                "width": 1280,
                "height": 720,
                "fps": 30,
                "quality": 85,
                "contrast": 1.0,
                "saturation": 1.0,
                "sharpness": 0.0,
                "sharpen_method": "standard"
            },
            "serial_settings": {
                "port": "/dev/ttyUSB0",
                "baudrate": 115200,
                "timeout": 1.0
            },
            "system_settings": {
                "debug_mode": False,
                "log_level": "INFO",
                "auto_save": True,
                "save_interval": 30
            },
             "servo_settings": {
                "P": 10,
                "I": 2,
                "D": 0.5,
                "servo_target_current": 1
            },
            "video_settings": {
                "video_mode": False,
                "video_file_path": None,
                "loop_mode": True,
                "auto_resize": True,
                "show_overlay": True,
                "apply_effects": True
            },
            "metadata": {
                "created_time": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat(),
                "version": "1.0.0",
                "description": "ROV系统配置文件"
            }
        }
        
        # 当前配置
        self.config = {}
        
        # 加载配置
        self.load_config()
        
        print(Fore.GREEN + f"✅ 配置管理器初始化完成: {self.config_path}")

    def _ensure_config_dir(self):
        """确保配置目录存在"""
        try:
            if not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir)
                print(Fore.CYAN + f"📁 创建配置目录: {self.config_dir}")
        except Exception as e:
            print(Fore.RED + f"❌ 创建配置目录失败: {e}")

    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                self.config = self._merge_config(self.default_config, loaded_config)
                
                # 更新最后修改时间
                self.config["metadata"]["last_modified"] = datetime.now().isoformat()
                
                print(Fore.GREEN + f"✅ 配置文件加载成功: {self.config_path}")
                return self.config
            else:
                # 配置文件不存在，使用默认配置并保存
                print(Fore.YELLOW + f"⚠️ 配置文件不存在，使用默认配置: {self.config_path}")
                self.config = self.default_config.copy()
                self.save_config()
                return self.config
                
        except Exception as e:
            print(Fore.RED + f"❌ 配置文件加载失败: {e}")
            print(Fore.YELLOW + "⚠️ 使用默认配置")
            self.config = self.default_config.copy()
            return self.config

    def save_config(self, backup: bool = False) -> bool:
        """
        保存配置文件
        
        Args:
            backup: 是否创建备份
            
        Returns:
            保存是否成功
        """
        try:
            # 更新元数据
            self.config["metadata"]["last_modified"] = datetime.now().isoformat()
            
            # 创建备份
            if backup and os.path.exists(self.config_path):
                self._create_backup()
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            # print(Fore.GREEN + f"✅ 配置文件保存成功: {self.config_path}")
            return True
            
        except Exception as e:
            print(Fore.RED + f"❌ 配置文件保存失败: {e}")
            return False

    def _create_backup(self):
        """创建配置文件备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"rov_config_backup_{timestamp}.json"
            backup_path = os.path.join(self.config_dir, backup_name)
            
            import shutil
            shutil.copy2(self.config_path, backup_path)
            
            print(Fore.CYAN + f"📋 配置备份已创建: {backup_path}")
            
            # 清理旧备份（保留最近5个）
            self._cleanup_old_backups()
            
        except Exception as e:
            print(Fore.YELLOW + f"⚠️ 创建配置备份失败: {e}")

    def _cleanup_old_backups(self, keep_count: int = 5):
        """清理旧的备份文件"""
        try:
            backup_files = []
            for file in os.listdir(self.config_dir):
                if file.startswith("rov_config_backup_") and file.endswith(".json"):
                    file_path = os.path.join(self.config_dir, file)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # 按修改时间排序，保留最新的几个
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            for file_path, _ in backup_files[keep_count:]:
                os.remove(file_path)
                print(Fore.YELLOW + f"🗑️ 删除旧备份: {os.path.basename(file_path)}")
                
        except Exception as e:
            print(Fore.YELLOW + f"⚠️ 清理备份文件失败: {e}")

    def _merge_config(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并默认配置和加载的配置
        
        Args:
            default: 默认配置
            loaded: 加载的配置
            
        Returns:
            合并后的配置
        """
        merged = default.copy()
        
        for key, value in loaded.items():
            if key in merged:
                if isinstance(value, dict) and isinstance(merged[key], dict):
                    merged[key] = self._merge_config(merged[key], value)
                else:
                    merged[key] = value
            else:
                merged[key] = value
        
        return merged

    def get_camera_settings(self) -> Dict[str, Any]:
        """获取摄像头设置"""
        return self.config.get("camera_settings", {})

    def set_camera_settings(self, settings: Dict[str, Any], auto_save: bool = True):
        """
        设置摄像头配置
        
        Args:
            settings: 摄像头设置字典
            auto_save: 是否自动保存
        """
        if "camera_settings" not in self.config:
            self.config["camera_settings"] = {}
        
        self.config["camera_settings"].update(settings)
        
        if auto_save:
            self.save_config()
        
        print(Fore.CYAN + f"📹 摄像头设置已更新: {list(settings.keys())}")
        
    def set_servo_settings(self,P,I,D,servo_target_current):
        self.set_setting("servo_settings","P",round(P,2))
        self.set_setting("servo_settings","I",round(I,2))
        self.set_setting("servo_settings","D",round(D,2))
        self.set_setting("servo_settings","servo_target_current",round(servo_target_current,2))

    def get_setting(self, section: str, key: str, default: Any = None) -> Any:
        """
        获取特定设置值
        
        Args:
            section: 配置节名
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(section, {}).get(key, default)

    def set_setting(self, section: str, key: str, value: Any, auto_save: bool = True):
        """
        设置特定配置值
        
        Args:
            section: 配置节名
            key: 配置键名
            value: 配置值
            auto_save: 是否自动保存
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        
        if auto_save:
            self.save_config()
        
        print(Fore.YELLOW + f"⚙️ 配置已更新: {section}.{key} = {value}")

    def get_all_settings(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config.copy()

    def reset_to_default(self, section: Optional[str] = None, auto_save: bool = True):
        """
        重置配置到默认值
        
        Args:
            section: 要重置的配置节，None表示重置所有
            auto_save: 是否自动保存
        """
        if section:
            if section in self.default_config:
                self.config[section] = self.default_config[section].copy()
                print(Fore.YELLOW + f"🔄 配置节已重置: {section}")
            else:
                print(Fore.RED + f"❌ 配置节不存在: {section}")
                return
        else:
            self.config = self.default_config.copy()
            print(Fore.YELLOW + "🔄 所有配置已重置到默认值")
        
        if auto_save:
            self.save_config()

    def export_config(self, export_path: str) -> bool:
        """
        导出配置到指定路径
        
        Args:
            export_path: 导出路径
            
        Returns:
            导出是否成功
        """
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            print(Fore.GREEN + f"✅ 配置导出成功: {export_path}")
            return True
            
        except Exception as e:
            print(Fore.RED + f"❌ 配置导出失败: {e}")
            return False

    def import_config(self, import_path: str, auto_save: bool = True) -> bool:
        """
        从指定路径导入配置
        
        Args:
            import_path: 导入路径
            auto_save: 是否自动保存
            
        Returns:
            导入是否成功
        """
        try:
            if not os.path.exists(import_path):
                print(Fore.RED + f"❌ 导入文件不存在: {import_path}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并导入的配置
            self.config = self._merge_config(self.default_config, imported_config)
            
            if auto_save:
                self.save_config()
            
            print(Fore.GREEN + f"✅ 配置导入成功: {import_path}")
            return True
            
        except Exception as e:
            print(Fore.RED + f"❌ 配置导入失败: {e}")
            return False

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置文件信息"""
        info = {
            "config_path": self.config_path,
            "config_exists": os.path.exists(self.config_path),
            "config_size": 0,
            "last_modified": None,
            "sections": list(self.config.keys()) if self.config else []
        }
        
        if info["config_exists"]:
            try:
                stat = os.stat(self.config_path)
                info["config_size"] = stat.st_size
                info["last_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
            except Exception as e:
                print(Fore.YELLOW + f"⚠️ 获取配置文件信息失败: {e}")
        
        return info

    def validate_config(self) -> Dict[str, Any]:
        """
        验证配置的有效性
        
        Returns:
            验证结果
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # 验证摄像头设置
            camera_settings = self.config.get("camera_settings", {})
            
            # 检查必需的摄像头参数
            required_camera_params = ["width", "height", "fps", "quality"]
            for param in required_camera_params:
                if param not in camera_settings:
                    validation_result["errors"].append(f"缺少摄像头参数: {param}")
                    validation_result["valid"] = False
            
            # 检查参数范围
            if "quality" in camera_settings:
                quality = camera_settings["quality"]
                if not (1 <= quality <= 100):
                    validation_result["warnings"].append(f"图像质量超出范围 (1-100): {quality}")
            
            if "contrast" in camera_settings:
                contrast = camera_settings["contrast"]
                if not (0.5 <= contrast <= 3.0):
                    validation_result["warnings"].append(f"对比度超出范围 (0.5-3.0): {contrast}")
            
            if "saturation" in camera_settings:
                saturation = camera_settings["saturation"]
                if not (0.0 <= saturation <= 2.0):
                    validation_result["warnings"].append(f"饱和度超出范围 (0.0-2.0): {saturation}")
            
            if "sharpness" in camera_settings:
                sharpness = camera_settings["sharpness"]
                if not (0.0 <= sharpness <= 3.0):
                    validation_result["warnings"].append(f"锐化强度超出范围 (0.0-3.0): {sharpness}")
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"配置验证异常: {e}")
        
        return validation_result

    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager(config_path='{self.config_path}', sections={list(self.config.keys())})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 获取摄像头设置
    camera_settings = config_manager.get_camera_settings()
    print("当前摄像头设置:", camera_settings)
    
    # 更新摄像头设置
    config_manager.set_camera_settings({
        "width": 1920,
        "height": 1080,
        "contrast": 1.2,
        "sharpness": 0.8
    })
    
    # 获取配置信息
    config_info = config_manager.get_config_info()
    print("配置文件信息:", config_info)
    
    # 验证配置
    validation = config_manager.validate_config()
    print("配置验证结果:", validation)
