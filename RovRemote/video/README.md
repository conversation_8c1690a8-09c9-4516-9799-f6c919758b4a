# ROV 视频文件流功能

## 📁 目录说明

这个目录用于存放ROV系统的视频文件，支持将本地MP4视频文件作为MJPEG流源。

## 🎬 功能特点

### 核心功能
- ✅ **视频文件播放**: 使用本地MP4文件作为视频流源
- ✅ **循环播放**: 视频播放完毕后自动重新开始
- ✅ **分辨率适配**: 自动调整视频分辨率到摄像头设置
- ✅ **图像处理**: 应用对比度、饱和度、锐化等效果
- ✅ **信息覆盖**: 显示视频文件名、帧数、循环次数等信息
- ✅ **模式切换**: 支持实时切换摄像头和视频文件模式

### 技术特性
- ✅ **错误处理**: 文件不存在时自动回退到测试帧
- ✅ **帧率控制**: 根据摄像头设置控制播放帧率
- ✅ **资源管理**: 正确的视频文件打开和关闭
- ✅ **WebSocket控制**: 通过WebSocket命令控制模式切换

## 📋 使用方法

### 1. 准备视频文件
将MP4视频文件放置在此目录下，默认文件名为 `test.mp4`：
```bash
rovRemote/video/test.mp4
```

### 2. 支持的视频格式
- **格式**: MP4 (推荐)
- **编码**: H.264 (推荐)
- **分辨率**: 任意 (会自动调整)
- **帧率**: 任意

### 3. WebSocket命令控制

#### 切换到视频文件模式
```javascript
{
    "type": "command",
    "command": "switch_to_video_file",
    "video_path": "rovRemote/video/test.mp4",  // 可选，默认为test.mp4
    "timestamp": 1234567890
}
```

#### 切换回摄像头模式
```javascript
{
    "type": "command",
    "command": "switch_to_camera",
    "timestamp": 1234567890
}
```

#### 获取当前模式状态
```javascript
{
    "type": "command",
    "command": "get_video_mode_status",
    "timestamp": 1234567890
}
```

### 4. 程序接口调用

#### Python代码示例
```python
# 切换到视频文件模式
rov_server.switch_to_video_mode("rovRemote/video/test.mp4")

# 切换回摄像头模式
rov_server.switch_to_camera_mode()

# 检查当前模式
if rov_server.video_mode:
    print(f"当前使用视频文件: {rov_server.video_file_path}")
else:
    print("当前使用摄像头模式")
```

## 🔧 技术实现

### 核心函数

#### 1. generate_video_file_frames()
```python
def generate_video_file_frames(self, video_path=None):
    """使用本地视频文件生成MJPEG流"""
    # 1. 检查视频文件是否存在
    # 2. 打开视频文件
    # 3. 循环读取和处理视频帧
    # 4. 应用图像处理效果
    # 5. 添加信息覆盖层
    # 6. 编码为JPEG并yield
```

#### 2. add_video_info_overlay()
```python
def add_video_info_overlay(self, frame, frame_count, loop_count, video_path):
    """在视频帧上添加信息覆盖层"""
    # 添加视频文件名、帧数、时间戳等信息
```

#### 3. 模式切换函数
```python
def switch_to_video_mode(self, video_path=None):
    """切换到视频文件模式"""
    
def switch_to_camera_mode(self):
    """切换回摄像头模式"""
```

### 集成到视频流生成
```python
def generate_frames(self):
    """生成视频流 - 支持摄像头和视频文件模式"""
    if self.video_mode:
        yield from self.generate_video_file_frames(self.video_file_path)
        return
    
    # 摄像头模式处理...
```

## 📊 应用场景

### 1. ROV系统演示
- **展示功能**: 使用预录制的水下视频展示ROV功能
- **培训教学**: 用于操作员培训和教学演示
- **系统测试**: 测试图像处理和界面功能

### 2. 离线分析
- **视频回放**: 回放之前录制的ROV作业视频
- **数据分析**: 结合传感器数据进行离线分析
- **问题诊断**: 重现和分析系统问题

### 3. 开发调试
- **功能测试**: 在没有实际摄像头时测试系统功能
- **界面开发**: 开发和调试Web界面
- **算法验证**: 验证图像处理算法效果

## ⚠️ 注意事项

### 文件要求
1. **文件格式**: 确保视频文件为OpenCV支持的格式
2. **文件路径**: 使用相对于项目根目录的路径
3. **文件大小**: 注意视频文件大小，避免内存占用过大

### 性能考虑
1. **帧率控制**: 根据需要调整播放帧率
2. **分辨率**: 大分辨率视频会增加处理负担
3. **循环播放**: 长时间运行时注意内存使用

### 错误处理
1. **文件不存在**: 自动回退到测试帧生成
2. **格式不支持**: 检查视频文件格式
3. **读取失败**: 检查文件权限和完整性

## 🚀 扩展功能

### 可能的扩展
1. **多视频文件**: 支持播放列表和随机播放
2. **播放控制**: 暂停、快进、倒退功能
3. **实时切换**: 在播放过程中切换不同视频
4. **视频信息**: 显示更详细的视频信息
5. **播放统计**: 记录播放次数和时长

### 配置选项
```python
video_settings = {
    'loop_mode': True,          # 是否循环播放
    'auto_resize': True,        # 是否自动调整分辨率
    'show_overlay': True,       # 是否显示信息覆盖层
    'apply_effects': True,      # 是否应用图像处理效果
    'frame_rate_limit': 30      # 帧率限制
}
```

## 📝 示例视频

为了测试功能，您可以：

1. **下载测试视频**: 下载任意MP4格式的测试视频
2. **重命名文件**: 将文件重命名为 `test.mp4`
3. **放置文件**: 将文件放在 `rovRemote/video/` 目录下
4. **测试功能**: 使用测试页面或WebSocket命令测试

### 推荐的测试视频特征
- **时长**: 10-60秒 (便于观察循环效果)
- **内容**: 水下场景或ROV相关内容
- **分辨率**: 720p或1080p
- **帧率**: 30fps

---

**注意**: 请确保视频文件的版权合规，仅用于测试和开发目的。
