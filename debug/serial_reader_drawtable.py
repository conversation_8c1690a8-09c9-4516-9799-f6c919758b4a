#!/usr/bin/env python3
"""
优化的ROV串口数据实时监控系统
支持高频数据显示和性能调优
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from Serial.serial_port import SerialPort
import matplotlib.pyplot as plt
from matplotlib.widgets import Button
from datetime import datetime
import re
import threading
import time
from collections import deque
import numpy as np

# 导入配置
try:
    from monitor_config import get_config, apply_performance_mode
    CONFIG = get_config('balanced')  # 默认使用平衡模式
except ImportError:
    # 如果配置文件不存在，使用默认配置
    CONFIG = {
        'performance': {
            'max_data_points': 1000,
            'update_interval': 1/30,
            'processing_interval': 0.01,
            'window_size': 200,
            'line_width': 1.5,
            'alpha': 0.8,
            'dpi': 100,
        }
    }

# 优化的数据存储
MAX_DATA_POINTS = CONFIG['performance']['max_data_points']
print(f"*"*50+f"MAX_DATA_POINTS:{MAX_DATA_POINTS}")

time_data = deque(maxlen=MAX_DATA_POINTS)
data_store = {
    'PV': deque(maxlen=MAX_DATA_POINTS),
    'IV': deque(maxlen=MAX_DATA_POINTS),
    'DV': deque(maxlen=MAX_DATA_POINTS),
    'C': deque(maxlen=MAX_DATA_POINTS),
    'Angle': deque(maxlen=MAX_DATA_POINTS),
    'Err': deque(maxlen=MAX_DATA_POINTS),
    'Adjust': deque(maxlen=MAX_DATA_POINTS)
}

# 全局变量
axes = []
lines = {}
fig = None
data_lock = threading.Lock()
update_flag = threading.Event()
last_update_time = 0
UPDATE_INTERVAL = CONFIG['performance']['update_interval']

# 暂停/恢复控制
is_paused = False
pause_button = None

# 性能统计
performance_stats = {
    'frames': 0,
    'start_time': time.time(),
    'data_received': 0,
    'parse_errors': 0,
}

# 优化的数据解析函数
def parse_serial_data(data_str):
    """快速解析串口数据"""
    result = {}
    try:
        # 预编译正则表达式提高性能
        if not hasattr(parse_serial_data, 'pattern'):
            parse_serial_data.pattern = re.compile(r'(PV|IV|DV|C|Angle|Err|Adjust):(-?\d+\.?\d*)')

        matches = parse_serial_data.pattern.findall(data_str)
        for param, value in matches:
            try:
                if param == 'Angle':
                    result[param] = int(float(value))  # 先转float再转int，更安全
                elif param in ('C', 'Adjust', 'Err'):
                    result[param] = float(value)
                else:  # PV, IV, DV
                    result[param] = float(value)
            except ValueError:
                continue  # 跳过无效数据
    except Exception as e:
        print(f"解析数据错误: {e}")
    return result

# 暂停/恢复按钮回调函数
def toggle_pause(event):
    """切换暂停/恢复状态"""
    global is_paused, pause_button
    is_paused = not is_paused
    if is_paused:
        pause_button.label.set_text('恢复')
        pause_button.color = '#ff6b6b'  # 红色表示暂停
        print("📊 图表更新已暂停")
    else:
        pause_button.label.set_text('暂停')
        pause_button.color = '#4ecdc4'  # 绿色表示运行
        print("📊 图表更新已恢复")
    pause_button.ax.figure.canvas.draw_idle()

# 优化的图表初始化函数
def init_plot():
    """初始化高性能实时图表"""
    global fig, axes, lines, pause_button

    # 设置matplotlib后端和性能优化
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 黑体、微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.rcParams['figure.facecolor'] = 'white'
    plt.rcParams['axes.facecolor'] = 'white'
    plt.rcParams['figure.dpi'] = 100  # 降低DPI提高性能
    plt.ion()  # 启用交互式模式

    # 初始化lines字典
    lines = {}

    # 创建优化的图表布局
    fig = plt.figure(figsize=(14, 10))
    fig.suptitle('PID实时数据监控', fontsize=16, fontweight='bold')

    # 为按钮预留空间，调整布局
    gs = plt.GridSpec(4, 1, height_ratios=[2, 2, 2, 2], hspace=0.3, top=0.92)

    # 子图1: PV/IV/DV - 电压数据
    ax1 = plt.subplot(gs[0])
    line_pv, = ax1.plot([], [], '-', linewidth=1, label='PV', color='#CD0000', alpha=0.8)
    line_iv, = ax1.plot([], [], '-', linewidth=1, label='IV', color='#00CF29', alpha=0.8)
    line_dv, = ax1.plot([], [], '-', linewidth=1, label='DV', color='#4444FF', alpha=0.8)
    # ax1.set_ylim(-5, 5)


    # 子图4: Adjust - 调整量
    ax4 = plt.subplot(gs[1])
    line_adjust, = ax4.plot([], [], '-', linewidth=1, label='Adjust', color='#4444FF', alpha=0.8)
    # ax4.set_title('Adjust', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Adjust')
    ax4.set_xlabel('时间')
    # ax4.set_ylim(-6, 6)

    # 子图2: C和Err - 控制参数
    ax2 = plt.subplot(gs[2])
    line_c, = ax2.plot([], [], '-', linewidth=1, label='C', color="#4444FF", alpha=0.8)
    line_err, = ax2.plot([], [], '-', linewidth=1, label='Err', color="#CD0000", alpha=0.8)
    ax2.set_ylim(-1, 1)

    # 子图3: Angle - 角度数据
    ax3 = plt.subplot(gs[3])
    line_angle, = ax3.plot([], [], '-', linewidth=1, label='Angle', color='#4444FF', alpha=0.8)
    # ax3.set_title('角度数据 (°)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Angle (°)')
    ax3.set_ylim(-10, 190)



    axes = [ax1, ax2, ax3, ax4]

    # 存储所有线条引用
    lines.update({
        'PV': line_pv,
        'IV': line_iv,
        'DV': line_dv,
        'C': line_c,
        'Err': line_err,
        'Angle': line_angle,
        'Adjust': line_adjust
    })

    # 优化图表显示
    for i, ax in enumerate(axes):
        ax.grid(True, alpha=0.3, linestyle='--')
        ax.legend(loc='upper right', fontsize=10)

        # 优化坐标轴
        ax.tick_params(axis='both', which='major', labelsize=9)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        # 只在最后一个子图显示x轴标签
        if i < len(axes) - 1:
            ax.set_xticklabels([])

    # 添加暂停/恢复按钮
    button_ax = plt.axes([0.85, 0.95, 0.1, 0.04])  # [left, bottom, width, height]
    pause_button = Button(button_ax, '暂停', color='#4ecdc4', hovercolor='#45b7aa')
    pause_button.on_clicked(toggle_pause)

    # 启用blitting提高性能
    fig.canvas.draw()
    plt.tight_layout()

    return fig


# 优化的图表更新函数
def update_plot():
    """高性能图表更新函数"""
    global axes, lines, last_update_time, is_paused

    # 如果暂停，则不更新图表
    if is_paused:
        return

    current_time = time.time()

    # 限制更新频率，避免过度刷新
    if current_time - last_update_time < UPDATE_INTERVAL:
        return

    last_update_time = current_time

    with data_lock:
        if not time_data:
            return

        # 转换为numpy数组提高性能
        time_array = np.array([t.timestamp() for t in time_data])

        # 批量更新所有线条数据
        for param, line in lines.items():
            if data_store[param]:
                data_array = np.array(data_store[param])
                line.set_data(time_array, data_array)

        # 智能调整坐标轴范围
        if len(time_data) > 1:
            x_min, x_max = time_array[0], time_array[-1]

            # 只显示最近的数据窗口
            window_size = min(len(time_data),  CONFIG['performance']['window_size'])  # 显示最近200个点
            if len(time_data) > window_size:
                start_idx = len(time_data) - window_size
                x_min = time_array[start_idx]

            for ax in axes:
                ax.set_xlim(x_min, x_max)

                # 动态调整y轴范围（除了固定范围的子图）
                if ax != axes[1] and ax != axes[2] and ax != axes[3]:  # 不调整C/Err, Angle, Adjust的y轴
                    ax.relim()
                    ax.autoscale_view(scalex=False)  # 只自动缩放y轴

    # 强制刷新画布
    try:
        fig.canvas.draw_idle()
        fig.canvas.flush_events()
    except:
        pass
            

# 数据缓存
data_buffer = []
buffer_lock = threading.Lock()

def handle_serial_data(data: bytes):
    """优化的串口数据处理函数"""
    try:
        decoded = data.decode('utf-8').strip()

        # 批量处理数据，减少锁竞争
        with buffer_lock:
            data_buffer.append(decoded)

        # 设置更新标志
        update_flag.set()

    except UnicodeDecodeError:
        print(f"HEX: {data.hex().upper()}")

def process_data_buffer():
    """批量处理数据缓存"""
    global data_buffer

    with buffer_lock:
        if not data_buffer:
            return

        # 取出所有缓存数据
        current_buffer = data_buffer.copy()
        data_buffer.clear()

    # 批量解析数据
    for decoded in current_buffer:
        parsed = parse_serial_data(decoded)
        if parsed:
            current_time = datetime.now()

            with data_lock:
                time_data.append(current_time)

                # 确保所有参数数组保持相同长度
                for param in data_store:
                    # 使用当前解析值或保持最后一个有效值
                    if param in parsed:
                        data_store[param].append(parsed[param])
                    elif len(data_store[param]) > 0:
                        data_store[param].append(data_store[param][-1])
                    else:
                        data_store[param].append(0.0)  # 默认值

def data_processing_thread():
    """数据处理线程"""
    while True:
        # 等待数据更新信号
        update_flag.wait(timeout=0.1)
        update_flag.clear()

        # 处理数据缓存
        process_data_buffer()

        # 更新图表
        update_plot()

def main():
    """主程序入口"""
    print("=" * 60)
    print("ROV串口数据实时监控系统")
    print("=" * 60)

    # 串口配置
    serial_port = SerialPort("/dev/tty.usbserial-0001", 115200)

    if not serial_port.open(receive_callback=handle_serial_data, reboot=False):
        print("❌ 串口连接失败")
        return

    print("✅ 串口已连接，开始接收数据...")

    try:
        # 初始化图表
        print("🎨 初始化图表...")
        fig = init_plot()

        # 启动数据处理线程
        print("🚀 启动数据处理线程...")
        processing_thread = threading.Thread(target=data_processing_thread, daemon=True)
        processing_thread.start()

        # 显示图表
        plt.show(block=False)

        print("📊 实时监控已启动")
        print("按 Ctrl+C 退出程序")
        print("-" * 60)

        # 主循环 - 优化的事件处理
        frame_count = 0
        start_time = time.time()

        while True:
            try:
                # 高效的事件处理
                fig.canvas.flush_events()

                # 性能统计
                frame_count += 1
                if frame_count % 100 == 0:
                    elapsed = time.time() - start_time
                    fps = frame_count / elapsed
                    print(f"📈 性能统计: {fps:.1f} FPS, 数据点: {len(time_data)}")

                # 适当的休眠，避免CPU占用过高
                time.sleep(0.01)  # 10ms休眠，100Hz主循环

            except Exception as e:
                print(f"⚠️ 主循环异常: {e}")
                time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n🛑 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    finally:
        # 清理资源
        print("🧹 清理资源...")
        serial_port.close()
        plt.close('all')
        print("✅ 程序已退出")

if __name__ == "__main__":
    main()
