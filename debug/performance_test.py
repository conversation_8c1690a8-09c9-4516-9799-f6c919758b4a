#!/usr/bin/env python3
"""
串口监控系统性能测试
用于测试不同配置下的性能表现
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import time
import threading
import random
from collections import deque
import matplotlib.pyplot as plt
import numpy as np

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        self.test_results = {}
        
    def generate_test_data(self, duration=10, frequency=100):
        """生成测试数据"""
        data_points = []
        start_time = time.time()
        
        for i in range(int(duration * frequency)):
            timestamp = start_time + i / frequency
            
            # 模拟真实的ROV数据
            data = {
                'timestamp': timestamp,
                'PV': 12.0 + random.uniform(-0.5, 0.5),
                'IV': 5.0 + random.uniform(-0.2, 0.2),
                'DV': 3.3 + random.uniform(-0.1, 0.1),
                'C': random.uniform(0, 1),
                'Angle': random.randint(0, 180),
                'Err': random.uniform(0, 0.5),
                'Adjust': random.uniform(-2, 2)
            }
            data_points.append(data)
            
        return data_points
    
    def test_data_structure_performance(self):
        """测试数据结构性能"""
        print("测试数据结构性能...")
        
        test_sizes = [100, 500, 1000, 2000, 5000]
        results = {}
        
        for size in test_sizes:
            print(f"  测试大小: {size}")
            
            # 测试list
            start_time = time.time()
            data_list = []
            for i in range(size):
                data_list.append(random.random())
                if len(data_list) > size // 2:
                    data_list.pop(0)
            list_time = time.time() - start_time
            
            # 测试deque
            start_time = time.time()
            data_deque = deque(maxlen=size // 2)
            for i in range(size):
                data_deque.append(random.random())
            deque_time = time.time() - start_time
            
            results[size] = {
                'list': list_time,
                'deque': deque_time,
                'speedup': list_time / deque_time if deque_time > 0 else float('inf')
            }
            
            print(f"    List: {list_time:.4f}s, Deque: {deque_time:.4f}s, 加速: {results[size]['speedup']:.2f}x")
        
        return results
    
    def test_plotting_performance(self):
        """测试绘图性能"""
        print("测试绘图性能...")
        
        # 生成测试数据
        test_data = self.generate_test_data(duration=5, frequency=50)
        
        # 测试不同的绘图方法
        methods = {
            'basic_plot': self._test_basic_plot,
            'optimized_plot': self._test_optimized_plot,
            'batch_update': self._test_batch_update
        }
        
        results = {}
        
        for method_name, method_func in methods.items():
            print(f"  测试方法: {method_name}")
            
            start_time = time.time()
            fps = method_func(test_data)
            total_time = time.time() - start_time
            
            results[method_name] = {
                'total_time': total_time,
                'fps': fps,
                'efficiency': fps / total_time if total_time > 0 else 0
            }
            
            print(f"    总时间: {total_time:.2f}s, FPS: {fps:.1f}, 效率: {results[method_name]['efficiency']:.1f}")
        
        return results
    
    def _test_basic_plot(self, test_data):
        """基础绘图测试"""
        fig, ax = plt.subplots()
        line, = ax.plot([], [], '-')
        
        frame_count = 0
        start_time = time.time()
        
        for i, data in enumerate(test_data):
            if i % 5 == 0:  # 每5个数据点更新一次
                x_data = [d['timestamp'] for d in test_data[:i+1]]
                y_data = [d['PV'] for d in test_data[:i+1]]
                
                line.set_data(x_data, y_data)
                ax.relim()
                ax.autoscale_view()
                
                plt.draw()
                plt.pause(0.001)
                
                frame_count += 1
        
        elapsed = time.time() - start_time
        fps = frame_count / elapsed if elapsed > 0 else 0
        
        plt.close(fig)
        return fps
    
    def _test_optimized_plot(self, test_data):
        """优化绘图测试"""
        fig, ax = plt.subplots()
        line, = ax.plot([], [], '-')
        
        # 预分配数组
        max_points = len(test_data)
        x_array = np.zeros(max_points)
        y_array = np.zeros(max_points)
        
        frame_count = 0
        start_time = time.time()
        
        for i, data in enumerate(test_data):
            x_array[i] = data['timestamp']
            y_array[i] = data['PV']
            
            if i % 5 == 0:  # 每5个数据点更新一次
                line.set_data(x_array[:i+1], y_array[:i+1])
                ax.set_xlim(x_array[0], x_array[i])
                ax.set_ylim(np.min(y_array[:i+1]), np.max(y_array[:i+1]))
                
                fig.canvas.draw_idle()
                fig.canvas.flush_events()
                
                frame_count += 1
        
        elapsed = time.time() - start_time
        fps = frame_count / elapsed if elapsed > 0 else 0
        
        plt.close(fig)
        return fps
    
    def _test_batch_update(self, test_data):
        """批量更新测试"""
        fig, ax = plt.subplots()
        line, = ax.plot([], [], '-')
        
        batch_size = 10
        frame_count = 0
        start_time = time.time()
        
        for i in range(0, len(test_data), batch_size):
            batch = test_data[i:i+batch_size]
            
            x_data = [d['timestamp'] for d in test_data[:i+len(batch)]]
            y_data = [d['PV'] for d in test_data[:i+len(batch)]]
            
            line.set_data(x_data, y_data)
            ax.relim()
            ax.autoscale_view()
            
            fig.canvas.draw_idle()
            fig.canvas.flush_events()
            
            frame_count += 1
        
        elapsed = time.time() - start_time
        fps = frame_count / elapsed if elapsed > 0 else 0
        
        plt.close(fig)
        return fps
    
    def test_threading_performance(self):
        """测试多线程性能"""
        print("测试多线程性能...")
        
        # 生成测试数据
        test_data = self.generate_test_data(duration=3, frequency=100)
        
        results = {}
        
        # 单线程测试
        start_time = time.time()
        processed_data = []
        for data in test_data:
            # 模拟数据处理
            processed = {k: v * 1.1 for k, v in data.items() if isinstance(v, (int, float))}
            processed_data.append(processed)
        single_thread_time = time.time() - start_time
        
        # 多线程测试
        start_time = time.time()
        processed_data_mt = []
        data_lock = threading.Lock()
        
        def process_batch(batch):
            local_processed = []
            for data in batch:
                processed = {k: v * 1.1 for k, v in data.items() if isinstance(v, (int, float))}
                local_processed.append(processed)
            
            with data_lock:
                processed_data_mt.extend(local_processed)
        
        # 分批处理
        batch_size = len(test_data) // 4
        threads = []
        
        for i in range(0, len(test_data), batch_size):
            batch = test_data[i:i+batch_size]
            thread = threading.Thread(target=process_batch, args=(batch,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        multi_thread_time = time.time() - start_time
        
        results = {
            'single_thread': single_thread_time,
            'multi_thread': multi_thread_time,
            'speedup': single_thread_time / multi_thread_time if multi_thread_time > 0 else float('inf')
        }
        
        print(f"  单线程: {single_thread_time:.4f}s")
        print(f"  多线程: {multi_thread_time:.4f}s")
        print(f"  加速比: {results['speedup']:.2f}x")
        
        return results
    
    def run_all_tests(self):
        """运行所有性能测试"""
        print("=" * 60)
        print("ROV串口监控系统性能测试")
        print("=" * 60)
        
        # 数据结构测试
        data_structure_results = self.test_data_structure_performance()
        print()
        
        # 绘图性能测试
        plotting_results = self.test_plotting_performance()
        print()
        
        # 多线程性能测试
        threading_results = self.test_threading_performance()
        print()
        
        # 汇总结果
        print("=" * 60)
        print("性能测试总结")
        print("=" * 60)
        
        print("数据结构性能 (推荐使用deque):")
        for size, result in data_structure_results.items():
            print(f"  {size}点: deque比list快 {result['speedup']:.2f}倍")
        
        print("\n绘图性能 (推荐使用优化方法):")
        best_method = max(plotting_results.items(), key=lambda x: x[1]['fps'])
        print(f"  最佳方法: {best_method[0]} ({best_method[1]['fps']:.1f} FPS)")
        
        print(f"\n多线程性能:")
        print(f"  多线程比单线程快 {threading_results['speedup']:.2f}倍")
        
        return {
            'data_structure': data_structure_results,
            'plotting': plotting_results,
            'threading': threading_results
        }

def main():
    """主程序"""
    tester = PerformanceTester()
    results = tester.run_all_tests()
    
    print("\n建议的优化配置:")
    print("- 使用deque代替list存储数据")
    print("- 使用numpy数组进行批量计算")
    print("- 限制更新频率到30-60 FPS")
    print("- 使用多线程分离数据处理和显示")
    print("- 限制显示的数据点数量")

if __name__ == "__main__":
    main()
