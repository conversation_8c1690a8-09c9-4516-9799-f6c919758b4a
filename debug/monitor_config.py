#!/usr/bin/env python3
"""
串口监控系统配置文件
用于调整性能参数和显示设置
"""

# 性能配置
PERFORMANCE_CONFIG = {
    # 数据缓存设置
    'max_data_points': 50000,        # 最大数据点数
    'update_interval': 1/30,        # 更新间隔 (秒) - 30 FPS
    'processing_interval': 0.01,    # 数据处理间隔 (秒) - 100Hz
    
    # 显示优化
    'window_size': 200,             # 显示窗口大小（数据点数）
    'line_width': 1,              # 线条宽度
    'alpha': 0.8,                   # 透明度
    'dpi': 100,                     # 图表DPI
    
    # 内存管理
    'buffer_size': 2500,             # 数据缓存大小
    'cleanup_interval': 2500,       # 清理间隔（帧数）
}

# 性能模式配置
PERFORMANCE_MODES = {
    'high_performance': {
        'max_data_points': 50000,
        'update_interval': 1/60,    # 60 FPS
        'processing_interval': 0.005,  # 200Hz
        'window_size': 100,
        'dpi': 80,
    },
    'balanced': {
        'max_data_points': 5000,
        'update_interval': 1/30,    # 30 FPS
        'processing_interval': 0.01,   # 100Hz
        'window_size': 1250,
        'dpi': 100,
    },
    'high_quality': {
        'max_data_points': 2000,
        'update_interval': 1/20,    # 20 FPS
        'processing_interval': 0.02,   # 50Hz
        'window_size': 500,
        'dpi': 120,
    }
}

# 串口配置
SERIAL_CONFIG = {
    'port': '/dev/tty.usbserial-0001',
    'baudrate': 115200,
    'timeout': 1.0,
    'reconnect_attempts': 3,
    'reconnect_delay': 2.0,
}

# 图表配置
PLOT_CONFIG = {
    'figure_size': (14, 10),
    'title': 'ROV实时数据监控',
    'title_fontsize': 16,
    'subplot_fontsize': 12,
    'legend_fontsize': 10,
    'tick_fontsize': 9,
    
    # 子图配置
    'subplots': [
        {
            'title': '电压数据 (V)',
            'params': ['PV', 'IV', 'DV'],
            'colors': ['#FF4444', '#44FF44', '#4444FF'],
            'ylabel': '电压 (V)',
            'auto_scale': True,
        },
        {
            'title': '控制参数',
            'params': ['C', 'Err'],
            'colors': ['#FF8800', '#FF0088'],
            'ylabel': '数值',
            'ylim': (-0.1, 1.1),
            'auto_scale': False,
        },
        {
            'title': '角度数据 (°)',
            'params': ['Angle'],
            'colors': ['#8800FF'],
            'ylabel': '角度 (°)',
            'ylim': (-10, 190),
            'auto_scale': False,
        },
        {
            'title': '调整量',
            'params': ['Adjust'],
            'colors': ['#00FF88'],
            'ylabel': '调整量',
            'ylim': (-6, 6),
            'auto_scale': False,
        }
    ]
}

# 数据解析配置
PARSE_CONFIG = {
    'pattern': r'(PV|IV|DV|C|Angle|Err|Adjust):(-?\d+\.?\d*)',
    'param_types': {
        'PV': float,
        'IV': float,
        'DV': float,
        'C': float,
        'Angle': int,
        'Err': float,
        'Adjust': float,
    },
    'default_values': {
        'PV': 0.0,
        'IV': 0.0,
        'DV': 0.0,
        'C': 0.0,
        'Angle': 0,
        'Err': 0.0,
        'Adjust': 0.0,
    }
}



# 调试配置
DEBUG_CONFIG = {
    'enable_debug': False,
    'log_level': 'INFO',
    'performance_stats': True,
    'show_fps': True,
    'show_data_count': True,
    'print_raw_data': False,
    'print_parsed_data': False,
}

def get_config(mode='balanced'):
    """获取指定模式的配置"""
    config = {
        'performance': PERFORMANCE_CONFIG.copy(),
        'serial': SERIAL_CONFIG.copy(),
        'plot': PLOT_CONFIG.copy(),
        'parse': PARSE_CONFIG.copy(),
        'debug': DEBUG_CONFIG.copy(),
    }
    
    # 应用性能模式
    if mode in PERFORMANCE_MODES:
        config['performance'].update(PERFORMANCE_MODES[mode])
    
    return config

def apply_performance_mode(mode):
    """应用性能模式"""
    if mode not in PERFORMANCE_MODES:
        print(f"警告: 未知的性能模式 '{mode}'，使用默认模式 'balanced'")
        mode = 'balanced'
    
    mode_config = PERFORMANCE_MODES[mode]
    PERFORMANCE_CONFIG.update(mode_config)
    
    print(f"✅ 已应用性能模式: {mode}")
    print(f"   - 最大数据点: {mode_config['max_data_points']}")
    print(f"   - 更新频率: {1/mode_config['update_interval']:.0f} FPS")
    print(f"   - 处理频率: {1/mode_config['processing_interval']:.0f} Hz")
    print(f"   - 显示窗口: {mode_config['window_size']} 点")

# 使用示例
if __name__ == "__main__":
    print("串口监控系统配置")
    print("=" * 40)
    
    # 显示所有性能模式
    print("可用的性能模式:")
    for mode, config in PERFORMANCE_MODES.items():
        print(f"  {mode}:")
        print(f"    - FPS: {1/config['update_interval']:.0f}")
        print(f"    - 数据点: {config['max_data_points']}")
        print(f"    - 窗口大小: {config['window_size']}")
    
    print("\n当前配置:")
    config = get_config('balanced')
    print(f"  性能模式: balanced")
    print(f"  串口: {config['serial']['port']} @ {config['serial']['baudrate']}")
    print(f"  更新频率: {1/config['performance']['update_interval']:.0f} FPS")
    print(f"  最大数据点: {config['performance']['max_data_points']}")
