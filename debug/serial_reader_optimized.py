#!/usr/bin/env python3
"""
高性能串口数据实时监控系统
专门优化用于高频数据显示
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from Serial.serial_port import SerialPort
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk
from datetime import datetime
import re
import threading
import time
from collections import deque
import numpy as np

class HighPerformanceMonitor:
    """高性能串口数据监控器"""
    
    def __init__(self, max_points=500):
        self.max_points = max_points
        self.data_lock = threading.Lock()
        
        # 数据存储
        self.time_data = deque(maxlen=max_points)
        self.data_store = {
            'PV': deque(maxlen=max_points),
            'IV': deque(maxlen=max_points),
            'DV': deque(maxlen=max_points),
            'C': deque(maxlen=max_points),
            'Angle': deque(maxlen=max_points),
            'Err': deque(maxlen=max_points),
            'Adjust': deque(maxlen=max_points)
        }
        
        # 性能统计
        self.frame_count = 0
        self.start_time = time.time()
        self.last_update = 0
        self.update_interval = 1/30  # 30 FPS
        
        # 数据缓存
        self.data_buffer = []
        self.buffer_lock = threading.Lock()
        
        # 预编译正则表达式
        self.pattern = re.compile(r'(PV|IV|DV|C|Angle|Err|Adjust):(-?\d+\.?\d*)')
        
        self.setup_gui()
        
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("ROV高性能数据监控")
        self.root.geometry("1200x800")
        
        # 创建控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(control_frame, text="状态: 未连接")
        self.status_label.pack(side=tk.LEFT)
        
        # FPS显示
        self.fps_label = ttk.Label(control_frame, text="FPS: 0")
        self.fps_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 数据点数显示
        self.points_label = ttk.Label(control_frame, text="数据点: 0")
        self.points_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 创建matplotlib图表
        self.setup_plots()
        
    def setup_plots(self):
        """设置高性能图表"""
        # 创建图表
        self.fig, self.axes = plt.subplots(4, 1, figsize=(12, 8))
        self.fig.suptitle('ROV实时数据监控 - 高性能版', fontsize=14)
        
        # 配置子图
        plot_configs = [
            {'title': '电压数据 (V)', 'params': ['PV', 'IV', 'DV'], 'colors': ['#FF4444', '#44FF44', '#4444FF']},
            {'title': '控制参数', 'params': ['C', 'Err'], 'colors': ['#FF8800', '#FF0088'], 'ylim': (-0.1, 1.1)},
            {'title': '角度 (°)', 'params': ['Angle'], 'colors': ['#8800FF'], 'ylim': (-10, 190)},
            {'title': '调整量', 'params': ['Adjust'], 'colors': ['#00FF88'], 'ylim': (-6, 6)}
        ]
        
        self.lines = {}
        
        for i, (ax, config) in enumerate(zip(self.axes, plot_configs)):
            ax.set_title(config['title'], fontsize=12)
            ax.grid(True, alpha=0.3)
            
            for param, color in zip(config['params'], config['colors']):
                line, = ax.plot([], [], '-', linewidth=1.5, label=param, color=color, alpha=0.8)
                self.lines[param] = line
            
            ax.legend(loc='upper right', fontsize=10)
            
            if 'ylim' in config:
                ax.set_ylim(config['ylim'])
            
            if i < len(self.axes) - 1:
                ax.set_xticklabels([])
        
        self.axes[-1].set_xlabel('时间')
        
        # 嵌入到tkinter
        canvas_frame = ttk.Frame(self.root)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.canvas = FigureCanvasTkAgg(self.fig, canvas_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # plt.tight_layout()
        
    def parse_data(self, data_str):
        """快速数据解析"""
        result = {}
        try:
            matches = self.pattern.findall(data_str)
            for param, value in matches:
                try:
                    if param == 'Angle':
                        result[param] = int(float(value))
                    else:
                        result[param] = float(value)
                except ValueError:
                    continue
        except Exception:
            pass
        return result
    
    def handle_serial_data(self, data: bytes):
        """串口数据处理"""
        try:
            decoded = data.decode('utf-8').strip()
            with self.buffer_lock:
                self.data_buffer.append(decoded)
        except UnicodeDecodeError:
            pass
    
    def process_data(self):
        """处理数据缓存"""
        with self.buffer_lock:
            if not self.data_buffer:
                return
            current_buffer = self.data_buffer.copy()
            self.data_buffer.clear()
        
        current_time = time.time()
        
        for decoded in current_buffer:
            parsed = self.parse_data(decoded)
            if parsed:
                with self.data_lock:
                    self.time_data.append(current_time)
                    
                    for param in self.data_store:
                        if param in parsed:
                            self.data_store[param].append(parsed[param])
                        elif len(self.data_store[param]) > 0:
                            self.data_store[param].append(self.data_store[param][-1])
                        else:
                            self.data_store[param].append(0.0)
    
    def update_plots(self):
        """更新图表"""
        current_time = time.time()
        
        # 限制更新频率
        if current_time - self.last_update < self.update_interval:
            return
        
        self.last_update = current_time
        
        with self.data_lock:
            if not self.time_data:
                return
            
            # 转换为numpy数组
            time_array = np.array(list(self.time_data))
            
            # 更新所有线条
            for param, line in self.lines.items():
                if self.data_store[param]:
                    data_array = np.array(list(self.data_store[param]))
                    line.set_data(time_array, data_array)
            
            # 调整x轴范围
            if len(time_array) > 1:
                x_min, x_max = time_array[0], time_array[-1]
                for ax in self.axes:
                    ax.set_xlim(x_min, x_max)
                    
                    # 动态调整y轴（除了固定范围的）
                    if ax == self.axes[0]:  # 电压数据
                        ax.relim()
                        ax.autoscale_view(scalex=False)
        
        # 刷新画布
        try:
            self.canvas.draw_idle()
        except:
            pass
        
        # 更新性能统计
        self.frame_count += 1
        if self.frame_count % 30 == 0:
            elapsed = current_time - self.start_time
            fps = self.frame_count / elapsed
            
            self.root.after(0, lambda: self.fps_label.config(text=f"FPS: {fps:.1f}"))
            self.root.after(0, lambda: self.points_label.config(text=f"数据点: {len(self.time_data)}"))
    
    def data_thread(self):
        """数据处理线程"""
        while True:
            self.process_data()
            self.update_plots()
            time.sleep(0.01)  # 100Hz处理频率
    
    def start_monitoring(self, port="/dev/tty.usbserial-0001", baudrate=115200):
        """开始监控"""
        self.serial_port = SerialPort(port, baudrate)
        
        if self.serial_port.open(receive_callback=self.handle_serial_data, reboot=False):
            self.status_label.config(text="状态: 已连接")
            
            # 启动数据处理线程
            self.thread = threading.Thread(target=self.data_thread, daemon=True)
            self.thread.start()
            
            print("✅ 高性能监控已启动")
            return True
        else:
            self.status_label.config(text="状态: 连接失败")
            print("❌ 串口连接失败")
            return False
    
    def run(self):
        """运行主循环"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n🛑 用户中断")
        finally:
            if hasattr(self, 'serial_port'):
                self.serial_port.close()
            print("✅ 程序已退出")

def main():
    """主程序"""
    print("=" * 60)
    print("ROV高性能串口数据监控系统")
    print("=" * 60)
    
    monitor = HighPerformanceMonitor(max_points=1000)
    
    if monitor.start_monitoring():
        monitor.run()

if __name__ == "__main__":
    main()
