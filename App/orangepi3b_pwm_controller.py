import time

class PWMController:
    def __init__(self, pwm_chip=0, channel=0):
        print("初始化PWM控制器")
        self.pwm_path = f"/sys/class/pwm/pwmchip{pwm_chip}/pwm{channel}"
        self._setup_pwm()
    
    def _setup_pwm(self):
        print("配置PWM参数")
        try:
            with open(f"{self.pwm_path.replace('pwm0', 'unexport')}", "w") as f:
                f.write("0")
        except:
            pass
        
        with open(f"{self.pwm_path.replace('pwm0', 'export')}", "w") as f:
            f.write("0")
        time.sleep(0.2)
        
        # 设置50Hz频率(周期20ms)
        with open(f"{self.pwm_path}/period", "w") as f:
            f.write("20000000")  # 20ms in ns
        
        # 初始1.5ms脉宽
        self.set_pulse_width(1500)
        
        # 启用PWM
        with open(f"{self.pwm_path}/enable", "w") as f:
            f.write("1")
    
    def set_pulse_width(self, us):
         # 读取当前周期
        with open(f"{self.pwm_path}/period", "r") as f:
            period_ns = int(f.read().strip())
        
        # 计算占空比纳秒值
        duty_ns = int(us * 1000)
        duty_ns = period_ns - duty_ns

        # print("设置脉冲宽度(微秒)")
        # duty_ns = us * 1000  # 转换为纳秒
        with open(f"{self.pwm_path}/duty_cycle", "w") as f:
            f.write(str(duty_ns))
    
    def cleanup(self):
        print("清理资源")
        with open(f"{self.pwm_path}/enable", "w") as f:
            f.write("0")
        with open(f"{self.pwm_path.replace('pwm0', 'unexport')}", "w") as f:
            f.write("0")
