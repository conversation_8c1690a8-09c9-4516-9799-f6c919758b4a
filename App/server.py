#!/usr/bin/env python3
"""
ROV Web控制服务器
提供WebSocket通信、视频流转发、传感器数据处理
V2
"""

import asyncio
import math

import websockets
import json
import time
import threading
import struct
from flask import Flask, Response, render_template, send_from_directory
import sys
import os
import pygame
import logging

from pygame import joystick

from colorama import Fore,init,Style,Back
init(autoreset=True)

from orangepi3b_pwm_controller import PWMController

import queue

print(Fore.GREEN+"\n\n\n--------------------ROV APP SERVER Starting------------------\n")

MAC_DEBUG=False
MAC_DEBUG=True

baudrate=115200

if MAC_DEBUG:
    # 测试
    camera_index=1
    
    # 开发板的串口通过Zero3网络转换
    serial_port_str="/dev/tty.wchusbserial599D0149021"
    
    #单线串口板
    # serial_port_str ='/dev/tty.wchusbserial5A680119801'

    # 开发板
    # serial_port_str="/dev/tty.wchusbserial539E0045951"

    # 转换板
    # serial_port_str'/dev/tty.usbserial-0001'
    
    # orangepi_server_url = "ws://orangepi3b.local:8766"
    
    # orangepi_server_url = "ws://orangepizero3.local:8765"
    
    # orangepi_server_url = "ws://orangepi3b.local:8765"
    
    orangepi_server_url = "ws://localhost:8765"
    
    # orangepi_server_url = "ws://*************:8799"
    
else:
    # OrangePi3b
    camera_index=0
    
    serial_port_str="/dev/ttyACM0"
    
    orangepi_server_url = "ws://***********:8765"


def map_value(value, in_min, in_max, out_min, out_max):
    """将输入值从一个范围映射到另一个范围"""
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

# 添加父目录到路径，以便导入App模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from Serial.serial_port import SerialPort
except ImportError:
    print(Fore.RED+f"警告: 无法导入串口模块")
    SerialPort = None
    # while(1):pass

# 导入Xbox手柄控制器
try:
    # sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'joystick'))
    from joystick.xbox_controller import XboxController
    XBOX_CONTROLLER_AVAILABLE = True
    print("Xbox手柄控制器已加载")
except ImportError:
    print("警告: 无法导入Xbox手柄控制器，手柄功能不可用")
    XboxController = None
    XBOX_CONTROLLER_AVAILABLE = False

class ROVWebServer:
    def __init__(self):
        print("ROVWebServer init")
        self.clients = set()
        self.serial_port = None
        self.xbox_controller = None
        self.controller_thread = None
        self.controller_running = False

        self.is_underwater=False

        self.esp32_init=False
        
        self.aera=0.05

        # WebSocket客户端相关
        self.client_websocket = None
        self.client_connected = False
        
        global orangepi_server_url
        self.orangepi_server_url = orangepi_server_url
           
            
        self.client_reconnect_interval = 3  # 重连间隔（秒）
        self.client_running = True
        self.client_thread = None


        # 串口延迟计时
        self.serial_send_time = None
        self.serial_receive_time=0

        # 串口数据缓冲区，用于帧头对齐
        self.serial_buffer = bytearray()
        
        self.is_recording = False
        self.camera = None
        
        self.sensor_data = {}
        
        # 水面临界数值
        self.surface_pressure=0
        
        self.send_joystick_data_lasttime=0
        
        self.serial_response_dispaly_lasttime=0
        
        self.has_other_commond=False
        
        self.serial_commond_list=[]
        
        
        # self.setup_serial()
        self.setup_xbox_controller()
        
        self._send_queue = queue.Queue(maxsize=1)  # 限制队列大小防止内存溢出
        
        self.joystick_data={
                    "lX": 0,    # 左摇杆X轴 (左右)
                    "lY": 0,    # 左摇杆Y轴 (前后)
                    "rX": 0,   # 右摇杆X轴 (左右)
                    "rY": 0    # 右摇杆Y轴 (前后)
                }
        
        # self.send_interval = 0.015  # 10ms发送间隔，100Hz频率
        self.joystick_send_interval = 0.02  # 0.02 10ms发送间隔，50Hz频率
        
    def calculate_crc8(self, data):
        """计算CRC8校验码，遵循ROV协议标准"""
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc


    def setup_xbox_controller(self):
        """初始化Xbox控制器"""
        if not XBOX_CONTROLLER_AVAILABLE:
            print("Xbox控制器不可用，跳过初始化")
            return

        try:
            # 使用新的基于pygame事件系统的Xbox控制器
            self.xbox_controller = XboxController(wait_for_controller=False)
            print("\n------Xbox控制器初始化完成")
            
            self.sensor_data['joystickStatus'] = 1

            # 启动控制器监控线程
            self.xbox_controller_start_loop()

        except Exception as e:
            print(f"Xbox控制器初始化失败: {e}")
            self.xbox_controller = None
            self.sensor_data['joystickStatus'] = 0

           
    def xbox_controller_start_loop(self):
         # 启动控制器监控线程
        self.controller_running = True
        self.controller_thread = threading.Thread(target=self.xbox_controller_loop, daemon=True)
        self.controller_thread.start()
   
    def xbox_controller_loop(self):
        # [ ]!!!Xbox控制器主循环
        print("Xbox控制器主循环")
        last_send_time = 0
        last_connection_status = None

        while self.controller_running:
            try:
                # # 构建发送给下位机的消息
                # message = {
                #     'type': 'command',
                #     'command': 'ping',
                #     'data': {},
                #     'timestamp': time.time(),
                #     'start_timestamp': time.time()
                # }
           
                # if self._send_queue.qsize()<2:
                #     # print(Fore.WHITE+f"Add to queue[{self._send_queue.qsize()}]: {message}")
                #     self.send_to_orangepi_sync(message)
                
                # time.sleep(0.01)
                
                if self.xbox_controller is None:
                    time.sleep(1)
                    continue

                # self.xbox_controller.check_connection()

                # 更新控制器状态（内置重连功能会自动处理断开重连）
                self.xbox_controller.update()
                
                # print(self.xbox_controller._button_states)
               

                # 检查连接状态变化
                current_status = self.xbox_controller.is_controller_connected()
                if current_status != last_connection_status:
                    if current_status:
                        self.sensor_data['joystickStatus']=1
                        print("✅ Xbox控制器已连接Server")
                    else:
                        self.sensor_data['joystickStatus'] = 0
                        print(Fore.RED+"❌ Xbox控制器已断开，自动重连中...Server")
                        
                    last_connection_status = current_status

                # 只有在连接时才发送数据
                if current_status:
                    current_time = time.time()
                    if current_time - last_send_time >= self.joystick_send_interval:
                        
                        # 获取摇杆数据
                        left_x, left_y = self.xbox_controller.get_left_stick()
                        right_x, right_y = self.xbox_controller.get_right_stick()

                        if abs(left_x)<self.aera:
                            left_x=0
                        if abs(left_y)<self.aera:
                            left_y=0
                        if abs(right_x)<self.aera:
                            right_x=0
                        if abs(right_y)<self.aera:
                            right_y=0
                        
                        # if MAC_DEBUG:
                        #     # 测试舵机
                        #     if left_y>0.68:
                        #         left_y=0.68
                        
                        self.joystick_data.update({
                            "XBOX":0,
                            "A": 0,    
                            "B": 0,   
                            "X": 0,  
                            "Y": 0,
                            "LB":0,
                            "RB":0,
                            "VIEW":0,
                            "MENU":0,
                            "LEFT_STICK":0,
                            "RIGHT_STICK":0,
                            "REC":0
                        })  
                       
                        if MAC_DEBUG:
                            self.joystick_data.update({
                                "UP":0,
                                "DOWN":0,
                                "LEFT":0,
                                "RIGHT":0
                            })
                            
                        # print(self.xbox_controller._hat_states["DPAD"][0]) #左-1 右1
                        # print(self.xbox_controller._hat_states["DPAD"][1]) #上-1 下1
                        
                        if not MAC_DEBUG:
                            # if self.xbox_controller._hat_states["DPAD"][0]==-1:
                            #     self.joystick_data.update({"LEFT": 1})
                            # if self.xbox_controller._hat_states["DPAD"][0]==1:
                            #     self.joystick_data.update({"RIGHT": 1})
                            
                            #手柄按键 右
                            if self.xbox_controller._hat_states["DPAD"][0]==1:
                                if self.joystick_data.get("RIGHT",0)==1:
                                    self.joystick_data.update({"RIGHT": 2})
                                    
                                if self.joystick_data.get("RIGHT",0)==0:
                                    self.joystick_data.update({"RIGHT": 1})
                            else:
                                self.joystick_data.update({"RIGHT": 0})
                            
                            # 左    
                            if self.xbox_controller._hat_states["DPAD"][0]==-1:
                                if self.joystick_data.get("LEFT",0)==1:
                                    self.joystick_data.update({"LEFT": 2})
                                    
                                if self.joystick_data.get("LEFT",0)==0:
                                    self.joystick_data.update({"LEFT": 1})
                            else:
                                self.joystick_data.update({"LEFT": 0})
                                
                            #手柄按键 下 
                            if self.xbox_controller._hat_states["DPAD"][1]==-1:
                                if self.joystick_data.get("DOWN",0)==1:
                                    self.joystick_data.update({"DOWN": 2})
                                    
                                if self.joystick_data.get("DOWN",0)==0:
                                    self.joystick_data.update({"DOWN": 1})
                                
                            else:
                                self.joystick_data.update({"DOWN": 0})
                                
                            #手柄按键 上
                            if self.xbox_controller._hat_states["DPAD"][1]==1:
                                # print(self.joystick_data.get("UP"), self.xbox_controller._hat_states["DPAD"][1])
                                if self.joystick_data.get("UP",0)==1:
                                    self.joystick_data.update({"UP": 2})
                                    
                                if self.joystick_data.get("UP",0)==0:
                                    self.joystick_data.update({"UP": 1})
                            else:
                                self.joystick_data.update({"UP": 0})
                                
                                 
                        
                        # 处理pygame事件
                        for event in pygame.event.get():
                            # 按钮按下事件
                            if event.type == pygame.JOYBUTTONUP:
                                button_id = event.button
                                button_name = None
                                for name, id in self.xbox_controller.BUTTON_MAPPING.items():
                                    if id == button_id:
                                        button_name = name
                                        break
                                
                                if button_name:
                                    print(Fore.GREEN+f"按钮释放: {button_name} (按钮{button_id})")
                                    self.joystick_data.update({button_name: 2})
                                    
                            if event.type == pygame.JOYBUTTONDOWN:
                                button_id = event.button
                                button_name = None
                                for name, id in self.xbox_controller.BUTTON_MAPPING.items():
                                    if id == button_id:
                                        button_name = name
                                        break
                                    
                                if button_name:
                                    print(Fore.YELLOW+f"按钮按下: {button_name} (按钮{button_id})")
                                    self.joystick_data.update({button_name: 1})
                                    
                                
                        # 发送控制数据到Zero3
                        if len(self.serial_commond_list)==0:
                            self.send_joystick_data(left_x, left_y, right_x, right_y)
                            last_send_time = current_time
                        else:
                            print(Fore.RED+"❌ 有其他命令，跳过发送摇杆数据")
                            print(self.serial_commond_list[0])
                            self.send_command(self.serial_commond_list[0][0], self.serial_commond_list[0][1])
                            self.serial_commond_list.pop(0)

                        # print(self.joystick_data)

                        # 处理按钮输入
                        # button_A = self.xbox_controller.get_button("A")
                        
                        # 震动测试
                        # self.xbox_controller.rumble(0.9, 0.9, 1000)
                                
                        # if self.xbox_controller.get_button("UP"):
                        #     print('button_name UP 1')
                                    
                        # 低电压震动提醒
                        # if self.sensor_data["adcVoltage"] < 7.4:
                            # try:
                            #     self.xbox_controller.rumble(0, 0.9, 20)
                            # except:
                            #     pass  # 忽略震动错误
                        
                        if not MAC_DEBUG:
                            #[ ]线轴电机控制黄线第2排从左数第4个）pwm2, 轴白线线轴（第1排从右数第5个）pwm1
                            
                            pwm1_value=1500
                            if self.xbox_controller.get_button("MENU"):
                                # 解决绕线电机工作时，上位机没有发送数据，ROV串口超时问题
                                # TODO 更改为心跳包
                                self.send_command(0x01)
                                time.sleep(0.02)  # 50Hz更新频率
                                    
                                # 双按钮版本
                                LT_trigger=self.xbox_controller.get_axis("LT")
                                RT_trigger=self.xbox_controller.get_axis("RT")
                                lt_mapped = map_value(LT_trigger, -1, 1, 0, 1000)
                                rt_mapped = map_value(RT_trigger, -1, 1, 0, 1000)
                                # PWM1计算: 右扳机增加,左扳机减少 (范围1000-2000μs)
                                pwm1_value=1500+(lt_mapped-rt_mapped)*1
                                
                                
                                # PWM2计算: 右摇杆左右控制
                                # if abs(right_x) > self.xbox_controller.deadzone:
                                #     pwm1_value = map_value(right_x, -1, 1, 2000, 1000)
                            
                            if self.xbox_controller.get_button("Y"):
                                RT_trigger=self.xbox_controller.get_axis("RT")
                                rt_mapped = map_value(RT_trigger, -1, 1, 0, 500)
                                pwm1_value=1500+rt_mapped*1
                                
                            pwm1_value = max(1000, min(2000, pwm1_value))  # 限制范围
                                    
                            pwm2_value=1500
                            # if self.xbox_controller.get_button("LB"):
                            #     pwm2_value=1500-500*0.3
                            # elif self.xbox_controller.get_button("RB"):
                            #     pwm2_value=1500+500*0.2
                            # else:
                            #     pwm2_value=1500
                                
                                # lt_mapped = map_value(LT_trigger, -1, 1, 0, 1000)
                                # rt_mapped = map_value(RT_trigger, -1, 1, 0, 1000)
                                
                                # pwm2_value=1500+(rt_mapped-lt_mapped)*0.1
                                # pwm2_value = max(1000, min(2000, pwm2_value))  # 限制范围
                                
                
                            # 更新PWM输出
                            pwm1.set_pulse_width(int(pwm1_value))
                            pwm2.set_pulse_width(int(pwm2_value))
                            # 显示当前值
                            # print(f"\rPWM1: {pwm1_value:.0f}μs | PWM2: {pwm2_value:.0f}μs", end="", flush=True)
                
                # joystick断开
                # TODO 断开时，控制信号全部重置
                if not current_status:
                    if not MAC_DEBUG:
                        pwm1.set_pulse_width(1500)
                        pwm2.set_pulse_width(1500)
                        
            except Exception as e:
                print(f"Xbox控制器循环错误: {e}")
                time.sleep(1)

    def send_joystick_data(self, left_x, left_y, right_x, right_y):
        """发送摇杆数据到OrangePiZero3下位机"""

        try:
            yaw_rate = 0.5

            # 将摇杆值从 [-1.0, 1.0] 转换为 [-100, 100]
            left_x_int = int(left_x * yaw_rate * 100)
            left_y_int = int(left_y * 100)
            right_x_int = int(right_x * 100)
            right_y_int = int(right_y * 100)

            # 限制范围
            left_x_int = max(-100, min(100, left_x_int))
            left_y_int = max(-100, min(100, left_y_int))
            right_x_int = max(-100, min(100, right_x_int))
            right_y_int = max(-100, min(100, right_y_int))
            
            self.joystick_data.update({
                "lX": left_x_int,    # 左摇杆X轴 (左右)
                "lY": left_y_int,    # 左摇杆Y轴 (前后)
                "rX": right_x_int,   # 右摇杆X轴 (左右)
                "rY": right_y_int    # 右摇杆Y轴 (前后)
            })

            # !!!TODO 前进时 rY -10，-12时电机闪动， -12之后停止，-33再次启动
            # print(self.joystick_data)

            # 构建发送给下位机的消息
            message = {
                'type': 'command',
                'command': 'joystick_control',
                'data': self.joystick_data,
                'timestamp': time.time()
            }

            # 发送数据到OrangePiZero3下位机
            if self.client_connected:
                # 使用线程安全的方式发送数据
                if self._send_queue.qsize()<2:
                    # print(Fore.WHITE+f"Add to queue[{self._send_queue.qsize()}]: {message}")
                    
                    self.send_to_orangepi_sync(message)
                else:
                    print(Fore.YELLOW+f"{self._send_queue.qsize()}： ⚠️ _send_queue.not empty, skip joystick data")

            else:
                # 如果未连接到下位机，显示警告（但不要频繁打印）
                if not hasattr(self, '_last_disconnect_warning') or time.time() - self._last_disconnect_warning > 5:
                    print(Fore.YELLOW+f"⚠️ 未连接到{orangepi_server_url}下位机，无法发送摇杆数据")
                    self._last_disconnect_warning = time.time()

        except Exception as e:
            print(f"❌ 发送摇杆数据失败: {e}")
            # 不要因为发送失败而中断摇杆数据采集

    def restart_camera(self):
        """重启摄像头以应用新设置"""
        if self.camera:
            self.camera.release()
        self.setup_camera()

    def setup_serial(self):
        """初始化串口连接"""
        if SerialPort:
            try:
                self.serial_port = SerialPort(port=serial_port_str, baudrate=baudrate)
                
                if self.serial_port.open(receive_callback=self.handle_serial_data):
                    print("串口连接成功")
                    print("串口接收回调已设置")
                else:
                    self.serial_port = None
                
            except Exception as e:
                print(f"串口连接失败: {e}")
                self.serial_port = None
        else:
            print("使用模拟数据模式")
            
    def calculate_crc8(self, data):
        """计算CRC8校验码"""
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc

    def send_command(self, cmd, data=b''):
        #[ ] """发送命令到ESP32"""
        
        if not self.serial_port:
            return None

        try:
            # 构建命令帧
            frame = bytearray([0xAA, cmd, len(data)])
            frame.extend(data)
            crc = self.calculate_crc8(frame[1:])
            frame.append(crc)

            # 转换为bytes类型
            frame_bytes = bytes(frame)

            # 记录发送时间
            self.serial_send_time = time.time() * 1000  # 转换为毫秒

            # 发送命令
            success = self.serial_port.send_hex(frame_bytes)
            if success:
                print(Fore.YELLOW+f"发送: {frame_bytes.hex().upper()}")
            else:
                print(Fore.RED+f"\n发送失败: {frame_bytes.hex().upper()}")
                self.serial_send_time = None  # 发送失败时清除计时
                self.sensor_data['serialResponseStatus'] = 'timeout'
                self.sensor_data['serialLatency'] = 150
                
                time.sleep(0.1)  # 等待10ms，减少延迟
                
                # TODO 增加发送失败重发机制，如果是串口断开，重新等待连接，直到连接成功

            # TODO 等待响应（简化版，实际应该有超时处理）
            

            return True
        except Exception as e:
            print(f"发送命令失败: {e}")
            return False

    def parse_response(self, data):
        """解析ESP32响应数据"""
        try:
            if len(data) < 4:
                return None

            # 检查帧头
            if data[0] != 0x55:
                return None

            status = data[1]
            data_len = data[2]

            if len(data) < 4 + data_len:
                return None

            payload = data[3:3+data_len]
            received_crc = data[3+data_len]

            # 验证CRC
            frame_data = data[1:3+data_len]
            calculated_crc = self.calculate_crc8(frame_data)

            if received_crc != calculated_crc:
                print("CRC校验失败")
                return None
            
            # self.has_other_commond=False

            return {'status': status, 'data': payload}

        except Exception as e:
            print(f"解析响应失败: {e}")
            return None

    def handle_serial_data(self, data: bytes):
        # [ ]! 1 接收ESP32数据，包含帧头对齐功能
        try:
            # [ ]TEST------------------------
            print(Fore.GREEN+f"\t接收HEX: {data.hex().upper()}")
            # print(Fore.GREEN+f"\tTEXT: {str(data)}\n")
            
            if str(data).find("searching")!=-1:
                # print(Fore.RED+f"CRC错误:{data}")
                self.serial_response_dispaly_lasttime=time.time()
                self.sensor_data["esp32CommunicationError"] = 'ESP32_CRC_ERROR'   
            
            # if str(data).find("serialTimeOut")!=-1:
            #     self.has_other_commond=False
            
            if time.time()-self.serial_response_dispaly_lasttime>5:
                self.sensor_data["esp32CommunicationError"] = ''
                self.sensor_data["esp32CommunicationResponseText"]=''
                
            # 将新数据添加到缓冲区
            self.serial_buffer.extend(data)

            # 处理缓冲区中的所有完整帧
            while len(self.serial_buffer) > 0:
                # 查找帧头0x55
                frame_start = self._find_frame_header()
                if frame_start == -1:
                    # 没有找到帧头，清空缓冲区
                    # print(f"未找到帧头0x55，清空缓冲区: {self.serial_buffer.hex().upper()}")
                    print(Fore.RED+f"无帧头:{data}")
                    
                    self.serial_response_dispaly_lasttime=time.time()
                    
                    data=data.replace(b"\r",b"<br>").replace(b"\n",b"<br>")
                    resposne_text=str(data)
                    resposne_text=resposne_text.replace("b'",'')
                    
                    if(len(self.sensor_data["esp32CommunicationResponseText"])>500):
                        self.sensor_data["esp32CommunicationResponseText"]=""
                    
                    self.sensor_data["esp32CommunicationResponseText"]+=resposne_text+"<br>"
                    
                    self.serial_buffer.clear()
                    break

                # 如果帧头不在开始位置，移除之前的无效数据
                if frame_start > 0:
                    discarded_data = self.serial_buffer[:frame_start]
                    # print(Fore.YELLOW+f"丢弃无效数据: {discarded_data.hex().upper()}")
                    self.serial_buffer = self.serial_buffer[frame_start:]

                # 尝试解析完整帧
                frame_data = self._extract_complete_frame()
                if frame_data is None:
                    # 数据不完整，等待更多数据
                    break

                self.serial_receive_time=time.time()*1000
                
                # 处理完整的帧数据
                self._process_frame(frame_data)

        except Exception as e:
            print(f"处理串口数据失败: {e}")
            # 出错时清空缓冲区
            self.serial_buffer.clear()

    def _find_frame_header(self):
        """查找帧头0x55的位置"""
        try:
            return self.serial_buffer.index(0x55)
        except ValueError:
            return -1

    def _extract_complete_frame(self):
        """从缓冲区提取完整的帧数据"""
        try:
            if len(self.serial_buffer) < 4:
                return None  # 数据不足，无法确定帧长度

            # 检查帧头
            if self.serial_buffer[0] != 0x55:
                return None

            # 获取数据长度
            data_len = self.serial_buffer[2]
            frame_len = 4 + data_len  # 帧头(1) + 状态(1) + 长度(1) + 数据(data_len) + CRC(1)

            if len(self.serial_buffer) < frame_len:
                return None  # 数据不完整

            # 提取完整帧
            frame_data = bytes(self.serial_buffer[:frame_len])
            self.serial_buffer = self.serial_buffer[frame_len:]  # 移除已处理的数据

            return frame_data

        except Exception as e:
            print(f"提取帧数据失败: {e}")
            return None

    def _process_frame(self, frame_data: bytes):
        # [ ]!!! """处理单个完整的帧数据"""
        try:
            # print(Fore.GREEN+f"\t处理帧数据[Other:{len(self.serial_commond_list)}]: {frame_data.hex().upper()}")
            # print(f"\t--------: {frame_data}")

            # 计算串口延迟和响应状态
            if self.serial_send_time:
                current_time = time.time() * 1000  # 转换为毫秒
                latency = max(0, int(current_time - self.serial_send_time))  # 确保延迟不为负数
                self.sensor_data['serialLatency'] = latency

                # 根据延迟设置响应状态
                if latency < 200:
                    self.sensor_data['serialResponseStatus'] = 'success'
                elif latency < 250:
                    self.sensor_data['serialResponseStatus'] = 'timeout'
                else:
                    self.sensor_data['serialResponseStatus'] = 'error'

                # TODO !!!!!!增加平均串口时间，丢包率
                # print(Fore.YELLOW+f"串口延迟: {latency}ms, 状态: {self.sensor_data['serialResponseStatus']}")
                self.serial_send_time = None  # 重置计时
            # else:
            #     # 没有发送时间记录，标记为未连接状态
            #     self.sensor_data['serialResponseStatus'] = 'disconnected'

            # 解析协议响应
            response = self.parse_response(frame_data)
            if response and response['status'] == 0x00:
                payload = response['data']

                # 根据数据长度判断响应类型
                if len(payload) == 2:  # 初始化
                    if payload[0:2] == b'\x01\xff':
                        self.esp32_init=True
                        print("ESP32 init")
                        
                elif len(payload) == 12:  # 气压计数据
                    import struct
                    temperature = struct.unpack('<f', payload[0:4])[0]
                    pressure = struct.unpack('<f', payload[4:8])[0]
                    depth = struct.unpack('<f', payload[8:12])[0]

                    self.sensor_data['temperature'] = temperature
                    self.sensor_data['pressure'] = pressure
                    self.sensor_data['depth'] = depth

                    print(f"气压计数据 - 温度: {temperature:.2f}°C, 压力: {pressure:.3f}hPa, 深度: {depth:.2f}m")


            else:
                print(f"协议解析失败或状态错误: {response}")

        except Exception as e:
            print(f"处理帧数据失败: {e}")


    async def handle_mode_control(self, websocket, data):
        """处理模式控制命令"""
        try:
            mode = data.get('mode')
            mode_data = data.get('data', {})

            print(f"🎮 收到模式控制命令: {mode}, 数据: {mode_data}")

            if mode == 'depth_hold':
                # 定深控制
                if mode_data.get('enable'):
                    target_depth = mode_data.get('target', 0.0)
                    print(f"🌊 启用定深模式，目标深度: {target_depth:.2f}m")

                    # 发送定深控制命令到ESP32
                    if self.serial_port:
                        # depth_data = struct.pack('<f', float(target_depth))
                        # self.send_command(0x13, depth_data)  # handleSetDepthControl
                        self.serial_commond_list.append((0x14, [0x01]))

                        # 广播模式状态更新
                        await self.broadcast_mode_status('depth_hold', True, target_depth)
                else:
                    print("🌊 禁用定深模式")

                    # 发送禁用定深命令
                    if self.serial_port:
                        # 发送一个特殊值表示禁用（比如负数）
                        # depth_data = struct.pack('<f', -1.0)
                        # self.send_command(0x13, depth_data)
                        self.serial_commond_list.append((0x14, [0x01]))
                        
                        # 广播模式状态更新
                        await self.broadcast_mode_status('depth_hold', False, 0)

            elif mode == 'heading_hold':
                # 锁定航向控制
                if mode_data.get('enable'):
                    target_heading = mode_data.get('target', 0)
                    print(f"🧭 启用锁定航向，目标航向: {target_heading}°")

                    # 发送锁定航向命令到ESP32
                    if self.serial_port:
                        # heading_data = struct.pack('<f', float(target_heading))
                        # self.send_command(0x14, heading_data)  # handleSetHeadingControl
                        self.serial_commond_list.append((0x15, [0x01]))

                        # 广播模式状态更新
                        await self.broadcast_mode_status('heading_hold', True, target_heading)
                else:
                    print("🧭 禁用锁定航向")

                    # 发送禁用锁定航向命令
                    if self.serial_port:
                        # 发送一个特殊值表示禁用（比如负数）
                        # heading_data = struct.pack('<f', -1.0)
                        # self.send_command(0x14, heading_data)
                        self.serial_commond_list.append((0x15, [0x01]))

                        # 广播模式状态更新
                        await self.broadcast_mode_status('heading_hold', False, 0)

        except Exception as e:
            print(f"处理模式控制命令失败: {e}")

    async def broadcast_mode_status(self, mode, enabled, target_value):
        """广播模式状态更新到所有客户端"""
        try:
            message = {
                'type': 'mode_status',
                'mode': mode,
                'enabled': enabled,
                'target': target_value,
                'timestamp': time.time()
            }

            # 发送给所有连接的客户端
            if self.clients:
                tasks = []
                for client in self.clients.copy():
                    tasks.append(self.safe_send(client, message))

                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            print(f"广播模式状态失败: {e}")

    # WebSocket客户端相关方法
    async def start_websocket_client(self):
        """启动WebSocket客户端连接到OrangePi服务器"""
        print(f"🔗 启动WebSocket客户端，连接到: {self.orangepi_server_url}")

        while self.client_running:
            try:
                await self.connect_to_orangepi()
                await asyncio.sleep(self.client_reconnect_interval)
            except Exception as e:
                print(f"❌ WebSocket客户端连接失败: {e}")
                if self.client_running:
                    print(f"⏳ {self.client_reconnect_interval}秒后重试连接...")
                    await asyncio.sleep(self.client_reconnect_interval)

    async def connect_to_orangepi(self):
        """连接到OrangePi WebSocket服务器"""
        try:
            print(f"🔄 正在连接到OrangePi服务器1: {self.orangepi_server_url}")

            async with websockets.connect(
                self.orangepi_server_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                self.client_websocket = websocket
                self.client_connected = True
                print(f"✅ 成功连接到OrangePi服务器2")

                # 发送初始连接消息
                # await self.send_to_orangepi({
                #     'type': 'connection',
                #     'client_id': 'rov_controller',
                #     'timestamp': time.time()
                # })

                # 创建任务来处理消息接收和发送队列
                async def message_receiver():
                    async for message in websocket:
                        try:
                            await self.handle_rov_message(message)
                        except Exception as e:
                            print(f"❌ 处理OrangePi消息失败: {e}")

                async def queue_processor():
                    while self.client_connected:
                        try:
                            await self.process_send_queue()
                            await asyncio.sleep(0.005)  # 5ms间隔处理队列
                        except Exception as e:
                            print(f"❌ 处理发送队列失败: {e}")
                            break

                # 并发运行消息接收和队列处理
                await asyncio.gather(
                    message_receiver(),
                    queue_processor(),
                    return_exceptions=True
                )

        except websockets.exceptions.ConnectionClosed:
            print("🔌 与OrangePi服务器的连接已关闭")
        except Exception as e:
            print(f"❌ 连接OrangePi服务器失败1: {e}")
        finally:
            self.client_websocket = None
            self.client_connected = False
            print("🔌 WebSocket客户端连接已断开1")

    async def send_to_orangepi(self, data):
        """发送数据到OrangePi服务器"""
        if self.client_websocket and self.client_connected:
            try:
                message = json.dumps(data, ensure_ascii=False)
                await self.client_websocket.send(message)
                # print(f"📤 发送数据到OrangePi: {data.get('type', 'unknown')}")
            except Exception as e:
                print(f"❌ 发送数据到OrangePi失败: {e}")
                self.client_connected = False
        else:
            print("⚠️ 未连接到OrangePi服务器，无法发送数据")

    def send_to_orangepi_sync(self, data):
        """同步方式发送数据到OrangePi服务器（线程安全）"""
        if self.client_websocket and self.client_connected:
            try:
                # 将数据放入队列，由WebSocket客户端线程处理
                # if not hasattr(self, '_send_queue'):
                    
                # 尝试将消息放入队列
                try:
                    self._send_queue.put_nowait(data)
                except queue.Full:
                    # 队列满时，丢弃最旧的消息
                    try:
                        self._send_queue.get_nowait()
                        self._send_queue.put_nowait(data)
                    except queue.Empty:
                        pass

            except Exception as e:
                print(f"❌ 添加数据到发送队列失败: {e}")
        else:
            # 连接断开时不打印错误，避免日志过多
            pass

    async def process_send_queue(self):
        """处理发送队列中的数据"""
        if hasattr(self, '_send_queue'):
            import queue
            try:
                while not self._send_queue.empty():
                    try:
                        data = self._send_queue.get_nowait()
                        await self.send_to_orangepi(data)
                    except queue.Empty:
                        break
                    except Exception as e:
                        print(f"❌ 处理发送队列数据失败: {e}")
                        break
            except Exception as e:
                print(f"❌ 处理发送队列失败: {e}")

    async def handle_rov_message(self, message):
        """处理来自OrangePi服务器的消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type', 'unknown')
            receive_time=data.get('timestamp',0)
            start_timestamp=data.get('start_timestamp',0)
            print(f"📥 收到OrangePi消息({(time.time()-start_timestamp):.3f}): {message_type}")

            if message_type == 'command':
                await self.handle_orangepi_command(data)
            elif message_type == 'status':
                await self.handle_orangepi_status(data)
            elif message_type == 'sensor_request':
                await self.handle_orangepi_sensor_request(data)
            elif message_type == 'control':
                await self.handle_orangepi_control(data)
            elif message_type == 'sensor_data':
                print("sensor_data",data)
                
            elif message_type == 'pong':
                start_timestamp=data.get('start_timestamp')
                if time.time()-start_timestamp>0.3:
                    print(Fore.RED+f"pong超时: {time.time()-start_timestamp}")
                
            else:
                print(f"⚠️ 未知的OrangePi消息类型: {message_type}")

        except json.JSONDecodeError:
            print(f"❌ 无效的JSON消息: {message}")
        except Exception as e:
            print(f"❌ 处理OrangePi消息失败: {e}")

    async def handle_orangepi_command(self, data):
        """处理来自OrangePi的命令"""
        command = data.get('command')
        params = data.get('params', {})

        print(f"🎮 收到OrangePi命令: {command}")

        if command == 'get_sensor_data':
            # 发送当前传感器数据
            await self.send_sensor_data_to_orangepi()
        elif command == 'set_mode':
            # 设置ROV模式
            mode = params.get('mode')
            enabled = params.get('enabled', False)
            target = params.get('target')
            await self.handle_remote_mode_control(mode, enabled, target)
        elif command == 'emergency_stop':
            # 紧急停止
            print("🚨 收到紧急停止命令")
            await self.emergency_stop()
        else:
            print(f"⚠️ 未知的OrangePi命令: {command}")

    async def handle_orangepi_status(self, data):
        """处理来自OrangePi的状态信息"""
        status = data.get('status')
        print(f"📊 OrangePi状态更新: {status}")

        # 可以根据需要处理不同的状态信息
        # 例如：系统状态、网络状态等

    async def handle_orangepi_sensor_request(self, data):
        """处理来自OrangePi的传感器数据请求"""
        requested_sensors = data.get('sensors', [])
        print(f"📡 OrangePi请求传感器数据: {requested_sensors}")

        # 发送请求的传感器数据
        await self.send_sensor_data_to_orangepi(requested_sensors)

    async def handle_orangepi_control(self, data):
        """处理来自OrangePi的控制命令"""
        control_type = data.get('control_type')
        control_data = data.get('data', {})

        print(f"🎛️ 收到OrangePi控制命令: {control_type}")

        if control_type == 'movement':
            # 运动控制
            await self.handle_remote_movement_control(control_data)
        elif control_type == 'camera':
            # 摄像头控制
            await self.handle_remote_camera_control(control_data)
        elif control_type == 'lights':
            # 灯光控制
            await self.handle_remote_lights_control(control_data)

    async def send_sensor_data_to_orangepi(self, requested_sensors=None):
        """发送传感器数据到OrangePi"""
        try:
            # 准备传感器数据
            sensor_data = {}

            if requested_sensors is None:
                # 发送所有传感器数据
                sensor_data = self.sensor_data.copy()
            else:
                # 只发送请求的传感器数据
                for sensor in requested_sensors:
                    if sensor in self.sensor_data:
                        sensor_data[sensor] = self.sensor_data[sensor]

            # 添加时间戳
            message = {
                'type': 'sensor_data',
                'data': sensor_data,
                'timestamp': time.time()
            }

            await self.send_to_orangepi(message)

        except Exception as e:
            print(f"❌ 发送传感器数据到OrangePi失败: {e}")

    async def handle_remote_mode_control(self, mode, enabled, target):
        """处理远程模式控制"""
        try:
            print(f"🎮 远程模式控制: {mode}, 启用: {enabled}, 目标: {target}")

            if mode == 'depth_hold':
                if enabled and target is not None:
                    # 启用定深模式
                    if self.serial_port:
                        depth_data = struct.pack('<f', float(target))
                        self.send_command(0x13, depth_data)
                        print(f"🌊 远程启用定深模式，目标深度: {target}m")
                else:
                    # 禁用定深模式
                    if self.serial_port:
                        depth_data = struct.pack('<f', -1.0)
                        self.send_command(0x13, depth_data)
                        print("🌊 远程禁用定深模式")

            elif mode == 'heading_hold':
                if enabled and target is not None:
                    # 启用锁定航向
                    if self.serial_port:
                        heading_data = struct.pack('<f', float(target))
                        self.send_command(0x14, heading_data)
                        print(f"🧭 远程启用锁定航向，目标航向: {target}°")
                else:
                    # 禁用锁定航向
                    if self.serial_port:
                        heading_data = struct.pack('<f', -1.0)
                        self.send_command(0x14, heading_data)
                        print("🧭 远程禁用锁定航向")

        except Exception as e:
            print(f"❌ 处理远程模式控制失败: {e}")

    async def handle_remote_movement_control(self, control_data):
        """处理远程运动控制"""
        try:
            # 这里可以处理来自OrangePi的运动控制命令
            # 例如：前进、后退、左转、右转等
            print(f"🎮 远程运动控制: {control_data}")

        except Exception as e:
            print(f"❌ 处理远程运动控制失败: {e}")

    async def handle_remote_camera_control(self, control_data):
        """处理远程摄像头控制"""
        try:
            # 处理摄像头控制命令
            print(f"📹 远程摄像头控制: {control_data}")

        except Exception as e:
            print(f"❌ 处理远程摄像头控制失败: {e}")

    async def handle_remote_lights_control(self, control_data):
        """处理远程灯光控制"""
        try:
            # 处理灯光控制命令
            print(f"💡 远程灯光控制: {control_data}")

        except Exception as e:
            print(f"❌ 处理远程灯光控制失败: {e}")

    async def emergency_stop(self):
        """紧急停止"""
        try:
            print("🚨 执行紧急停止")

            # 停止所有运动
            if self.serial_port:
                # 发送停止命令
                stop_data = bytes([0])
                self.send_command(0x01, stop_data)  # 假设0x01是停止命令

            # 禁用所有模式
            if self.serial_port:
                # 禁用定深模式
                depth_data = struct.pack('<f', -1.0)
                self.send_command(0x13, depth_data)

                # 禁用锁定航向
                heading_data = struct.pack('<f', -1.0)
                self.send_command(0x14, heading_data)

            print("✅ 紧急停止执行完成")

        except Exception as e:
            print(f"❌ 执行紧急停止失败: {e}")

    def start_websocket_client_thread(self):
        """在单独线程中启动WebSocket客户端"""
        def run_client():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.start_websocket_client())
            except Exception as e:
                print(f"❌ WebSocket客户端线程异常: {e}")
            finally:
                loop.close()

        self.client_thread = threading.Thread(target=run_client, daemon=True)
        self.client_thread.start()
        print("🚀 WebSocket客户端线程已启动")

    def start_sensor_data_broadcast(self):
        """启动定期向OrangePi发送传感器数据的线程"""
        def broadcast_sensor_data():
            while self.client_running:
                try:
                    if self.client_connected and self.sensor_data:
                        # 创建新的事件循环用于发送数据
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        # 发送传感器数据
                        loop.run_until_complete(self.send_sensor_data_to_orangepi())
                        loop.close()

                except Exception as e:
                    print(f"❌ 广播传感器数据到OrangePi失败: {e}")

                # 每2秒发送一次数据
                time.sleep(2)

        broadcast_thread = threading.Thread(target=broadcast_sensor_data, daemon=True)
        broadcast_thread.start()
        print("📡 传感器数据广播线程已启动")

    def create_websocket_handler(self):
        """创建兼容不同websockets版本的处理器"""
        async def handler(websocket, *args):
            # 兼容不同版本的websockets库，忽略额外参数
            _ = args  # 忽略未使用的参数
            await self.websocket_handler(websocket)
        return handler

    def start_data_collection(self): 
        # [ ]!!! """启动数据采集"""
        def collect_data():
            request_count = 0
            while True:
                #   and self.esp32_init
                if self.serial_port:
                    # 高频请求传感器数据
                    # request_count += 1

                    # 优化请求策略：统一使用"获取所有传感器数据"协议，降低通讯延迟
                    # if request_count % 100 == 0:
                    #     print(f"发送心跳包 #{request_count//100}")
                    #     self.send_command(0xFF)  # 每10秒发送一次心跳包
                    # else:
                    #     if request_count % 100 == 1:  # 每10秒打印一次状态
                    #         print(f"高频统一传感器数据请求中... (10Hz)")
                    #     self.request_all_sensor_data()
                    
                    # TODO 测试时注释 启动数据采集 
                    # self.request_all_sensor_data()
                    time.sleep(0.1)  # 每100ms请求一次，实现100Hz更新频率
     

        # 启动数据采集线程
        collection_thread = threading.Thread(target=collect_data, daemon=True)
        collection_thread.start()

        if self.serial_port:
            print(Fore.GREEN+"ESP32数据采集已启动")
        else:
            print(Fore.YELLOW+"\n\n------------数据模拟已启动------------\n")
            # 模拟数据模式下设置响应状态
            self.sensor_data['serialResponseStatus'] = 'disconnected'
            self.sensor_data['serialLatency'] = 150

# Flask应用用于提供静态文件和视频流
app = Flask(__name__, static_folder='.', template_folder='.')

# 全局ROV服务器实例
rov_server = None

@app.route('/')
def index():
    return send_from_directory('../MobileWeb', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('../MobileWeb', filename)


def run_flask():
    """运行Flask服务器"""
    app.run(host='0.0.0.0', port=8088, debug=False, threaded=True)

def xbox_controller_check_count():
    pygame.init()
    joystick.init()
    while 1:
        pygame.event.pump()  # 处理事件队列
        print("count",joystick.get_count())
        
        time.sleep(0.1)
        
async def main():
    """主函数"""
    # 测试：获取joystick个数，在主函数里可运行，似乎在线程中不可获得新的变化
    # pygame.init()
    # # joystick.init()
    # while 1:
    #     pygame.event.pump()  # 处理事件队列
    #     print("count",joystick.get_count())

    # xbox_check_thread = threading.Thread(target=xbox_controller_check_count, daemon=False)
    # xbox_check_thread.start()
    
    # 初始化两路PWM
    global pwm1 # 第一路PWM
    global pwm2 # 第二路PWM
    
    # MAC开发临时注释
    if not MAC_DEBUG:
        pwm1=PWMController(pwm_chip=1, channel=0)  # 第一路PWM
        pwm2=PWMController(pwm_chip=2, channel=0)  # 第二路PWM
    
    global rov_server
    # 创建ROV服务器实例
    rov_server = ROVWebServer()

    # 启动WebSocket客户端连接到OrangePi
    print("🚀 启动WebSocket客户端连接到OrangePi...")
    rov_server.start_websocket_client_thread()

    await asyncio.Future()  # 保持运行


if __name__ == "__main__":
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        
        if not MAC_DEBUG:
            pwm1.cleanup()
            pwm2.cleanup()
        
        if rov_server:
            # 停止Xbox控制器线程
            rov_server.controller_running = False

            # 停止WebSocket客户端
            rov_server.client_running = False
            if rov_server.client_websocket:
                try:
                    asyncio.create_task(rov_server.client_websocket.close())
                except:
                    pass

            if rov_server.xbox_controller:
                rov_server.xbox_controller.cleanup()

        print("服务器已停止")
