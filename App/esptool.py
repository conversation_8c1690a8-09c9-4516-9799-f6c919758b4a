import esptool


def reset_esp32(port="/dev/tty.wchusbserial599D0149021", baud=115200):
    try:
        # 创建 esptool 连接对象
        esp = esptool.ESP32ROM(port=port, baud=baud)
        esp.connect()
        
        # 硬重置 ESP32（相当于按复位按钮）
        esp.hard_reset()
        print(f"ESP32 on {port} has been restarted.")
    except Exception as e:
        print(f"Error resetting ESP32: {e}")

def reset():
    import serial
    ser = serial.Serial('/dev/tty.wchusbserial599D0149021', 115200)  # 替换成你的串口
    ser.setDTR(False)  # 拉低 DTR（复位）
    # ser.setRTS(True)   # 拉高 RTS（进入下载模式，可选）
    ser.close()


if __name__ == "__main__":
    # reset_esp32()  # 使用默认端口，根据实际情况修改
    
    reset()
