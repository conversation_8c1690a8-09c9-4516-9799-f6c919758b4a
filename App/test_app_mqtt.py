import json
import paho.mqtt.client as mqtt
import time
import threading
from colorama import Fore,init,Style,Back
init(autoreset=True)

# 上位机

app_start_time=time.time()

# 回调函数：连接成功时触发
def on_connect(client, userdata, flags, rc):
    print(f"Connected with result code {rc}")
    client.subscribe("rov/status")  # 订阅主题


timeout_count=0
last_message_time=0
# 回调函数：收到消息时触发
def on_message(client, userdata, msg):
    # print(Fore.GREEN+f"[{msg.topic}]message: {msg.payload.decode()}")
    global timeout_count,last_message_time
    data = json.loads(msg.payload.decode())
    start_timestamp=data.get('start_timestamp')
    index=data.get('index')
    use_time=time.time()-start_timestamp
    delay_onece=data.get('use_time')
    
    
    # print(Fore.WHITE+f"{index}:delay={use_time:.3f},TimeOutCount={timeout_count}")
    
    if time.time()-last_message_time>1:
        last_message_time=time.time()
        print(Fore.CYAN+f"{index}:delay={use_time:.3f}\trunTime:{(time.time()-app_start_time):.0f}\tTimeOutCount={timeout_count}")
        timeout_count=0
    
    if use_time>0.10:
        timeout_count+=1
        print(Fore.RED+f"{index}(TimeOutCount={timeout_count}):delay={use_time:.3f},delay_onece={delay_onece:.3f}")
        


server="orangepi3b.local"
server="192.168.1.1"

# 创建 MQTT 客户端
client = mqtt.Client()
client.on_connect = on_connect
client.on_message = on_message

# 连接到 MQTT 代理（默认端口 1883）

client.connect(server, 1883, 60)


def send_loop():
    i=0
    while 1:
        # for i in range(3):
            # {i} {time.time()}
        message = f"{{\"index\":\"{i}\",\"timestamp\":{time.time()},\"text\":\"aaaaaaaaaaaaaaaaaaa\"}}"
        client.publish("rov/command", message)
        
        # print(Fore.YELLOW+f"Published: {message}")
        
        time.sleep(0.005)
        i+=1


controller_thread = threading.Thread(target=send_loop, daemon=True)
controller_thread.start()

# 保持连接，监听消息
client.loop_forever()
