#!/usr/bin/env python3
"""
测试server.py中的协议通信
"""

import sys
import os
import time

# 添加App目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from App.serial_port import SerialPort
except ImportError:
    print("❌ 无法导入串口模块")
    sys.exit(1)

class TestROVServer:
    def __init__(self):
        self.serial_port = None
        self.sensor_data = {
            'depth': 0.0,
            'rovHeading': 0,
            'phoneHeading': 0,
            'temperature': 25.0,
            'pressure': 1013.25,
            'voltage': 12.0
        }
        
    def calculate_crc8(self, data):
        """计算CRC8校验码"""
        crc = 0x00
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc

    def send_command(self, cmd, data=b''):
        """发送命令到ESP32"""
        if not self.serial_port:
            print("❌ 串口未连接")
            return False
            
        try:
            # 构建命令帧
            frame = bytearray([0xAA, cmd, len(data)])
            frame.extend(data)
            crc = self.calculate_crc8(frame[1:])
            frame.append(crc)
            
            # 发送命令
            success = self.serial_port.send_hex(frame)
            if success:
                print(f"✅ 发送命令: {frame.hex().upper()}")
            else:
                print(f"❌ 发送命令失败: {frame.hex().upper()}")
            
            return success
        except Exception as e:
            print(f"❌ 发送命令异常: {e}")
            return False

    def handle_serial_data(self, data: bytes):
        """处理从ESP32接收的数据"""
        try:
            hex_str = data.hex().upper()
            print(f"📥 接收到数据: {hex_str}")
            
            # 尝试解码为文本（调试信息）
            try:
                text = data.decode('utf-8', errors='ignore')
                if text.strip() and not any(c < 32 or c > 126 for c in text if c not in [10, 13]):
                    print(f"📝 调试信息: {text.strip()}")
                    return
            except:
                pass
            
            # 解析协议响应
            response = self.parse_response(data)
            if response and response['status'] == 0x00:
                payload = response['data']
                
                # 根据数据长度判断响应类型
                if len(payload) == 4:  # 心跳响应
                    import struct
                    timestamp = struct.unpack('<I', payload)[0]
                    print(f"💓 心跳响应: 系统运行时间 {timestamp} ms")
                    
                elif len(payload) == 12:  # 气压计数据
                    import struct
                    temperature = struct.unpack('<f', payload[0:4])[0]
                    pressure = struct.unpack('<f', payload[4:8])[0]
                    depth = struct.unpack('<f', payload[8:12])[0]
                    
                    self.sensor_data['temperature'] = temperature
                    self.sensor_data['pressure'] = pressure
                    self.sensor_data['depth'] = depth
                    
                    print(f"🌊 气压计数据 - 温度: {temperature:.2f}°C, 压力: {pressure:.2f}hPa, 深度: {depth:.2f}m")
                    
                elif len(payload) == 24:  # 完整传感器数据
                    import struct
                    temperature = struct.unpack('<f', payload[0:4])[0]
                    pressure = struct.unpack('<f', payload[4:8])[0]
                    depth = struct.unpack('<f', payload[8:12])[0]
                    angle_x = struct.unpack('<f', payload[12:16])[0]
                    angle_y = struct.unpack('<f', payload[16:20])[0]
                    angle_z = struct.unpack('<f', payload[20:24])[0]
                    
                    self.sensor_data.update({
                        'temperature': temperature,
                        'pressure': pressure,
                        'depth': depth,
                        'angleX': angle_x,
                        'angleY': angle_y,
                        'angleZ': angle_z
                    })
                    
                    print(f"📊 完整传感器数据更新")
                    
                elif len(payload) == 0:  # 控制命令响应
                    print(f"✅ 控制命令执行成功")
                    
        except Exception as e:
            print(f"❌ 处理串口数据失败: {e}")

    def parse_response(self, data):
        """解析ESP32响应数据"""
        try:
            if len(data) < 4:
                return None
                
            # 检查帧头
            if data[0] != 0x55:
                return None
                
            status = data[1]
            data_len = data[2]
            
            if len(data) < 4 + data_len:
                return None
                
            payload = data[3:3+data_len]
            received_crc = data[3+data_len]
            
            # 验证CRC
            frame_data = data[1:3+data_len]
            calculated_crc = self.calculate_crc8(frame_data)
            
            if received_crc != calculated_crc:
                print(f"❌ CRC校验失败: 期望{calculated_crc:02X}, 收到{received_crc:02X}")
                return None
                
            return {'status': status, 'data': payload}
            
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return None

    def setup_serial(self):
        """初始化串口连接"""
        try:
            self.serial_port = SerialPort(port='/dev/tty.usbserial-0001', baudrate=115200)
            self.serial_port.open(receive_callback=self.handle_serial_data)
            print("✅ 串口连接成功")
            print("📡 串口接收回调已设置")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            self.serial_port = None
            return False

    def test_commands(self):
        """测试各种命令"""
        if not self.serial_port:
            print("❌ 串口未连接，无法测试")
            return
            
        print("\n🧪 开始命令测试")
        print("=" * 40)
        
        tests = [
            ("心跳包", 0xFF, b''),
            ("气压计数据", 0x02, b''),
            ("所有传感器数据", 0x01, b''),
            ("LED测试(红色)", 0x11, bytes([255, 0, 0, 128])),
            ("PWM测试", 0x10, bytes([0, 90])),
        ]
        
        for test_name, cmd, data in tests:
            print(f"\n🔬 测试: {test_name}")
            if self.send_command(cmd, data):
                time.sleep(2)  # 等待响应
            else:
                print(f"❌ {test_name} 发送失败")
        
        print(f"\n🎉 命令测试完成")

def main():
    """主函数"""
    print("🧪 Server.py协议通信测试")
    print("=" * 50)
    
    # 创建测试实例
    test_server = TestROVServer()
    
    # 连接串口
    if not test_server.setup_serial():
        return
    
    try:
        # 等待ESP32启动
        print("⏳ 等待ESP32启动...")
        time.sleep(3)
        
        # 运行测试
        test_server.test_commands()
        
        # 保持运行，监听数据
        print("\n🎧 持续监听数据...")
        print("按 Ctrl+C 停止")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试停止")
    finally:
        if test_server.serial_port:
            test_server.serial_port.close()
        print("🔌 串口已关闭")

if __name__ == "__main__":
    main()
