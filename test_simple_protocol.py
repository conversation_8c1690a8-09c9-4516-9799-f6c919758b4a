#!/usr/bin/env python3
"""
简化的协议测试
只测试心跳包通信
"""

import serial
import time

def calculate_crc8(data):
    """计算CRC8校验码"""
    crc = 0x00
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x07
            else:
                crc <<= 1
            crc &= 0xFF
    return crc

def test_heartbeat():
    """测试心跳包"""
    port = '/dev/tty.usbserial-0001'
    
    print("🔌 连接串口...")
    ser = serial.Serial(port, 115200, timeout=2)
    
    print("⏳ 等待ESP32启动...")
    time.sleep(3)
    
    # 清空缓冲区
    ser.flushInput()
    ser.flushOutput()
    
    print("📤 发送心跳包...")
    
    # 构建心跳包: AA FF 00 CRC
    cmd_data = bytes([0xFF, 0x00])
    crc = calculate_crc8(cmd_data)
    heartbeat = bytes([0xAA]) + cmd_data + bytes([crc])
    
    print(f"发送: {heartbeat.hex().upper()}")
    ser.write(heartbeat)
    
    print("📥 等待响应...")
    
    # 等待响应
    start_time = time.time()
    response_data = b''
    
    while time.time() - start_time < 5:  # 等待5秒
        if ser.in_waiting > 0:
            new_data = ser.read(ser.in_waiting)
            response_data += new_data
            print(f"收到数据: {new_data.hex().upper()}")
            
            # 检查是否收到完整响应
            if len(response_data) >= 8:  # 心跳响应应该是8字节
                break
        time.sleep(0.1)
    
    if response_data:
        print(f"完整响应: {response_data.hex().upper()}")
        
        # 解析响应
        if len(response_data) >= 8 and response_data[0] == 0x55:
            status = response_data[1]
            data_len = response_data[2]
            print(f"状态码: 0x{status:02X}")
            print(f"数据长度: {data_len}")
            
            if status == 0x00 and data_len == 4:
                import struct
                timestamp = struct.unpack('<I', response_data[3:7])[0]
                print(f"✅ 心跳成功! 系统运行时间: {timestamp} ms")
            else:
                print(f"❌ 心跳失败: 状态={status}, 长度={data_len}")
        else:
            print("❌ 响应格式错误")
    else:
        print("❌ 没有收到响应")
    
    ser.close()
    print("🔌 串口已关闭")

if __name__ == "__main__":
    print("🧪 简化协议测试 - 心跳包")
    print("=" * 40)
    test_heartbeat()
