import pygame
import time

# 初始化
print("pygame.init！")
pygame.init()
pygame.joystick.init()


# 检测手柄
joystick_count = pygame.joystick.get_count()
if joystick_count == 0:
    print("未检测到手柄！")
    exit()

print(f"手柄({joystick_count})")


# 初始化第一个手柄
joystick = pygame.joystick.Joystick(0)
joystick.init()
print(f"手柄名称({joystick_count}): {joystick.get_name()}")

def rumble():
    # 设置震动强度（范围：0.0 到 1.0）
    low_frequency_rumble = 0  # 低频震动（重低音）
    high_frequency_rumble = 1  # 高频震动（高音）

    # 设置震动持续时间（毫秒）
    duration_ms = 2000  # 2秒

    # 启动震动
    if hasattr(joystick, 'rumble'):
        joystick.rumble(low_frequency_rumble, high_frequency_rumble, duration_ms)
        print(f"手柄开始震动: 低频={low_frequency_rumble}, 高频={high_frequency_rumble}, 持续={duration_ms / 1000}秒")
    else:
        print("当前手柄或系统不支持震动功能。")

def normalize(value):
    """将 [-1, 1] 的值映射到 [0, 1]"""
    return (value + 1) / 2

try:
    while True:
        pygame.event.pump()  # 处理事件队列

        # 获取摇杆和按钮状态
        left_stick_x = joystick.get_axis(0)  # 左摇杆X轴
        left_stick_y = joystick.get_axis(1)  # 左摇杆Y轴
        right_stick_x = joystick.get_axis(2)  # 右摇杆X轴
        right_stick_y = joystick.get_axis(3)  # 右摇杆Y轴

        # 按钮映射（Pro Controller示例）
        a_button = joystick.get_button(0)  # A键
        b_button = joystick.get_button(1)  # B键
        x_button = joystick.get_button(2)  # X键
        y_button = joystick.get_button(3)  # Y键
        l_button = joystick.get_button(4)  # L键
        r_button = joystick.get_button(5)  # R键
        plus_button = joystick.get_button(6)  # +键
        minus_button = joystick.get_button(7)  # -键

        # print(f"左摇杆: ({left_stick_x:.2f}, {left_stick_y:.2f}) 右杆：({right_stick_x:.2f},{right_stick_y:.2f}) L:{l_button} R:{r_button} A:{a_button}")
        # 打印所有按钮和轴信息

        output=f""
        for i in range(joystick.get_numbuttons()):
            # print(f"按钮 {i}: {joystick.get_button(i)}")
            output+=f"钮{i}:[{joystick.get_button(i)}] "
            # output+=f"按钮:{joystick.get_button(i)} "

        for i in range(joystick.get_numaxes()):
            # print(f"轴 {i}: {joystick.get_axis(i)}")
            output+=f"轴{i}:[{joystick.get_axis(i)}] "
            # output+=f"轴:{joystick.get_axis(i)} "

        # print(f"\r{output}",end="",flush=True)

        # 震动测试
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

            # 手柄按钮事件
            if event.type == pygame.JOYBUTTONDOWN:
                button = event.button

                # 不同按钮触发不同震动强度
                if button == 0:  # A 按钮
                    if hasattr(joystick, 'rumble'):
                        joystick.rumble(0.8, 0.2, 1000)  # 强震动 1秒
                        # print("触发强震动")

                elif button == 1:  # B 按钮
                    if hasattr(joystick, 'rumble'):
                        joystick.rumble(0.2, 0.8, 500)  # 弱震动 0.5秒
                        # print("触发弱震动")

                elif button == 7:  # 开始按钮
                    running = False

            if event.type == pygame.JOYAXISMOTION:
                left=normalize(joystick.get_axis(4))
                right=normalize(joystick.get_axis(5))
                print(f"\r{joystick.get_axis(4),left},{joystick.get_axis(5),right}",end="",flush=True)

                joystick.rumble(right, left, 1000)  # 强震动 1秒

        # print(f"-------------------------------------")
        time.sleep(0.01)

except (FileNotFoundError, PermissionError, OSError,KeyboardInterrupt):
    print("退出程序")
    pygame.quit()
