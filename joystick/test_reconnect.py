#!/usr/bin/env python3
"""
测试Xbox控制器断开重连功能
"""

import time
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from joystick.xbox_controller import XboxController

def test_reconnect():
    """测试重连功能"""
    print("🎮 Xbox控制器断开重连测试")
    print("=" * 50)
    
    # 初始化控制器
    controller = XboxController(wait_for_controller=True)
    
    try:
        print("\n📊 开始监控控制器状态...")
        print("请在测试过程中断开和重新连接手柄")
        print("按 Ctrl+C 退出测试")
        print("-" * 50)
        
        last_status = None
        test_count = 0
        
        while True:
            # 更新控制器状态
            controller.update()
            
            # 获取连接状态
            status = controller.get_connection_status()
            print(status)
            
            # 只在状态改变时打印
            if status != last_status:
                test_count += 1
                print(f"\n[{test_count:03d}] 状态更新:")
                print(f"  连接状态: {'✅ 已连接' if status['connected'] else '❌ 断开'}")
                print(f"  控制器名称: {status['controller_name'] or '无'}")
                print(f"  重连中: {'🔄 是' if status['reconnecting'] else '⏹️ 否'}")
                print(f"  手柄ID: {status['joystick_id']}")
                
                if status['connected']:
                    # 测试基本功能
                    left_stick = controller.get_left_stick()
                    right_stick = controller.get_right_stick()
                    a_button = controller.get_button('A')
                    
                    print(f"  左摇杆: ({left_stick[0]:.2f}, {left_stick[1]:.2f})")
                    print(f"  右摇杆: ({right_stick[0]:.2f}, {right_stick[1]:.2f})")
                    print(f"  A按钮: {'按下' if a_button else '释放'}")
                
                last_status = status.copy()
            
            
            time.sleep(0.1)  # 100ms更新频率
            
    except KeyboardInterrupt:
        print("\n\n🛑 测试中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
    finally:
        print("\n🧹 清理资源...")
        controller.cleanup()
        print("✅ 测试完成")

if __name__ == "__main__":
    test_reconnect()
