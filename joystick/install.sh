#!/bin/bash

echo "=== Xbox手柄控制器安装脚本 ==="
echo ""

# 检查Python版本
echo "检查Python版本..."
python3 --version

if [ $? -ne 0 ]; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

echo ""
echo "安装pygame依赖..."
pip3 install pygame

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ pygame安装成功！"
    echo ""
    echo "=== 安装完成 ==="
    echo ""
    echo "使用方法："
    echo "1. 运行程序（会自动等待手柄连接）: python3 main.py"
    echo "2. 运行示例程序: python3 example_usage.py"
    echo "3. 运行按钮测试: python3 main.py test"
    echo "4. 测试等待手柄功能: python3 test_wait_controller.py"
    echo ""
    echo "特性："
    echo "- 自动等待手柄连接，无需提前插入手柄"
    echo "- 支持所有Xbox手柄按键和摇杆"
    echo "- 提供震动反馈功能"
    echo "- 面向对象的设计，易于集成"
    echo ""
    echo "文件说明："
    echo "- xbox_controller.py: Xbox手柄控制器类"
    echo "- main.py: 主程序和测试"
    echo "- example_usage.py: 使用示例"
    echo "- test_wait_controller.py: 等待手柄连接测试"
    echo "- README.md: 详细文档"
    echo ""
else
    echo ""
    echo "✗ pygame安装失败，请手动安装："
    echo "pip3 install pygame"
    echo ""
fi
