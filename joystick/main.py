#!/usr/bin/env python3
"""
Xbox手柄测试主程序
演示如何使用XboxController类获取所有按键状态
"""

import pygame
import time
import sys
from xbox_controller import XboxController


def main():
    """主函数"""
    try:
        # 初始化Xbox手柄（会自动等待手柄连接）
        controller = XboxController(0, wait_for_controller=True)
        
        print("\n=== Xbox手柄测试程序 ===")
        print("按下手柄上的任意按键或移动摇杆来测试")
        print("按下START键或Ctrl+C退出程序")
        print("-" * 50)
        
        running = True
        last_print_time = time.time()
        
        while running:
            # 更新手柄状态
            controller.update()
            
            # 处理pygame事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                
                # 按钮按下事件
                elif event.type == pygame.JOYBUTTONDOWN:
                    button_id = event.button
                    button_name = None
                    
                    # 查找按钮名称
                    for name, id in controller.BUTTON_MAPPING.items():
                        if id == button_id:
                            button_name = name
                            break
                    
                    print(f"按钮按下: {button_name} f'按钮{button_id}'")
                    
                    # 特殊按钮处理
                    # if button_name == 'A':
                    #     controller.rumble(0.8, 0.2, 500)  # A键：强震动
                    #     print("  -> 触发强震动")
                    # elif button_name == 'START':
                    #     print("检测到START键")
                    #     # running = False
                    
          
                
                # 按钮释放事件
                elif event.type == pygame.JOYBUTTONUP:
                    button_id = event.button
                    button_name = None
                    
                    for name, id in controller.BUTTON_MAPPING.items():
                        if id == button_id:
                            button_name = name
                            break
                    
                    print(f"按钮释放: {button_name} f'按钮{button_id}'")
                    
                
                # 摇杆/轴移动事件
                elif event.type == pygame.JOYAXISMOTION:
                    axis_id = event.axis
                    axis_value = event.value
                    
                    axis_name = None
                    for name, id in controller.AXIS_MAPPING.items():
                        if id == axis_id:
                            axis_name = name
                            break
                    
                    # 只显示明显的移动（避免噪音）
                    if abs(axis_value) > 0.05:
                        print(f"轴移动: {axis_name or f'轴{axis_id}'} = {axis_value:.3f}")

                    # 扳机震动反馈
                    if axis_name == 'LT':
                        lt_norm = (axis_value + 1.0) / 2.0
                        controller.rumble(lt_norm, 0.0, 1000)
                    elif axis_name == 'RT':
                        rt_norm = (axis_value + 1.0) / 2.0
                        controller.rumble(0.0, rt_norm, 1000)
                
                # 方向键事件
                elif event.type == pygame.JOYHATMOTION:
                    hat_value = event.value
                    
                    if hat_value != (0, 0):
                        direction = None
                        for name, value in controller.DPAD_MAPPING.items():
                            if value == hat_value:
                                direction = name
                                break
                        
                        print(f"方向键: {direction or hat_value}")
            
            # 每秒显示一次完整状态（可选）
            # current_time = time.time()
            # if current_time - last_print_time > 2.0:  # 每2秒显示一次
            #     print("\n--- 当前状态 ---")
            #     controller.print_all_states()
            #     print("-" * 20)
            #     last_print_time = current_time
            
            # 检查特定按键组合
            if controller.get_button('LB') and controller.get_button('RB'):
                print("检测到LB+RB组合键！")
                time.sleep(0.5)  # 避免重复触发
            
            if controller.get_button('LEFT_STICK'):
                print("LEFT_STICK")
             
            # 短暂延迟
            time.sleep(0.01)
    
    except RuntimeError as e:
        print(f"错误: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"未知错误: {e}")
        return 1
    finally:
        # 清理资源
        if 'controller' in locals():
            controller.cleanup()
        print("程序已退出")
    
    return 0


def test_all_buttons():
    """测试所有按钮的函数"""
    try:
        controller = XboxController(0)
        
        print("\n=== 按钮映射测试 ===")
        print("请按下以下按钮进行测试：")
        
        button_names = list(controller.BUTTON_MAPPING.keys())
        tested_buttons = set()
        
        while len(tested_buttons) < len(button_names):
            controller.update()
            
            for button_name in button_names:
                if button_name not in tested_buttons and controller.get_button(button_name):
                    print(f"✓ {button_name} 按钮测试通过")
                    tested_buttons.add(button_name)
                    controller.rumble(0.3, 0.3, 100)
            
            time.sleep(0.05)
        
        print("所有按钮测试完成！")
        controller.cleanup()
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    # 可以选择运行主程序或按钮测试
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_all_buttons()
    else:
        sys.exit(main())
