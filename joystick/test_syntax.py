#!/usr/bin/env python3
"""
语法测试脚本 - 不需要pygame运行
"""

def test_syntax():
    """测试代码语法"""
    try:
        # 测试导入语法
        import sys
        import os
        
        # 检查文件是否存在
        files_to_check = [
            'xbox_controller.py',
            'main.py', 
            'example_usage.py'
        ]
        
        for file in files_to_check:
            if os.path.exists(file):
                print(f"✓ {file} 文件存在")
                
                # 尝试编译文件检查语法
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                try:
                    compile(content, file, 'exec')
                    print(f"✓ {file} 语法正确")
                except SyntaxError as e:
                    print(f"✗ {file} 语法错误: {e}")
            else:
                print(f"✗ {file} 文件不存在")
        
        print("\n语法检查完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_syntax()
