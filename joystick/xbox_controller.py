from colorama import Fore
import pygame
from pygame import joystick
import time
import threading
from typing import Dict, <PERSON><PERSON>, Optional
from colorama import Fore,init,Style,Back
init(autoreset=True)

class XboxController:
    """Xbox手柄控制器类，包含所有按键和摇杆的映射"""
    
    # Xbox手柄按键映射
    BUTTON_MAPPING_PS = {
        'A': 0,
        'B': 1, 
        'X': 2,
        'Y': 3,
        'VIEW': 4,  # 小键1
        'XBOX': 5,  # 灯键
        'MENU': 6,  # 小键3
        'LEFT_STICK': 7,  # 左摇杆按下
        'RIGHT_STICK': 8,  # 右摇杆按下
        'LB': 9,  # 左肩键
        'RB': 10,  # Xbox键（如果支持）
        'REC': 15,  # 小键2
        'UP': 11,  # 
        'DOWN':12,
        'LEFT':13,
        'RIGHT':14
    }
    BUTTON_MAPPING_XBOX = {
        'A': 0,
        'B': 1, 
        'X': 3,
        'Y': 2,
        'VIEW': 8,  # 小键1
        'XBOX': 10,  # 灯键
        'MENU': 9,  # 小键3
        'LEFT_STICK': 11,  # 左摇杆按下
        'RIGHT_STICK': 12,  # 右摇杆按下
        'LB': 4,  # 左肩键
        'RB': 5,  # Xbox键（如果支持）
        'REC': 15,  # 小键2
        # 'UP': 7, 
        # 'DOWN':6,
        # 'LEFT':13,
        # 'RIGHT':14
    }
    
    BUTTON_MAPPING=BUTTON_MAPPING_PS
    
    # 轴映射
    AXIS_MAPPING_PS = {
        'LEFT_STICK_X': 0,   # 左摇杆X轴
        'LEFT_STICK_Y': 1,   # 左摇杆Y轴
        'RIGHT_STICK_X': 2,  # 右摇杆X轴
        'RIGHT_STICK_Y': 3,  # 右摇杆Y轴
        'LT': 4,             # 左扳机
        'RT': 5,             # 右扳机
    }
    AXIS_MAPPING_XBOX = {
        'LEFT_STICK_X': 0,   # 左摇杆X轴
        'LEFT_STICK_Y': 1,   # 左摇杆Y轴
        'RIGHT_STICK_X': 3,  # 右摇杆X轴
        'RIGHT_STICK_Y': 4,  # 右摇杆Y轴
        'LT': 2,             # 左扳机
        'RT': 5,             # 右扳机
    }
    
    AXIS_MAPPING=AXIS_MAPPING_PS
    
    # 方向键映射（通过hat获取）
    DPAD_MAPPING = {
        'UP': (0, 1),
        'DOWN': (0, -1),
        'LEFT': (-1, 0),
        'RIGHT': (1, 0),
        'UP_LEFT': (-1, 1),
        'UP_RIGHT': (1, 1),
        'DOWN_LEFT': (-1, -1),
        'DOWN_RIGHT': (1, -1),
        'CENTER': (0, 0),
    }
    
    def __init__(self, joystick_id: int = 0, wait_for_controller: bool = True):
        """
        初始化Xbox手柄 - 使用pygame事件系统进行连接检测

        Args:
            joystick_id: 手柄ID，默认为0（第一个手柄）
            wait_for_controller: 是否等待手柄连接，默认为True
        """
        pygame.init()
        joystick.init()

        self.joystick_id = joystick_id
        self.joystick = None
        self.name = ""
        self.num_buttons = 0
        self.num_axes = 0
        self.num_hats = 0
        
        self.deadzone = 0   # 摇杆死区

        # 基于pygame事件的连接管理 (参考xbox_connection_test.py)
        self.connected_controllers = {}
        self.check_interval = 0.01  # 检查间隔(秒)
        self.last_check_time = 0
        self.is_connected = False

        # 监控线程管理
        self.monitor_thread = None
        self.monitor_running = False
        self._lock = threading.Lock()

        # 按键状态缓存
        self._button_states = {}
        self._axis_states = {}
        self._hat_states = {}

        # 启动连接监控
        self._start_connection_monitor()

        if wait_for_controller:
            self._wait_for_controller()
        else:
            self._check_and_connect_controllers()

    def _start_connection_monitor(self):
        """启动连接监控线程"""
        if not self.monitor_running:
            self.monitor_running = True
            self.monitor_thread = threading.Thread(target=self._connection_monitor_loop, daemon=False)
            self.monitor_thread.start()
            print("🔄 启动Xbox控制器连接监控线程")

    def _stop_connection_monitor(self):
        """停止连接监控线程"""
        self.monitor_running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            print("⏹️ 停止Xbox控制器连接监控线程")

    def _connection_monitor_loop(self):
        """连接监控循环 - 参考xbox_connection_test.py实现"""
        while self.monitor_running:
            try:
                new_connections, lost_connections = self._check_connections()

                # 处理新连接
                for controller in new_connections:
                    if self.joystick is None:
                        self._set_active_controller(controller)
                        
                        joystick_name=controller.get_name()
                        print(f"✅Xbox控制器已连接: {joystick_name}")
                        
                        if joystick_name.find("Sony Computer Entertainment Wireless Controller")>=0:
                            self.BUTTON_MAPPING=self.BUTTON_MAPPING_XBOX
                            self.AXIS_MAPPING=self.AXIS_MAPPING_XBOX
                        
                        print(self.BUTTON_MAPPING)

                # 处理断开连接
                for controller in lost_connections:
                    if self.joystick and controller.get_instance_id() == self.joystick.get_instance_id():
                        self._handle_controller_disconnect()
                        # 尝试使用其他连接的控制器
                        if self.connected_controllers:
                            new_controller = list(self.connected_controllers.values())[0]
                            self._set_active_controller(new_controller)
                            print(f"🔄 切换到控制器: {new_controller.get_name()}")
                        else:
                            print(Fore.RED+"❌ Xbox控制器已断开，等待重新连接...")

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"连接监控循环错误: {e}")
                time.sleep(1)

    def _check_connections(self):
        """
        检查控制器连接状态并处理事件 - 参考xbox_connection_test.py实现
        返回: (新连接的控制器列表, 断开的控制器列表)
        """
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return [], []

        self.last_check_time = current_time
        pygame.event.pump()  # 处理事件队列

        # 获取当前连接的控制器
        current_joysticks = {}
        for i in range(joystick.get_count()):
            joy = joystick.Joystick(i)
            joy.init()
            current_joysticks[joy.get_instance_id()] = joy

        # print(current_joysticks)
        # 找出变化
        new_connections = []
        lost_connections = []

        # 检查新连接
        for joy_id, joy in current_joysticks.items():
            if joy_id not in self.connected_controllers:
                new_connections.append(joy)

        # 检查断开连接
        for joy_id, joy in self.connected_controllers.items():
            if joy_id not in current_joysticks:
                lost_connections.append(joy)

        # 更新状态
        self.connected_controllers = current_joysticks

        return new_connections, lost_connections

    def _check_and_connect_controllers(self):
        """检查并连接可用的控制器"""
        try:
            # 获取当前连接的控制器
            current_joysticks = {}
            for i in range(joystick.get_count()):
                joy = joystick.Joystick(i)
                joy.init()
                current_joysticks[joy.get_instance_id()] = joy

                # 如果还没有主控制器，设置第一个为主控制器
                if self.joystick is None:
                    self._set_active_controller(joy)
                    
                    joystick_name=joy.get_name()
                    print(f"✅Xbox控制器已连接: {joystick_name}")
                    
                    if joystick_name.index("Sony Computer Entertainment Wireless Controller")>=0:
                        self.BUTTON_MAPPING=self.BUTTON_MAPPING_XBOX
                        self.AXIS_MAPPING=self.AXIS_MAPPING_XBOX
                    
                    print(self.BUTTON_MAPPING)
                        

            # 更新连接状态
            self.connected_controllers = current_joysticks

            if joystick.get_count() == 0:
                self.joystick = None
                self.is_connected = False
                print("⏳ 等待Xbox控制器连接...")

        except Exception as e:
            print(f"检查控制器连接失败: {e}")

    def _set_active_controller(self, controller):
        """设置活动控制器"""
        with self._lock:
            self.joystick = controller
            self.name = controller.get_name()
            self.num_buttons = controller.get_numbuttons()
            self.num_axes = controller.get_numaxes()
            self.num_hats = controller.get_numhats()
            self.is_connected = True

            # 清空状态缓存
            self._button_states.clear()
            self._axis_states.clear()
            self._hat_states.clear()

    def _handle_controller_disconnect(self):
        """处理控制器断开"""
        with self._lock:
            if self.is_connected:
                self.is_connected = False

                # 清理控制器对象
                if self.joystick:
                    try:
                        self.joystick.quit()
                    except:
                        pass
                    self.joystick = None

                # 清空状态
                self._button_states.clear()
                self._axis_states.clear()
                self._hat_states.clear()
                self.name = ""
                self.num_buttons = 0
                self.num_axes = 0
                self.num_hats = 0

    def is_xbox_controller(self, joystick_obj):
        """检查是否是Xbox控制器"""
        name = joystick_obj.get_name().lower()
        return "xbox" in name or "x-box" in name or "xinput" in name or "ps4" in name

    def get_controller_info(self, joystick_obj):
        """获取控制器详细信息"""
        return {
            "id": joystick_obj.get_instance_id(),
            "name": joystick_obj.get_name(),
            "guid": joystick_obj.get_guid(),
            "num_axes": joystick_obj.get_numaxes(),
            "num_buttons": joystick_obj.get_numbuttons(),
            "num_hats": joystick_obj.get_numhats(),
            "is_xbox": self.is_xbox_controller(joystick_obj)
        }

    def _wait_for_controller(self):
        """等待手柄连接"""
        print("正在等待Xbox手柄连接...")
        print("请连接手柄后按任意键...")

        while True:
            joystick.quit()
            joystick.init()

            joystick_count = joystick.get_count()
            if joystick_count > 0:
                if self.joystick_id < joystick_count:
                    self._connect_controller()
                    break
                else:
                    print(f"手柄ID {self.joystick_id} 超出范围，当前有 {joystick_count} 个手柄")

            time.sleep(1)  # 每秒检查一次

    def _try_connect(self):
        """尝试连接手柄，不等待"""
        joystick.quit()
        joystick.init()
        joystick_count = joystick.get_count()

        if joystick_count == 0:
            print("未检测到手柄，监控线程将自动检测连接...")
            self.is_connected = False
            return

        if self.joystick_id >= joystick_count:
            print(f"手柄ID {self.joystick_id} 超出范围，当前有 {joystick_count} 个手柄")
            self.is_connected = False
            return

        self._connect_controller()

    def _connect_controller(self):
        """连接手柄"""
        with self._lock:
            try:
                # 初始化手柄
                self.joystick = joystick.Joystick(self.joystick_id)
                self.joystick.init()

                self.name = self.joystick.get_name()
                self.num_buttons = self.joystick.get_numbuttons()
                self.num_axes = self.joystick.get_numaxes()
                self.num_hats = self.joystick.get_numhats()
                self.is_connected = True

                print(f"✓ 手柄已连接: {self.name}")
                print(f"  按钮数量: {self.num_buttons}")
                print(f"  轴数量: {self.num_axes}")
                print(f"  方向键数量: {self.num_hats}")
                print("")

            except Exception as e:
                print(f"连接手柄失败: {e}")
                self.is_connected = False
                raise
            
    def check_connections(self):
        """
        检查控制器连接状态并处理事件
        返回: (新连接的控制器列表, 断开的控制器列表)
        """
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return [], []

        self.last_check_time = current_time
        
        # pygame.event.pump()  # 处理事件队列

        print(joystick.get_count(),pygame.joystick.get_count())
        
        # 获取当前连接的控制器
        current_joysticks = {}
        for i in range(joystick.get_count()):
            joy = joystick.Joystick(i)
            joy.init()
            current_joysticks[joy.get_instance_id()] = joy

        # 找出变化
        new_connections = []
        lost_connections = []

        # 检查新连接
        for joy_id, joy in current_joysticks.items():
            if joy_id not in self.connected_controllers:
                new_connections.append(joy)

        # 检查断开连接
        for joy_id, joy in self.connected_controllers.items():
            if joy_id not in current_joysticks:
                lost_connections.append(joy)

        # 更新状态
        self.connected_controllers = current_joysticks

        return new_connections, lost_connections
    
    def get_controller_info(self, joystick):
        """
        获取控制器详细信息
        """
        return {
            "id": joystick.get_instance_id(),
            "name": joystick.get_name(),
            "guid": joystick.get_guid(),
            "num_axes": joystick.get_numaxes(),
            "num_buttons": joystick.get_numbuttons(),
            "num_hats": joystick.get_numhats()
        }
        
    def check_connection(self):
        new_cons, lost_cons = self.check_connections()
        # print(new_cons,lost_cons)
        
        for controller in new_cons:
            info = self.get_controller_info(controller)
            print(f"ID: {info['id']}")
            print(f"名称: {info['name']}")
            print(f"GUID: {info['guid']}")
            print(f"轴: {info['num_axes']}, 按钮: {info['num_buttons']}, 方向键: {info['num_hats']}")
        
        for controller in lost_cons:
            info = self.get_controller_info(controller)
            if info["is_xbox"]:
                print(Fore.RED+f"\n[Xbox控制器已断开] ID: {info['id']}, 名称: {info['name']}")
            else:
                print(Fore.RED+f"\n[控制器已断开] ID: {info['id']}, 名称: {info['name']} (非Xbox)")
            
    def _check_connection(self) -> bool:
        """检查手柄连接状态"""
        try:
            if not self.joystick:
                return False

            # 尝试获取手柄状态来检测连接
            if hasattr(self.joystick, 'get_init') and not self.joystick.get_init():
                return False

            # 尝试读取一个按钮状态来测试连接
            if self.num_buttons > 0:
                self.joystick.get_button(0)

            # print(f"connection true{'-'*30}")
            return True
        except (pygame.error, OSError, AttributeError):
            # print(f"connection false{'*'*30}")
            return False

    def update(self):
        """更新手柄状态，需要在主循环中调用 - 连接检测由监控线程处理"""
        if not self.is_connected or self.joystick is None:
            return

        try:
            # 更新按钮状态
            for button_name, button_id in self.BUTTON_MAPPING.items():
                if button_id < self.num_buttons:
                    self._button_states[button_name] = self.joystick.get_button(button_id)

            # 更新轴状态
            for axis_name, axis_id in self.AXIS_MAPPING.items():
                if axis_id < self.num_axes:
                    self._axis_states[axis_name] = self.joystick.get_axis(axis_id)

            # 更新方向键状态
            if self.num_hats > 0:
                hat_value = self.joystick.get_hat(0)
                self._hat_states['DPAD'] = hat_value
                
            # self.print_all_states()

        except (pygame.error, OSError, AttributeError) as e:
            print(f"更新手柄状态时发生错误: {e}")
            # 错误会由监控线程检测到并处理
    
    def get_button(self, button_name: str) -> bool:
        """
        获取按钮状态

        Args:
            button_name: 按钮名称（如'A', 'B', 'X', 'Y'等）

        Returns:
            bool: 按钮是否被按下
        """
        if not self.is_connected:
            return False
        return self._button_states.get(button_name, False)
    
    def get_axis(self, axis_name: str) -> float:
        """
        获取轴值

        Args:
            axis_name: 轴名称（如'LEFT_STICK_X', 'RIGHT_STICK_Y'等）

        Returns:
            float: 轴值（-1.0到1.0）
        """
        if not self.is_connected:
            return 0.0
        return self._axis_states.get(axis_name, 0.0)
    
    def get_left_stick(self) -> Tuple[float, float]:
        """获取左摇杆位置"""
        x = self.get_axis('LEFT_STICK_X')
        y = self.get_axis('LEFT_STICK_Y')
        return (x, y)
    
    def get_right_stick(self) -> Tuple[float, float]:
        """获取右摇杆位置"""
        x = self.get_axis('RIGHT_STICK_X')
        y = self.get_axis('RIGHT_STICK_Y')
        return (x, y)
    
    def get_triggers(self) -> Tuple[float, float]:
        """
        获取扳机值
        
        Returns:
            Tuple[float, float]: (左扳机值, 右扳机值)，范围-1.0到1.0
        """
        lt = self.get_axis('LT')
        rt = self.get_axis('RT')
        return (lt, rt)
    
    def get_triggers_normalized(self) -> Tuple[float, float]:
        """
        获取标准化的扳机值（0.0到1.0）
        
        Returns:
            Tuple[float, float]: (左扳机值, 右扳机值)，范围0.0到1.0
        """
        lt, rt = self.get_triggers()
        lt_norm = (lt + 1.0) / 2.0
        rt_norm = (rt + 1.0) / 2.0
        return (lt_norm, rt_norm)
    
    def get_dpad(self) -> Tuple[int, int]:
        """
        获取方向键状态
        
        Returns:
            Tuple[int, int]: (x, y)方向，(-1, 0, 1)
        """
        return self._hat_states.get('DPAD', (0, 0))
    
    def is_dpad_pressed(self, direction: str) -> bool:
        """
        检查特定方向键是否被按下
        
        Args:
            direction: 方向名称（'UP', 'DOWN', 'LEFT', 'RIGHT'等）
            
        Returns:
            bool: 是否被按下
        """
        current_dpad = self.get_dpad()
        target_dpad = self.DPAD_MAPPING.get(direction, (0, 0))
        return current_dpad == target_dpad
    
    def rumble(self, low_freq: float = 0.0, high_freq: float = 0.0, duration_ms: int = 1000):
        """
        手柄震动
        
        Args:
            low_freq: 低频震动强度（0.0-1.0）
            high_freq: 高频震动强度（0.0-1.0）
            duration_ms: 持续时间（毫秒）
        """
        if hasattr(self.joystick, 'rumble'):
            self.joystick.rumble(low_freq, high_freq, duration_ms)
            print(f"震动: 低频={low_freq:.2f}, 高频={high_freq:.2f}, 持续={duration_ms}ms")
        else:
            print("当前手柄不支持震动功能")
    
    def get_all_states(self) -> Dict:
        """
        获取所有按键和轴的状态
        
        Returns:
            Dict: 包含所有状态的字典
        """
        return {
            'buttons': self._button_states.copy(),
            'axes': self._axis_states.copy(),
            'dpad': self._hat_states.get('DPAD', (0, 0)),
            'left_stick': self.get_left_stick(),
            'right_stick': self.get_right_stick(),
            'triggers': self.get_triggers(),
            'triggers_normalized': self.get_triggers_normalized(),
        }
    
    def print_all_states(self):
        """打印所有状态信息"""
        states = self.get_all_states()
        
        # 打印按钮状态
        pressed_buttons = [name for name, pressed in states['buttons'].items() if pressed]
        if pressed_buttons:
            print(f"按下的按钮: {', '.join(pressed_buttons)}")
        
        # 打印摇杆状态
        left_x, left_y = states['left_stick']
        right_x, right_y = states['right_stick']
        if abs(left_x) > 0.2 or abs(left_y) > 0.2:
            print(f"左摇杆: ({left_x:.2f}, {left_y:.2f})")
        if abs(right_x) > 0.2 or abs(right_y) > 0.2:
            print(f"右摇杆: ({right_x:.2f}, {right_y:.2f})")
        
        # 打印扳机状态
        lt_norm, rt_norm = states['triggers_normalized']
        if lt_norm > 0.2:
            print(f"左扳机: {lt_norm:.2f}")
        if rt_norm > 0.2:
            print(f"右扳机: {rt_norm:.2f}")
        
        # 打印方向键状态
        dpad_x, dpad_y = states['dpad']
        if dpad_x != 0 or dpad_y != 0:
            dpad_direction = None
            for direction, (x, y) in self.DPAD_MAPPING.items():
                if (x, y) == (dpad_x, dpad_y):
                    dpad_direction = direction
                    break
            print(f"方向键: {dpad_direction or f'({dpad_x}, {dpad_y})'}")
    
    def is_controller_connected(self) -> bool:
        """检查手柄是否连接"""
        return self.is_connected

    def get_connection_status(self) -> Dict[str, any]:
        """获取连接状态信息"""
        return {
            'connected': self.is_connected,
            'controller_name': self.name if self.is_connected else None,
            'monitoring': self.monitor_running,
            'joystick_id': self.joystick_id,
            'connected_count': len(self.connected_controllers)
        }

    def cleanup(self):
        """清理资源"""
        print("🧹 清理Xbox控制器资源...")

        # 停止连接监控线程
        self._stop_connection_monitor()

        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1)

        # 清理手柄对象
        with self._lock:
            if self.joystick:
                try:
                    self.joystick.quit()
                except:
                    pass
                self.joystick = None

            self.is_connected = False
            self._button_states.clear()
            self._axis_states.clear()
            self._hat_states.clear()
            self.connected_controllers.clear()

        # 清理pygame joystick系统
        try:
            joystick.quit()
        except:
            pass

        print("✅ Xbox控制器资源清理完成")
