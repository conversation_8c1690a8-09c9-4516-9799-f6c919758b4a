# Xbox手柄控制器

这是一个用于获取Xbox手柄输入的Python库，基于pygame实现。

## 文件说明

- `xbox_controller.py` - Xbox手柄控制器类
- `main.py` - 主程序，演示手柄的基本使用
- `example_usage.py` - 使用示例，包含游戏控制演示
- `joystick.py` - 原始的手柄测试代码

## 安装依赖

```bash
pip install pygame
```

## 快速开始

### 1. 基本使用

```python
from xbox_controller import XboxController

# 创建手柄实例（自动等待手柄连接）
controller = XboxController(wait_for_controller=True)

# 主循环
while True:
    # 更新手柄状态
    controller.update()

    # 检查按钮
    if controller.get_button('A'):
        print("A键被按下")

    # 获取摇杆位置
    left_x, left_y = controller.get_left_stick()
    print(f"左摇杆: ({left_x:.2f}, {left_y:.2f})")

    # 清理资源
    controller.cleanup()
```

### 2. 等待手柄连接

```python
# 自动等待手柄连接（默认行为）
controller = XboxController(0, wait_for_controller=True)

# 不等待，立即尝试连接（如果没有手柄会抛出异常）
try:
    controller = XboxController(0, wait_for_controller=False)
except RuntimeError as e:
    print(f"手柄连接失败: {e}")
```

### 3. 运行示例程序

```bash
# 运行主程序（会自动等待手柄连接）
python3 main.py

# 运行按钮测试
python3 main.py test

# 运行使用示例
python3 example_usage.py

# 测试等待手柄连接功能
python3 test_wait_controller.py
```

## Xbox手柄按键映射

### 按钮 (Buttons)
- `A` - A键 (按钮0)
- `B` - B键 (按钮1)
- `X` - X键 (按钮2)
- `Y` - Y键 (按钮3)
- `LB` - 左肩键 (按钮4)
- `RB` - 右肩键 (按钮5)
- `BACK` - 返回键/视图键 (按钮6)
- `START` - 开始键/菜单键 (按钮7)
- `LEFT_STICK` - 左摇杆按下 (按钮8)
- `RIGHT_STICK` - 右摇杆按下 (按钮9)
- `XBOX` - Xbox键 (按钮10，如果支持)

### 轴 (Axes)
- `LEFT_STICK_X` - 左摇杆X轴 (轴0)
- `LEFT_STICK_Y` - 左摇杆Y轴 (轴1)
- `RIGHT_STICK_X` - 右摇杆X轴 (轴2)
- `RIGHT_STICK_Y` - 右摇杆Y轴 (轴3)
- `LT` - 左扳机 (轴4)
- `RT` - 右扳机 (轴5)

### 方向键 (D-Pad)
- `UP` - 上 (0, 1)
- `DOWN` - 下 (0, -1)
- `LEFT` - 左 (-1, 0)
- `RIGHT` - 右 (1, 0)
- `UP_LEFT` - 左上 (-1, 1)
- `UP_RIGHT` - 右上 (1, 1)
- `DOWN_LEFT` - 左下 (-1, -1)
- `DOWN_RIGHT` - 右下 (1, -1)
- `CENTER` - 中心 (0, 0)

## API参考

### XboxController类

#### 初始化
```python
# 自动等待手柄连接
controller = XboxController(joystick_id=0, wait_for_controller=True)

# 立即尝试连接，不等待
controller = XboxController(joystick_id=0, wait_for_controller=False)
```

#### 主要方法

- `update()` - 更新手柄状态（必须在主循环中调用）
- `get_button(button_name)` - 获取按钮状态
- `get_axis(axis_name)` - 获取轴值
- `get_left_stick()` - 获取左摇杆位置
- `get_right_stick()` - 获取右摇杆位置
- `get_triggers()` - 获取扳机值（-1.0到1.0）
- `get_triggers_normalized()` - 获取标准化扳机值（0.0到1.0）
- `get_dpad()` - 获取方向键状态
- `is_dpad_pressed(direction)` - 检查特定方向键
- `rumble(low_freq, high_freq, duration_ms)` - 手柄震动
- `get_all_states()` - 获取所有状态
- `print_all_states()` - 打印所有状态
- `cleanup()` - 清理资源

## 使用示例

### 检查按钮
```python
if controller.get_button('A'):
    print("A键被按下")
    controller.rumble(0.5, 0.5, 200)  # 震动反馈
```

### 获取摇杆输入
```python
left_x, left_y = controller.get_left_stick()
if abs(left_x) > 0.1 or abs(left_y) > 0.1:
    print(f"左摇杆移动: ({left_x:.2f}, {left_y:.2f})")
```

### 检查扳机
```python
lt, rt = controller.get_triggers_normalized()
if lt > 0.1:
    print(f"左扳机: {lt:.2f}")
if rt > 0.1:
    print(f"右扳机: {rt:.2f}")
```

### 检查方向键
```python
if controller.is_dpad_pressed('UP'):
    print("方向键上被按下")
```

### 按钮组合
```python
if controller.get_button('LB') and controller.get_button('RB'):
    print("LB+RB组合键")
```

## 注意事项

1. 确保手柄已正确连接到电脑
2. 在主循环中必须调用 `controller.update()`
3. 程序结束时调用 `controller.cleanup()` 清理资源
4. 轴值范围为-1.0到1.0，可能存在轻微的噪音
5. 扳机值在某些系统上可能需要标准化处理

## 故障排除

### 手柄未检测到
- 检查手柄是否正确连接
- 确认手柄驱动已安装
- 尝试在其他程序中测试手柄

### 按钮映射不正确
- 不同品牌的手柄可能有不同的按钮映射
- 可以运行 `python main.py test` 来测试按钮映射
- 根据需要修改 `BUTTON_MAPPING` 字典

### 震动不工作
- 确认手柄支持震动功能
- 检查系统是否启用了手柄震动
- 某些手柄可能需要特殊驱动
