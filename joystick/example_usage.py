#!/usr/bin/env python3
"""
Xbox手柄使用示例
展示如何在实际项目中使用XboxController类
"""

import time
from xbox_controller import XboxController


def simple_example():
    """简单使用示例"""
    print("=== 简单使用示例 ===")
    
    try:
        # 创建手柄实例（会自动等待手柄连接）
        controller = XboxController(wait_for_controller=True)
        
        print("按下A键测试，按MENU键退出...")
        
        while True:
            controller.update()
            
            # 检查A键
            if controller.get_button('A'):
                print("A键被按下！")
                controller.rumble(0.5, 0.5, 200)
            
            # 检查摇杆
            left_x, left_y = controller.get_left_stick()
            if abs(left_x) > 0.5 or abs(left_y) > 0.5:
                print(f"左摇杆移动: ({left_x:.2f}, {left_y:.2f})")
            
            # 检查扳机
            lt, rt = controller.get_triggers_normalized()
            if lt > 0.5:
                print(f"左扳机: {lt:.2f}")
            if rt > 0.5:
                print(f"右扳机: {rt:.2f}")
            
            # 退出条件
            if controller.get_button('MENU'):
                break
            
            time.sleep(0.1)
    
    except Exception as e:
        print(f"错误: {e}")
    finally:
        controller.cleanup()


def game_control_example():
    """游戏控制示例"""
    print("\n=== 游戏控制示例 ===")
    
    try:
        controller = XboxController(wait_for_controller=True)
        
        print("模拟游戏控制...")
        print("左摇杆：移动角色")
        print("右摇杆：控制视角")
        print("A键：跳跃")
        print("B键：攻击")
        print("扳机：加速/减速")
        print("START键：退出")
        
        player_x, player_y = 0.0, 0.0
        camera_x, camera_y = 0.0, 0.0
        speed = 1.0
        
        while True:
            controller.update()
            
            # 角色移动（左摇杆）
            move_x, move_y = controller.get_left_stick()
            if abs(move_x) > 0.1 or abs(move_y) > 0.1:
                player_x += move_x * speed * 0.1
                player_y += move_y * speed * 0.1
                print(f"角色位置: ({player_x:.1f}, {player_y:.1f})")
            
            # 视角控制（右摇杆）
            look_x, look_y = controller.get_right_stick()
            if abs(look_x) > 0.1 or abs(look_y) > 0.1:
                camera_x += look_x * 2.0
                camera_y += look_y * 2.0
                print(f"视角: ({camera_x:.1f}, {camera_y:.1f})")
            
            # 速度控制（扳机）
            lt, rt = controller.get_triggers_normalized()
            if rt > 0.1:  # 右扳机加速
                speed = 1.0 + rt * 2.0
                print(f"加速: {speed:.1f}x")
            elif lt > 0.1:  # 左扳机减速
                speed = 1.0 - lt * 0.8
                print(f"减速: {speed:.1f}x")
            else:
                speed = 1.0
            
            # 动作按钮
            if controller.get_button('A'):
                print("跳跃！")
                controller.rumble(0.3, 0.3, 100)
                time.sleep(0.2)  # 避免重复
            
            if controller.get_button('B'):
                print("攻击！")
                controller.rumble(0.8, 0.2, 150)
                time.sleep(0.2)
            
            if controller.get_button('X'):
                print("使用道具")
                time.sleep(0.2)
            
            if controller.get_button('Y'):
                print("互动")
                time.sleep(0.2)
            
            # 方向键快捷操作
            if controller.is_dpad_pressed('UP'):
                print("快捷键：向上")
            elif controller.is_dpad_pressed('DOWN'):
                print("快捷键：向下")
            elif controller.is_dpad_pressed('LEFT'):
                print("快捷键：向左")
            elif controller.is_dpad_pressed('RIGHT'):
                print("快捷键：向右")
            
            # 退出
            if controller.get_button('START'):
                break
            
            time.sleep(0.05)
    
    except Exception as e:
        print(f"错误: {e}")
    finally:
        controller.cleanup()


def button_mapping_info():
    """显示按钮映射信息"""
    print("\n=== Xbox手柄按钮映射 ===")
    
    controller = XboxController(wait_for_controller=False)  # 不等待，直接显示信息
    
    print("按钮映射:")
    for name, id in controller.BUTTON_MAPPING.items():
        print(f"  {name}: 按钮{id}")
    
    print("\n轴映射:")
    for name, id in controller.AXIS_MAPPING.items():
        print(f"  {name}: 轴{id}")
    
    print("\n方向键映射:")
    for name, value in controller.DPAD_MAPPING.items():
        print(f"  {name}: {value}")
    
    controller.cleanup()


if __name__ == "__main__":
    # 显示按钮映射信息
    button_mapping_info()
    
    # 运行简单示例
    simple_example()
    
    # 运行游戏控制示例
    game_control_example()
