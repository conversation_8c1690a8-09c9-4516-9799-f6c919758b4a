#!/usr/bin/env python3
"""
测试等待手柄连接功能
"""

import time
from xbox_controller import XboxController


def test_wait_for_controller():
    """测试等待手柄连接功能"""
    print("=== 测试等待手柄连接功能 ===")
    print("这个测试会等待您连接Xbox手柄")
    print("请在测试开始后连接或断开手柄来测试功能")
    print("按Ctrl+C可以随时退出")
    print("")
    
    try:
        # 测试自动等待手柄连接
        print("1. 测试自动等待手柄连接...")
        controller = XboxController(0, wait_for_controller=True)
        
        print("手柄连接成功！测试基本功能...")
        
        # 简单测试
        test_count = 0
        max_tests = 50  # 测试5秒
        
        while test_count < max_tests:
            controller.update()
            
            # 检查是否有按键被按下
            pressed_buttons = []
            for button_name in controller.BUTTON_MAPPING.keys():
                if controller.get_button(button_name):
                    pressed_buttons.append(button_name)
            
            if pressed_buttons:
                print(f"检测到按键: {', '.join(pressed_buttons)}")
            
            # 检查摇杆
            left_x, left_y = controller.get_left_stick()
            if abs(left_x) > 0.2 or abs(left_y) > 0.2:
                print(f"左摇杆: ({left_x:.2f}, {left_y:.2f})")
            
            # 检查扳机
            lt, rt = controller.get_triggers_normalized()
            if lt > 0.2:
                print(f"左扳机: {lt:.2f}")
            if rt > 0.2:
                print(f"右扳机: {rt:.2f}")
            
            # 退出条件
            if controller.get_button('START'):
                print("检测到START键，退出测试")
                break
            
            test_count += 1
            time.sleep(0.1)
        
        print("✓ 手柄功能测试完成")
        controller.cleanup()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中出错: {e}")


def test_no_wait_controller():
    """测试不等待手柄连接的情况"""
    print("\n=== 测试不等待手柄连接功能 ===")
    print("这个测试会立即尝试连接手柄，如果没有手柄会报错")
    
    try:
        # 测试不等待手柄连接
        controller = XboxController(0, wait_for_controller=False)
        print("✓ 手柄连接成功（手柄已经连接）")
        controller.cleanup()
        
    except RuntimeError as e:
        print(f"✓ 预期的错误: {e}")
    except Exception as e:
        print(f"✗ 意外错误: {e}")


if __name__ == "__main__":
    print("Xbox手柄等待连接功能测试")
    print("=" * 40)
    
    # 测试等待功能
    test_wait_for_controller()
    
    # 测试不等待功能
    test_no_wait_controller()
    
    print("\n测试完成！")
