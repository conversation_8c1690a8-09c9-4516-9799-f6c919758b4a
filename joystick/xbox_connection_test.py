import threading
import pygame
from pygame import joystick
import time
from colorama import Fore,init,Style,Back
init(autoreset=True)

class XboxControllerMonitor:
    def __init__(self):
        """
        初始化Xbox控制器监视器
        """
        pygame.init()
        joystick.init()
        self.connected_controllers = {}
        self.check_interval = 0.01  # 检查间隔(秒)
        self.last_check_time = 0
        self.current_joystick=None
        
        # 设置事件类型
        self.JOYSTICK_CONNECTED = pygame.JOYDEVICEADDED
        self.JOYSTICK_DISCONNECTED = pygame.JOYDEVICEREMOVED
        
    def check_connections(self):
        """
        检查控制器连接状态并处理事件
        返回: (新连接的控制器列表, 断开的控制器列表)
        """
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return [], []
        
        self.last_check_time = current_time
        pygame.event.pump()  # 处理事件队列
        
        # print(joystick.get_count())
        
        # 获取当前连接的控制器
        current_joysticks = {}
        for i in range(joystick.get_count()):
            joy = joystick.Joystick(i)
            self.current_joystick=joy
            joy.init()
            current_joysticks[joy.get_instance_id()] = joy
            
        if joystick.get_count()==0:
            self.current_joystick=None
            
        # 找出变化
        new_connections = []
        lost_connections = []
        
        # 检查新连接
        for joy_id, joy in current_joysticks.items():
            if joy_id not in self.connected_controllers:
                new_connections.append(joy)
        
        # 检查断开连接
        for joy_id, joy in self.connected_controllers.items():
            if joy_id not in current_joysticks:
                lost_connections.append(joy)
        
        # 更新状态
        self.connected_controllers = current_joysticks
        
        return new_connections, lost_connections
    
    def is_xbox_controller(self, joystick):
        """
        检查是否是Xbox控制器
        """
        name = joystick.get_name().lower()
        return "xbox" in name or "x-box" in name or "xinput" in name
    
    def get_controller_info(self, joystick):
        """
        获取控制器详细信息
        """
        return {
            "id": joystick.get_instance_id(),
            "name": joystick.get_name(),
            "guid": joystick.get_guid(),
            "num_axes": joystick.get_numaxes(),
            "num_buttons": joystick.get_numbuttons(),
            "num_hats": joystick.get_numhats(),
            "is_xbox": self.is_xbox_controller(joystick)
        }
    
    def cleanup(self):
        """
        清理资源
        """
        joystick.quit()
        pygame.quit()



def main():
    monitor = XboxControllerMonitor()
    print("Xbox控制器监视器已启动，等待连接/断开事件...")
    print("按Ctrl+C退出程序")
    
    try:
        while True:
            new_cons, lost_cons = monitor.check_connections()
            
            for controller in new_cons:
                info = monitor.get_controller_info(controller)
                if info["is_xbox"]:
                    print(f"\n[Xbox控制器已连接]")
                else:
                    print(f"\n[控制器已连接] (非Xbox)")
                print(f"ID: {info['id']}")
                print(f"名称: {info['name']}")
                print(f"GUID: {info['guid']}")
                print(f"轴: {info['num_axes']}, 按钮: {info['num_buttons']}, 方向键: {info['num_hats']}")
            
            for controller in lost_cons:
                info = monitor.get_controller_info(controller)
                if info["is_xbox"]:
                    print(Fore.RED+f"\n[Xbox控制器已断开] ID: {info['id']}, 名称: {info['name']}")
                else:
                    print(Fore.RED+f"\n[控制器已断开] ID: {info['id']}, 名称: {info['name']} (非Xbox)")
            
            if monitor.current_joystick:
                if monitor.current_joystick.get_button(0)==1:
                    print(monitor.current_joystick.get_button(0))
                
            time.sleep(0.1)  # 减少CPU占用
                
    except KeyboardInterrupt:
        print("\n正在退出...")


if __name__ == "__main__":
    controller_thread = threading.Thread(target=main, daemon=False)
    controller_thread.start()
