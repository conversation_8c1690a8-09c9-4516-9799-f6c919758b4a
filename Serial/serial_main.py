from serial_port import SerialPort
import time

def data_received_callback(data: bytes):
    """
    接收数据的回调函数
    :param data: 接收到的字节数据
    """
    hex_str = data.hex(' ').upper()
    print(f"回调接收到数据: {hex_str}")
    # 在这里添加你的数据处理逻辑

def main():
    # 创建串口实例
    # 如果不指定端口，会自动选择第一个可用串口
    # serial_port = SerialPort(baudrate=115200)  # 自动选择端口
    # serial_port = SerialPort(port='/dev/ttyACM0', baudrate=115200)  # 手动指定端口
    serial_port = SerialPort(port='/tmp/tty.RemoteUsb', baudrate=115200)  # 手动指定端口
     
    try:
        # 打开串口并设置接收回调
        if not serial_port.open(receive_callback=data_received_callback):
            return
        
        while 1:
            # 示例1: 发送十六进制字符串
            # print("\n示例1: 发送十六进制字符串")
            serial_port.send_hex("01 02 03 04 05 06 07 08 09 10")  # 带空格的字符串
            time.sleep(0.02)
        
        # # 示例2: 发送十六进制列表
        # print("\n示例2: 发送十六进制列表")
        # hex_list = [0xA0, 0x12, 0x34]
        # serial_port.send_hex(hex_list)
        # time.sleep(0.1)
        
        # # 示例3: 发送bytes
        # print("\n示例3: 发送bytes")
        # hex_bytes = b'\xA0\x12\x34'
        # serial_port.send_hex(hex_bytes)
        
        # 主循环 - 在这里可以执行其他任务
        # print("\n主程序继续运行，接收线程在后台工作...")
        # for i in range(10):
        #     print(f"主程序运行中... {i+1}/10")
        #     time.sleep(1)
        
    finally:
        # 确保串口关闭
        serial_port.close()

if __name__ == "__main__":
    main()
