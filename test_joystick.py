#!/usr/bin/env python3
"""
测试Xbox摇杆控制功能
"""

import sys
import os
import time

# 添加joystick目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'joystick'))

try:
    from xbox_controller import XboxController
    print("Xbox控制器模块加载成功")
except ImportError as e:
    print(f"无法导入Xbox控制器模块: {e}")
    sys.exit(1)

def test_joystick():
    """测试摇杆功能"""
    try:
        # 创建控制器实例
        controller = XboxController(wait_for_controller=False)
        print("Xbox控制器连接成功")
        
        print("\n开始测试摇杆控制...")
        print("移动摇杆来测试，按START键退出")
        print("左摇杆: 前后左右移动")
        print("右摇杆: 上下左右控制")
        
        last_values = (0, 0, 0, 0)
        
        while True:
            # 更新控制器状态
            controller.update()
            
            # 获取摇杆数据
            left_x, left_y = controller.get_left_stick()
            right_x, right_y = controller.get_right_stick()
            
            # 转换为整数值 (-100 到 +100)
            left_x_int = int(left_x * 100)
            left_y_int = int(left_y * 100)
            right_x_int = int(right_x * 100)
            right_y_int = int(right_y * 100)
            
            current_values = (left_x_int, left_y_int, right_x_int, right_y_int)
            
            # 只在值发生变化时打印
            if current_values != last_values:
                # 检查是否超过死区
                deadzone = 10
                if (abs(left_x_int) > deadzone or abs(left_y_int) > deadzone or 
                    abs(right_x_int) > deadzone or abs(right_y_int) > deadzone):
                    
                    print(f"🎮 摇杆数据: L({left_x_int:+4d},{left_y_int:+4d}) R({right_x_int:+4d},{right_y_int:+4d})")
                    
                    # 模拟发送到ESP32的数据包
                    command = bytearray([0xAA, 0x30, 0x04])  # 头部 + 命令类型 + 数据长度
                    
                    # 添加摇杆数据（有符号字节）
                    command.append(left_x_int & 0xFF if left_x_int >= 0 else (256 + left_x_int) & 0xFF)
                    command.append(left_y_int & 0xFF if left_y_int >= 0 else (256 + left_y_int) & 0xFF)
                    command.append(right_x_int & 0xFF if right_x_int >= 0 else (256 + right_x_int) & 0xFF)
                    command.append(right_y_int & 0xFF if right_y_int >= 0 else (256 + right_y_int) & 0xFF)
                    
                    # 计算校验和
                    checksum = sum(command[1:]) & 0xFF
                    command.append(checksum)
                    
                    # 显示将要发送的数据包
                    hex_str = ' '.join([f'{b:02X}' for b in command])
                    print(f"📡 数据包: {hex_str}")
                
                last_values = current_values
            
            # 检查退出条件
            if controller.get_button('MENU'):  # START键
                print("退出测试...")
                break
            
            time.sleep(0.05)  # 20Hz更新频率
            
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        if 'controller' in locals():
            controller.cleanup()

if __name__ == "__main__":
    test_joystick()
