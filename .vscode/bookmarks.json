{"files": [{"path": "MobileWeb/server.py", "bookmarks": [{"line": 25, "column": 11, "label": ""}, {"line": 285, "column": 39, "label": ""}, {"line": 729, "column": 32, "label": ""}, {"line": 1317, "column": 18, "label": ""}]}, {"path": "ROV_esp32/src/main.cpp", "bookmarks": [{"line": 286, "column": 5, "label": ""}]}, {"path": "MobileWeb/script.js", "bookmarks": [{"line": 114, "column": 55, "label": ""}, {"line": 132, "column": 39, "label": ""}, {"line": 191, "column": 62, "label": ""}, {"line": 216, "column": 36, "label": ""}, {"line": 615, "column": 56, "label": ""}, {"line": 1063, "column": 45, "label": ""}, {"line": 1094, "column": 59, "label": ""}, {"line": 1340, "column": 62, "label": ""}]}, {"path": "joystick/xbox_controller.py", "bookmarks": [{"line": 31, "column": 27, "label": ""}, {"line": 61, "column": 24, "label": ""}, {"line": 155, "column": 63, "label": ""}, {"line": 485, "column": 27, "label": ""}, {"line": 609, "column": 24, "label": ""}]}, {"path": "MobileWeb/style.css", "bookmarks": [{"line": 142, "column": 14, "label": ""}, {"line": 158, "column": 19, "label": ""}]}, {"path": "App/server.py", "bookmarks": [{"line": 33, "column": 9, "label": ""}, {"line": 55, "column": 27, "label": ""}, {"line": 258, "column": 0, "label": ""}, {"line": 271, "column": 37, "label": ""}, {"line": 390, "column": 51, "label": ""}, {"line": 496, "column": 37, "label": ""}, {"line": 519, "column": 56, "label": ""}]}, {"path": "MobileWeb/scan_ip.html", "bookmarks": [{"line": 199, "column": 37, "label": ""}, {"line": 336, "column": 36, "label": ""}, {"line": 389, "column": 44, "label": ""}, {"line": 418, "column": 17, "label": ""}, {"line": 442, "column": 38, "label": ""}, {"line": 508, "column": 36, "label": ""}]}, {"path": "MobileWeb/index.html", "bookmarks": [{"line": 38, "column": 68, "label": ""}, {"line": 111, "column": 94, "label": ""}, {"line": 267, "column": 63, "label": ""}]}, {"path": "rovRemote/rov_main.py", "bookmarks": [{"line": 26, "column": 15, "label": ""}, {"line": 71, "column": 18, "label": ""}, {"line": 246, "column": 1, "label": ""}, {"line": 898, "column": 22, "label": ""}, {"line": 1324, "column": 22, "label": ""}, {"line": 1651, "column": 46, "label": ""}, {"line": 1725, "column": 40, "label": ""}]}, {"path": "RovRemote/MobileWebRemote/rov_index.html", "bookmarks": [{"line": 303, "column": 53, "label": ""}]}, {"path": "Serial/serial_port.py", "bookmarks": [{"line": 97, "column": 54, "label": ""}]}, {"path": "RovRemote/MobileWebRemote/rov_script.js", "bookmarks": [{"line": 98, "column": 46, "label": ""}, {"line": 220, "column": 42, "label": ""}, {"line": 261, "column": 76, "label": ""}, {"line": 397, "column": 48, "label": ""}, {"line": 700, "column": 24, "label": ""}, {"line": 1282, "column": 31, "label": ""}, {"line": 1374, "column": 17, "label": ""}]}, {"path": "App/test_app_mqtt.py", "bookmarks": [{"line": 66, "column": 24, "label": ""}]}, {"path": "../../../../../../rovRemote/rov_main.py", "bookmarks": [{"line": 183, "column": 25, "label": ""}, {"line": 333, "column": 56, "label": ""}]}, {"path": "RovRemote/config_manager.py", "bookmarks": [{"line": 56, "column": 32, "label": ""}, {"line": 260, "column": 57, "label": ""}]}, {"path": "debug/serial_reader_optimized.py", "bookmarks": [{"line": 122, "column": 28, "label": ""}]}, {"path": "debug/monitor_config.py", "bookmarks": [{"line": 35, "column": 44, "label": ""}, {"line": 104, "column": 16, "label": ""}]}, {"path": "debug/serial_reader_drawtable.py", "bookmarks": [{"line": 24, "column": 65, "label": ""}, {"line": 109, "column": 4, "label": ""}, {"line": 168, "column": 89, "label": ""}, {"line": 358, "column": 28, "label": ""}, {"line": 435, "column": 63, "label": ""}, {"line": 529, "column": 46, "label": ""}]}]}