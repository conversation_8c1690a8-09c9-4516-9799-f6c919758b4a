#!/usr/bin/env python3
"""
双向串口通信测试
同时监听ESP32输出并发送测试数据
"""

import serial
import time
import threading
import sys

class BidirectionalTest:
    def __init__(self, port='/dev/tty.usbserial-0001', baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=0.1)
            print(f"✅ 连接成功: {self.port} @ {self.baudrate}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def listen_thread(self):
        """监听线程"""
        print("🎧 开始监听ESP32输出...")
        
        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting)
                    
                    # 尝试解码为文本
                    try:
                        text = data.decode('utf-8', errors='ignore')
                        if text.strip():
                            print(f"📥 ESP32: {text.strip()}")
                    except:
                        print(f"📥 HEX: {data.hex().upper()}")
                        
            except Exception as e:
                print(f"❌ 监听错误: {e}")
                break
                
            time.sleep(0.05)
    
    def send_test_data(self):
        """发送测试数据"""
        print("📤 开始发送测试数据...")
        
        test_count = 0
        while self.running:
            try:
                # 每5秒发送一次测试数据
                time.sleep(5)
                
                if not self.ser:
                    break
                    
                test_count += 1
                
                # 发送简单字节
                test_byte = bytes([0xAA])
                self.ser.write(test_byte)
                print(f"📤 发送测试 #{test_count}: {test_byte.hex().upper()}")
                
                # 等待1秒后发送协议帧
                time.sleep(1)
                
                # 发送心跳包
                heartbeat = bytes([0xAA, 0xFF, 0x00, 0xFF])
                self.ser.write(heartbeat)
                print(f"📤 发送心跳包 #{test_count}: {heartbeat.hex().upper()}")
                
                # 等待1秒后发送气压计请求
                time.sleep(1)
                
                # 发送气压计数据请求
                pressure_req = bytes([0xAA, 0x02, 0x00, 0x2A])  # 包含正确的CRC
                self.ser.write(pressure_req)
                print(f"📤 发送气压计请求 #{test_count}: {pressure_req.hex().upper()}")
                
            except Exception as e:
                print(f"❌ 发送错误: {e}")
                break
    
    def run(self):
        """运行测试"""
        if not self.connect():
            return
            
        self.running = True
        
        # 启动监听线程
        listen_thread = threading.Thread(target=self.listen_thread, daemon=True)
        listen_thread.start()
        
        # 启动发送线程
        send_thread = threading.Thread(target=self.send_test_data, daemon=True)
        send_thread.start()
        
        try:
            print("🚀 双向通信测试开始")
            print("按 Ctrl+C 停止测试")
            print("=" * 50)
            
            # 主线程等待
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ 停止测试...")
        finally:
            self.running = False
            if self.ser:
                self.ser.close()
            print("🔌 串口已关闭")

def main():
    """主函数"""
    print("🔄 ESP32双向通信测试")
    print("=" * 50)
    
    # 检查串口
    import serial.tools.list_ports
    ports = serial.tools.list_ports.comports()
    
    print("🔍 可用串口:")
    for port in ports:
        if 'usbserial' in port.device or 'ttyACM' in port.device:
            print(f"  📍 {port.device} - {port.description}")
    
    print()
    
    # 运行测试
    tester = BidirectionalTest()
    tester.run()

if __name__ == "__main__":
    main()
