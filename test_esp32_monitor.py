#!/usr/bin/env python3
"""
ESP32串口监视器
监听ESP32的Serial输出（调试信息）
"""

import serial
import time
import sys

def monitor_esp32():
    """监听ESP32的Serial输出"""
    port = '/dev/tty.usbserial-0001'
    baudrate = 115200
    
    print(f"🔍 监听ESP32调试输出")
    print(f"📍 串口: {port}")
    print(f"📊 波特率: {baudrate}")
    print("按 Ctrl+C 停止监听")
    print("=" * 50)
    
    try:
        ser = serial.Serial(port, baudrate, timeout=0.1)
        print("✅ 串口连接成功")
        
        # 清空缓冲区
        ser.flushInput()
        
        print("🎧 开始监听...")
        
        while True:
            if ser.in_waiting > 0:
                try:
                    # 读取数据
                    data = ser.read(ser.in_waiting)
                    
                    # 尝试解码为文本
                    try:
                        text = data.decode('utf-8', errors='ignore')
                        if text.strip():
                            print(f"📥 {text.strip()}")
                    except:
                        # 如果不是文本，显示十六进制
                        print(f"📥 HEX: {data.hex().upper()}")
                        
                except Exception as e:
                    print(f"❌ 读取错误: {e}")
            
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 监听已停止")
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        try:
            ser.close()
            print("🔌 串口已关闭")
        except:
            pass

if __name__ == "__main__":
    monitor_esp32()
