# 可以使用延迟最低，CPU占用少

from flask import Flask, Response
import cv2

app = Flask(__name__)

global width
global height

width=640
height=480

# width=720
# height=480

# width=1280
# height=720

# width=1920
# height=1080
    
def generate_frames():
    # cap = cv2.VideoCapture(0)  # 摄像头设备索引（/dev/video1）∏
    cap = cv2.VideoCapture(1,cv2.CAP_V4L2)  # ubunbtu 摄像头设备索引（/dev/video1）∏

    # 设置分辨率（宽度 x 高度）
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)   # 宽度
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)  # 高度
    # 设置帧率（FPS）
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    # 验证设置是否成功
    actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
    actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
    actual_fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"实际分辨率: {actual_width}x{actual_height}, 实际帧率: {actual_fps}")

    while True:
        success, frame = cap.read()
        if not success:
            break
        else:
            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

@app.route('/video_feed')
def video_feed():
    return Response(generate_frames(),
                  mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/')
def index():
    return f"""
    <html>
      <head><title>香橙派摄像头监控</title></head>
      <body>
        <h1>实时视频流</h1>
        <img src="/video_feed" width="{width}" height="{height}">
      </body>
    </html>
    """

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, threaded=True)  # 允许远程访问
