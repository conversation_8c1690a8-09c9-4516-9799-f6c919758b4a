from flask import Flask, Response
import subprocess

app = Flask(__name__)

def generate_frames():
    ffmpeg_cmd = [
        'ffmpeg',
        '-f', 'v4l2',                  # 视频采集格式（Linux摄像头）
        '-video_size', '640x480',      # 分辨率
        '-framerate', '30',            # 帧率
        '-i', '/dev/video0',           # 摄像头设备
        
        # 硬件编码参数优化（关键修改点）
        '-c:v', 'h264_v4l2m2m',       # 使用香橙派硬件编码器
        '-preset', 'ultrafast',        # 最快编码速度（降低延迟）
        '-tune', 'zerolatency',        # 零延迟优化
        '-bf', '0',                    # 禁用B帧（进一步减少延迟）
        '-g', '30',                    # 关键帧间隔（不宜过小）
        
        '-f', 'image2pipe',            # 输出为图像流
        '-pix_fmt', 'bgr24',           # OpenCV兼容格式
        '-vcodec', 'mjpeg',            # 输出为MJPEG
        '-'                            # 输出到标准输出
    ]

    proc = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, bufsize=0)
    while True:
        frame = proc.stdout.read(1024 * 1024)
        if not frame:
            break
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

@app.route('/video_feed')
def video_feed():
    return Response(generate_frames(),
                   mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/')
def index():
    return """
    <html>
      <head><title>香橙派硬件编码MJPEG流</title></head>
      <body>
        <h1>实时视频流（硬件编码）</h1>
        <img src="/video_feed" width="640" height="480">
      </body>
    </html>
    """

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, threaded=True)
