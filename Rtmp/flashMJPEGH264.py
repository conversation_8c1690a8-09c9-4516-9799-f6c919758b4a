from flask import Flask, Response
import subprocess

app = Flask(__name__)

# 硬件编码捕获摄像头帧的生成器
def generate_hw_frames():
    # 使用FFmpeg硬件编码捕获摄像头（/dev/video0），输出为JPEG格式
    ffmpeg_cmd = [
        'ffmpeg',
        '-f', 'v4l2',                  # 视频采集格式（Linux摄像头）
        '-video_size', '640x480',       # 分辨率
        '-framerate', '30',             # 帧率
        '-i', '/dev/video0',            # 摄像头设备
        '-c:v', 'h264_v4l2m2m',         # 使用香橙派硬件编码器
        '-f', 'image2pipe',             # 输出为图像流
        '-pix_fmt', 'bgr24',            # OpenCV兼容的像素格式
        '-vcodec', 'mjpeg',             # 输出为MJPEG
        '-'                             # 输出到标准输出
    ]
    
    # 启动FFmpeg进程
    proc = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, bufsize=0)
    
    while True:
        # 从FFmpeg输出读取JPEG帧
        jpeg_data = proc.stdout.read(1024 * 1024)  # 读取1MB数据（足够容纳一帧）
        if not jpeg_data:
            break
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + jpeg_data + b'\r\n')

@app.route('/video_feed')
def video_feed():
    return Response(generate_hw_frames(),
                   mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/')
def index():
    return """
    <html>
      <head><title>香橙派硬件编码MJPEG流</title></head>
      <body>
        <h1>实时视频流（硬件编码）</h1>
        <img src="/video_feed" width="640" height="480">
      </body>
    </html>
    """

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, threaded=True)
