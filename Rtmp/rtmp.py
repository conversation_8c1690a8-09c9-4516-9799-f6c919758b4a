
import cv2
import subprocess

rtmp_url = "rtmp://orangepi3b.local/live/stream_key"
width, height = 640, 480
fps = 30

# FFmpeg管道配置
command = [
    'ffmpeg',
    '-y',
    '-f', 'rawvideo',
    '-vcodec', 'rawvideo',
    '-pix_fmt', 'bgr24',
    '-s', f'{width}x{height}',
    '-r', str(fps),
    '-i', '-',
    '-c:v', 'libx264',
    '-f', 'flv',
    rtmp_url
]

process = subprocess.Popen(command, stdin=subprocess.PIPE)
cap = cv2.VideoCapture(0)  # 摄像头索引

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break
    # 可在此处对frame进行处理（如画图）
    process.stdin.write(frame.tobytes())

cap.release()
process.stdin.close()
process.wait()
